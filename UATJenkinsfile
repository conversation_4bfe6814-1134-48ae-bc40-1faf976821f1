node {
      def branch = BRANCH_NAME
      def mvnHome
      def MAIN_VERSION = 1
      def SMALL_VERSION = 0
      def REGISTRY = 'dockerimg.lenztechretail.com'
      def library = 'bi-service'
      def NAMES = [
                  'enterprise-api-service'
      ]
      String[] branchArr = branch.split("/")

      if (branchArr.length > 0) {
            branch = branchArr[branchArr.length-1]
      }
      String latestVersion = "${branch}.latest"

      String tag = "${branch}.${MAIN_VERSION}.${SMALL_VERSION}.${BUILD_NUMBER}"


      stage('Preparation') { // for display purposes
            // Get the Maven tool.
            // ** NOTE: This 'M3' Maven tool must be configured
            // **       in the global configuration.

           // echo "pom中的属性信息version:${pom.version}"
           mvnHome = tool 'maven-3.5.2'
      }
      stage('Git Checkout'){
         echo "Checkout...${branch}"
         checkout scm
      }
      stage('Unit Testing'){
      	echo "Unit Testing..."
      }
      stage('Maven Build'){
          env.JAVA_HOME="${tool 'jdk8'}"
          env.PATH="${env.JAVA_HOME}/bin:${env.PATH}"
          sh 'java -version'
          print "JAVA_HOME: ${env.JAVA_HOME}"
          print "PATH: ${env.PATH}"
          sh "chmod +x mvnw"
          sh "./mvnw clean package -Dmaven.test.skip=true -Ptest"
      }
      stage('Build and Push Image'){
           //编译service服务镜像
          for (i = 0; i < NAMES.size(); i++) {
            String NAME = NAMES[i]
            sh "rm -rf ./services/${NAME}/build/docker/"
            sh "mkdir -p ./services/${NAME}/build/docker && cp -f ./Dockerfile  ./services/${NAME}/build/docker/Dockerfile && cp -f ./${NAME}/target/*[^sources].jar ./services/${NAME}/build/docker/"
            dir("./services/${NAME}/build/docker") {
                def image = docker.build("${REGISTRY}/${library}/${NAME}:${tag}")
                // image.tag("${latestVersion}")
                echo("Push image:${image.imageName()}")
                image.push()
                // image.push("${latestVersion}")
            }

          }

      }
        echo "发布..."
        def vars = input message: '请输入部署的环境参数', parameters: [
                text(defaultValue: "${tag}", description: '请输入这次部署使用的镜像版本', name: 'version'),
                choice(choices: ['test', 'uat', 'alpha', 'gamma', 'delta'], description: '请选择部署环境', name: 'NAMESPACE')
            ]
            version = vars['version']
            NAMESPACE = vars['NAMESPACE']
            print('发布服务到:' + NAMESPACE)
            if (NAMESPACE == 'test') {
                NFS = '************'
            } else if (NAMESPACE == 'uat') {
                NFS = '************'
            } else if (NAMESPACE == 'alpha') {
                NFS = '************'
            }  else if (NAMESPACE == 'delta') {
                NFS = '************'
            }  else if (NAMESPACE == 'gamma') {
                NFS = '************'
            }  else {
                throw new Exception('发布环境不存在')
            }            
            dir("./yaml") {
                for (i = 0; i < NAMES.size(); i++) {
                    String NAME = NAMES[i]
                    sh "sed -i \"s#NAMESPACE#${NAMESPACE}#g\" ${NAME}.yaml"
                    sh "sed -i \"s#NAME#${NAME}#g\" ${NAME}.yaml"
                    sh "sed -i \"s#VERSION#${REGISTRY}/${library}/${NAME}:${version}#g\" ${NAME}.yaml"
                    sh "sed -i \"s#NFSSERVER#${NFS}#g\" ${NAME}.yaml"
                    sh "kubectl apply -f ${NAME}.yaml"
            }
		}
}