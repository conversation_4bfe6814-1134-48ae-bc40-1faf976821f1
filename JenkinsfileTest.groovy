/**  =========================  标准配置  =============================  **/

import com.trax.devops.publish.*
import hudson.model.*

@Library('trax-cicd-lib') _

//发布的镜像
def imageMap = [:]

def group = 'dockerimg.lenztechretail.com'
def nameSpace = 'tope'

//maven版本,用来构建的脚本，确定 snapshot / release
def mavenVersion = "snapshot"
//版本号
String tag = "${BUILD_TAG}"
//环境列表
def deployEnvList = ['test']


/**  =========================  应用自定义  =============================  **/

//应用前缀
def appPrefix = "bi"
//项目名
def projectName = "${appPrefix}-service"
//服务所在目录，会根据该目录结合appName寻找jar包进行docker镜像构建
def serviceDir = ""
//发布的应用
def appNames = ["enterprise-api-service"]
//git仓库地址
def gitUrl = "************************:rd-java/${projectName}.git"
//通知版本号的dataID
def nacosDataId = "${projectName}.yml"

/**  =========================  发布环境（仅包含发布时使用）  =============================  **/


pipeline {
    agent any

    stages {

        stage('Git Checkout') {
            steps {
                script {
                    echo "Checkout...${BRANCH_NAME}"
                    checkout scm
                }
            }
        }

        stage('Branch Check') {
            steps {
                script {
                    checkTag(BRANCH_NAME)
                }
            }
        }

        stage('Input Env') {
            steps {
                timeout(time: 1, unit: 'MINUTES') {
                    script {
                        env.DEPLOY_ENV = input message: '选择部署的环境', ok: 'deploy',
                                parameters: [choice(name: 'DEPLOY_ENV', choices: ['test', 'uat','prod' ], description: '选择部署环境')]
                        println("build ${env.DEPLOY_ENV} env")
                        switch ("${env.DEPLOY_ENV}") {
                            case 'prod':
                                deployEnvList = ['prod']
                                break;
                            case 'uat':
                                deployEnvList = ['uat']
                                break;

                            case 'test':
                                deployEnvList = ['test']
                                break;
                            default:
                                println('error env')
                        }
                    }
                }
            }
        }

        stage('Maven Build') {
            steps {
                script {
                    maven(mavenVersion)
                }
            }
        }

        stage('Build and Push Image') {
            steps {
                script {
                    customDocker.build(group, nameSpace, tag, serviceDir, appNames, imageMap)
                }
            }
        }

        stage("Publish Version") {
            steps {
                script {
                    PublishImage.noticeNacos(nacosDataId, imageMap, deployEnvList)
                    Notice.noticeK8sNacos(nacosDataId, imageMap, deployEnvList)
                }
            }
        }

    }
}
