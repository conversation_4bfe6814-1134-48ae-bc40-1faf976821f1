package com.lenztech.bi.enterprise.utils;

import com.lenztech.bi.enterprise.comon.Constant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * @Description: Controller工具类（获取请求路径 &&）
 * @Author: zhangjie
 * @Date: 3/5/20 AM10:15
 */
public class ControllerUtil {

    /**
     * 获取RequestMapping注解的路径
     *
     * @param annotations 注解
     * @return
     */
    public static String lookupRequestMappingPath(Annotation[] annotations) {
        try {
            for (Annotation ann : annotations) {
                Class annType = ann.annotationType();
                // 如果annType与RequestMapping类型一致 或 RequestMapping注释存在于annType上
                if (annType.isAssignableFrom(RequestMapping.class) || annType.isAnnotationPresent(RequestMapping.class)) {
                    String[] paths = (String[]) AnnotationUtils.getValue(ann);
                    return paths.length > 0 ? paths[0] : Constant.VALUE_BLANK;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return Constant.VALUE_BLANK;
    }

    /**
     * 获取RequestMapping注解的路径
     *
     * @param annotations 注解
     * @return
     */
    public static String lookupFeignClientPath(Annotation[] annotations) {
        try {
            for (Annotation ann : annotations) {
                Class annType = ann.annotationType();
                // 如果annType与FeignClient类型一致 或FeignClient注释存在于annType上
                if (annType.isAssignableFrom(FeignClient.class) || annType.isAnnotationPresent(FeignClient.class)) {
                    String[] paths = (String[]) AnnotationUtils.getValue(ann);
                    return paths.length > 0 ? paths[0] : Constant.VALUE_BLANK;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return Constant.VALUE_BLANK;
    }

    /**
     * 获取请求Controller的路径
     * @param controllerBeanClass Controller类class
     * @param targetMethod Controller类方法
     * @return
     */
    public static String lookupRequestPath(Class controllerBeanClass, Method targetMethod){
        String controlerPath = ControllerUtil.lookupRequestMappingPath(controllerBeanClass.getAnnotations());
        controlerPath = controlerPath != null ? controlerPath : Constant.VALUE_BLANK;

        String methodPath = ControllerUtil.lookupRequestMappingPath(AnnotationUtils.getAnnotations(targetMethod));
        methodPath = methodPath != null ? methodPath : Constant.VALUE_BLANK;

        return controlerPath + methodPath;
    }

    /**
     * 获取请求Controller的路径
     * @param controllerBeanClass Controller类class
     * @param targetMethod Controller类方法
     * @return
     */
    public static String lookupFeignRequestPath(Class controllerBeanClass, Method targetMethod){
        String controlerPath = ControllerUtil.lookupFeignClientPath(controllerBeanClass.getAnnotations());
        controlerPath = controlerPath != null ? controlerPath : Constant.VALUE_BLANK;

        String methodPath = ControllerUtil.lookupRequestMappingPath(AnnotationUtils.getAnnotations(targetMethod));
        methodPath = methodPath != null ? methodPath : Constant.VALUE_BLANK;

        return controlerPath + methodPath;
    }
}
