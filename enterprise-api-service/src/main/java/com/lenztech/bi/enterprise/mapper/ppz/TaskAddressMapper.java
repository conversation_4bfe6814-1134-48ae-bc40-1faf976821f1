package com.lenztech.bi.enterprise.mapper.ppz;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.ppz.TaskAddressDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 任务地址 Mapper
 */
@Mapper
@DS("ppz")
public interface TaskAddressMapper extends BaseMapper<TaskAddressDO> {

    @Select("SELECT DISTINCT t1.* " +
            "FROM business_task_address t1 " +
            "JOIN business_task_address_audit_record t2 ON t2.task_address_id = t1.task_address_id " +
            "WHERE t1.task_id = #{taskId} " +
            "AND t2.update_time >= #{startTime} " +
            "AND t2.update_time < #{endTime} " +
            "AND t2.audit_round = 1 " +
            "AND t2.status = 4 " +
            "AND t2.audit_result_value = 2 ")
    java.util.List<TaskAddressDO> selectYesterdayAuditedTaskAddress(
        @Param("taskId") String taskId,
        @Param("startTime") String startTime,
        @Param("endTime") String endTime
    );
}