package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.entity.PgOptimusRealogramSequence;
import com.lenztech.bi.enterprise.mapper.PgOptimusRealogramSequenceMapper;
import com.lenztech.bi.enterprise.service.IPgOptimusRealogramSequenceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 生成唯一批次表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class PgOptimusRealogramSequenceServiceImpl extends ServiceImpl<PgOptimusRealogramSequenceMapper, PgOptimusRealogramSequence> implements IPgOptimusRealogramSequenceService {

}
