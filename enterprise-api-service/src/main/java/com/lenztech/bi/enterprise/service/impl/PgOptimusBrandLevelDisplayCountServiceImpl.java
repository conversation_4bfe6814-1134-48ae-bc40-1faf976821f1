package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.entity.PgOptimusBrandLevelDisplayCount;
import com.lenztech.bi.enterprise.mapper.PgOptimusBrandLevelDisplayCountMapper;
import com.lenztech.bi.enterprise.service.IPgOptimusBrandLevelDisplayCountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Brand级别二陈数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class PgOptimusBrandLevelDisplayCountServiceImpl extends ServiceImpl<PgOptimusBrandLevelDisplayCountMapper, PgOptimusBrandLevelDisplayCount> implements IPgOptimusBrandLevelDisplayCountService {

}
