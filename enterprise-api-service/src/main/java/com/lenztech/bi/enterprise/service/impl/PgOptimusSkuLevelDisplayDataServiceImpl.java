package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.entity.PgOptimusSkuLevelDisplayData;
import com.lenztech.bi.enterprise.mapper.PgOptimusSkuLevelDisplayDataMapper;
import com.lenztech.bi.enterprise.service.IPgOptimusSkuLevelDisplayDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * SKU级别二陈数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class PgOptimusSkuLevelDisplayDataServiceImpl extends ServiceImpl<PgOptimusSkuLevelDisplayDataMapper, PgOptimusSkuLevelDisplayData> implements IPgOptimusSkuLevelDisplayDataService {

}
