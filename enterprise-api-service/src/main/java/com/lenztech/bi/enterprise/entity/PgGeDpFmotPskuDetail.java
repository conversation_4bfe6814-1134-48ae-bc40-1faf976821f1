package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * 宝洁GE云看店明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public class PgGeDpFmotPskuDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String month;

    private String date;

    private String addressId;

    private String distributorId;

    private String distributorStoreId;

    private String storeSeqCode;

    @TableField("is_dcp_Flag")
    private String isDcpFlag;

    private String category;

    private String pskuActual;

    private String skuName;

    private String skuBarcode;

    private String skuDistribution;

    private String storeType;

    private String subBannerName;

    private String categoryCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
    public String getAddressId() {
        return addressId;
    }

    public void setAddressId(String addressId) {
        this.addressId = addressId;
    }
    public String getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(String distributorId) {
        this.distributorId = distributorId;
    }
    public String getDistributorStoreId() {
        return distributorStoreId;
    }

    public void setDistributorStoreId(String distributorStoreId) {
        this.distributorStoreId = distributorStoreId;
    }
    public String getStoreSeqCode() {
        return storeSeqCode;
    }

    public void setStoreSeqCode(String storeSeqCode) {
        this.storeSeqCode = storeSeqCode;
    }
    public String getIsDcpFlag() {
        return isDcpFlag;
    }

    public void setIsDcpFlag(String isDcpFlag) {
        this.isDcpFlag = isDcpFlag;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getPskuActual() {
        return pskuActual;
    }

    public void setPskuActual(String pskuActual) {
        this.pskuActual = pskuActual;
    }
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
    public String getSkuBarcode() {
        return skuBarcode;
    }

    public void setSkuBarcode(String skuBarcode) {
        this.skuBarcode = skuBarcode;
    }
    public String getSkuDistribution() {
        return skuDistribution;
    }

    public void setSkuDistribution(String skuDistribution) {
        this.skuDistribution = skuDistribution;
    }
    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }
    public String getSubBannerName() {
        return subBannerName;
    }

    public void setSubBannerName(String subBannerName) {
        this.subBannerName = subBannerName;
    }
    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PgGeDpFmotPskuDetail{" +
            "id=" + id +
            ", month=" + month +
            ", date=" + date +
            ", addressId=" + addressId +
            ", distributorId=" + distributorId +
            ", distributorStoreId=" + distributorStoreId +
            ", storeSeqCode=" + storeSeqCode +
            ", isDcpFlag=" + isDcpFlag +
            ", category=" + category +
            ", pskuActual=" + pskuActual +
            ", skuName=" + skuName +
            ", skuBarcode=" + skuBarcode +
            ", skuDistribution=" + skuDistribution +
            ", storeType=" + storeType +
            ", subBannerName=" + subBannerName +
            ", categoryCode=" + categoryCode +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
