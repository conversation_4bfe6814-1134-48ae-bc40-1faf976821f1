//package com.lenztech.bi.enterprise.service;
//
//import com.lenztech.bi.enterprise.dto.CommonListRet;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.entity.BiReportBrand;
//import com.lenztech.bi.enterprise.entity.BiReportBrandExample;
//import com.lenztech.bi.enterprise.entity.BiReportCategory;
//import com.lenztech.bi.enterprise.entity.BiReportCategoryExample;
//import com.lenztech.bi.enterprise.mapper.BiReportCategoryMapper;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.HashMap;
//import java.util.List;
//
///**
// * @Description: bi 品牌
// * @Author: zhangjie
// * @Date: 3/26/20 PM1:59
// */
//@Service
//public class BiCategoryService {
//
//    @Autowired
//    private BiReportCategoryMapper biReportCategoryMapper;
//
//
//    /**
//     * 品牌id与品牌信息对应关系
//     * @param taskId
//     * @return
//     */
//    public HashMap<Integer, BiReportCategory> categoryIdHashMap(String taskId){
//        HashMap<Integer,BiReportCategory> categoryHashMap = new HashMap<>();
//
//        List<BiReportCategory> categoryList = getCategoryList(taskId);
//        if (CollectionUtils.isNotEmpty(categoryList)){
//            for (BiReportCategory biReportCategory: categoryList){
//                categoryHashMap.put(biReportCategory.getId(), biReportCategory);
//            }
//        }
//        return categoryHashMap;
//    }
//
//    /**
//     * 获取品牌列表
//     * @param taskId
//     * @return
//     */
//    public List<BiReportCategory> getCategoryList(String taskId){
//        BiReportCategoryExample example = new BiReportCategoryExample();
//        example.createCriteria().andTaskIdEqualTo(taskId);
//
//        List<BiReportCategory> categoryList = biReportCategoryMapper.selectByExample(example);
//
//        return categoryList;
//    }
//}
