package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lenztech.bi.enterprise.dto.pg.PgRtirApiRequestDTO;
import com.lenztech.bi.enterprise.dto.pg.PgRtirApiResponseDTO;
import com.lenztech.bi.enterprise.dto.pg.PgRtirApiRecordDTO;
import com.lenztech.bi.enterprise.dto.pg.PgRtirMdDTO;
import com.lenztech.bi.enterprise.entity.PgRtirMd;
import com.lenztech.bi.enterprise.mapper.PgRtirMdMapper;
import com.lenztech.bi.enterprise.service.IPgRtirMdService;
import com.lenztech.bi.enterprise.utils.*;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PG RTIR 业务服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class PgRtirService {

    @Autowired
    private PgRtirMdMapper pgRtirMdMapper;

    @Autowired
    private IPgRtirMdService pgRtirMdService;

    @Autowired
    private SnowFlakeFactory snowFlakeFactory;

    public static final String API_KEY = "trax-image-recognition-91582";

    public static final String SECRET = "sTGxmiKbmqXV";

    public static final String BASE_URL = "https://api-b2b-qa.cn-pgcloud.com/ghr-image-recognition/v1/gh/optimus/fpc/gtin/data/page";

    public static final String SUB_SCRIPTION_KEY = "c30543816498403b937e7d0e35613bb0";

//    public static final String API_KEY = "ghr-golden-hand-63314";
//
//    public static final String SECRET = "NvDNacp7wDcg";
//
//    public static final String BASE_URL = "https://api-b2b-qa.cn-pgcloud.com/ghr-image-recognition/v1/gh/optimus/fpc/gtin/data/page";
//
//    public static final String SUB_SCRIPTION_KEY = "6a221b31fbbf4deb92583a86c349e8cc";

    /**
     * 分页查询 RTIR 主数据
     * @param gtinCode GTIN编码
     * @param categoryCode 品类编码
     * @param categoryCn 品类名称
     * @param brandCode 品牌编码
     * @param brandCn 品牌名称
     * @param productNameCn 商品名称
     * @param isPgProduct 是否本品
     * @param modeling 是否建模
     * @param isPosm 是否POSM
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public IPage<PgRtirMdDTO> getRtirMdPageList(String gtinCode, String categoryCode, String categoryCn,
                                                String brandCode, String brandCn, String productNameCn,
                                                String isPgProduct, String modeling, String isPosm,
                                                Integer pageNum, Integer pageSize) {
        
        // 设置默认分页参数
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }

        // 构建查询条件
        LambdaQueryWrapper<PgRtirMd> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(gtinCode)) {
            queryWrapper.like(PgRtirMd::getGtinCode, gtinCode);
        }
        if (StringUtils.isNotBlank(categoryCode)) {
            queryWrapper.eq(PgRtirMd::getCategoryCode, categoryCode);
        }
        if (StringUtils.isNotBlank(categoryCn)) {
            queryWrapper.like(PgRtirMd::getCategoryCn, categoryCn);
        }
        if (StringUtils.isNotBlank(brandCode)) {
            queryWrapper.eq(PgRtirMd::getBrandCode, brandCode);
        }
        if (StringUtils.isNotBlank(brandCn)) {
            queryWrapper.like(PgRtirMd::getBrandCn, brandCn);
        }
        if (StringUtils.isNotBlank(productNameCn)) {
            queryWrapper.like(PgRtirMd::getProductNameCn, productNameCn);
        }
        if (StringUtils.isNotBlank(isPgProduct)) {
            queryWrapper.eq(PgRtirMd::getIsPgProduct, isPgProduct);
        }
        if (StringUtils.isNotBlank(modeling)) {
            queryWrapper.eq(PgRtirMd::getModeling, modeling);
        }
        if (StringUtils.isNotBlank(isPosm)) {
            queryWrapper.eq(PgRtirMd::getIsPosm, isPosm);
        }
        queryWrapper.eq(PgRtirMd::getIsPgProduct, "0");

        // 按 GTIN 编码排序
        queryWrapper.orderByAsc(PgRtirMd::getGtinCode);

        // 执行分页查询
        Page<PgRtirMd> page = new Page<>(pageNum, pageSize);
        IPage<PgRtirMd> resultPage = pgRtirMdService.page(page, queryWrapper);

        // 转换为DTO对象
        IPage<PgRtirMdDTO> dtoPage = resultPage.convert(entity -> {
            PgRtirMdDTO dto = new PgRtirMdDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });

        return dtoPage;
    }

    /**
     * 根据GTIN编码查询单条记录
     * @param gtinCode GTIN编码
     * @return RTIR主数据DTO
     */
    public PgRtirMdDTO getRtirMdByGtinCode(String gtinCode) {
        if (StringUtils.isBlank(gtinCode)) {
            return null;
        }

        PgRtirMd entity = pgRtirMdService.getById(gtinCode);
        if (entity == null) {
            return null;
        }

        PgRtirMdDTO dto = new PgRtirMdDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 获取所有品类列表
     * @return 品类列表
     */
    public List<PgRtirMdDTO> getCategoryList() {
        LambdaQueryWrapper<PgRtirMd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PgRtirMd::getCategoryCode, PgRtirMd::getCategoryCn)
                   .groupBy(PgRtirMd::getCategoryCode, PgRtirMd::getCategoryCn)
                   .orderByAsc(PgRtirMd::getCategoryCode);

        List<PgRtirMd> entityList = pgRtirMdService.list(queryWrapper);
        List<PgRtirMdDTO> dtoList = new ArrayList<>();
        
        for (PgRtirMd entity : entityList) {
            PgRtirMdDTO dto = new PgRtirMdDTO();
            dto.setCategoryCode(entity.getCategoryCode());
            dto.setCategoryCn(entity.getCategoryCn());
            dtoList.add(dto);
        }
        
        return dtoList;
    }

    /**
     * 获取所有品牌列表
     * @return 品牌列表
     */
    public List<PgRtirMdDTO> getBrandList() {
        LambdaQueryWrapper<PgRtirMd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PgRtirMd::getBrandCode, PgRtirMd::getBrandCn)
                   .groupBy(PgRtirMd::getBrandCode, PgRtirMd::getBrandCn)
                   .orderByAsc(PgRtirMd::getBrandCode);

        List<PgRtirMd> entityList = pgRtirMdService.list(queryWrapper);
        List<PgRtirMdDTO> dtoList = new ArrayList<>();
        
        for (PgRtirMd entity : entityList) {
            PgRtirMdDTO dto = new PgRtirMdDTO();
            dto.setBrandCode(entity.getBrandCode());
            dto.setBrandCn(entity.getBrandCn());
            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 调用第三方API获取数据并保存到数据库
     * @param requestDTO 请求参数
     * @return 同步结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String syncDataFromThirdPartyApi(PgRtirApiRequestDTO requestDTO) {
        try {
            log.info("开始调用第三方API获取数据，请求参数：{}", JsonUtil.toJsonString(requestDTO));

            // 调用第三方API（带鉴权）
            String responseJson = callThirdPartyApiWithAuth(requestDTO);

            if (StringUtils.isBlank(responseJson)) {
                log.error("第三方API返回空响应");
                return "调用第三方API失败：响应为空";
            }

            log.info("第三方API响应：{}", responseJson);

            // 解析响应数据
            PgRtirApiResponseDTO responseDTO = JsonUtil.jsonToPojo(responseJson, PgRtirApiResponseDTO.class);
            if (responseDTO == null || !Boolean.TRUE.equals(responseDTO.getSuccess()) || responseDTO.getCode() != 200) {
                log.error("第三方API调用失败，响应：{}", responseJson);
                return "调用第三方API失败：" + (responseDTO != null ? responseDTO.getMsg() : "解析响应失败");
            }

            PgRtirApiResponseDTO.PgRtirApiDataDTO data = responseDTO.getData();
            if (data == null || data.getRecords() == null || data.getRecords().isEmpty()) {
                log.warn("第三方API返回数据为空");
                return "同步完成，但没有获取到数据";
            }

            // 转换并保存数据
            List<PgRtirMd> entityList = convertApiRecordsToEntities(data.getRecords());
            int savedCount = batchSaveOrUpdateData(entityList);

            String result = String.format("同步完成，共获取%d条数据，成功保存%d条", data.getRecords().size(), savedCount);
            log.info(result);
            return result;

        } catch (Exception e) {
            log.error("调用第三方API同步数据异常", e);
            throw new RuntimeException("同步数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 将API记录转换为实体对象
     * @param apiRecords API记录列表
     * @return 实体对象列表
     */
    private List<PgRtirMd> convertApiRecordsToEntities(List<PgRtirApiRecordDTO> apiRecords) {
        List<PgRtirMd> entityList = new ArrayList<>();
        Date now = new Date();

        for (PgRtirApiRecordDTO record : apiRecords) {
            PgRtirMd entity = new PgRtirMd();

            // 基本字段映射
            entity.setGtinCode(record.getGtinCode());
            entity.setFpcCode(record.getFpcCode());
            entity.setItemStatus(record.getItemStatus());
            entity.setYear(record.getYear() != null ? record.getYear().toString() : null);
            entity.setSosDate(record.getSosDate());
            entity.setCategoryCode(record.getCategoryCode());
            entity.setCategoryCn(record.getCategoryCn());
            entity.setProductFormCode(record.getProductFormCode());
            entity.setProductFormCn(record.getProductFormCn());
            entity.setBrandCode(record.getBrandCode());
            entity.setBrandCn(record.getBrandCn());
            entity.setVariantCode(record.getVariantCode());
            entity.setVariantCn(record.getVariantCn());
            entity.setProductNameCn(record.getProductNameCn());

            // 尺寸信息
            entity.setLength(record.getLength() != null ? record.getLength().toString() : null);
            entity.setWidth(record.getWidth() != null ? record.getWidth().toString() : null);
            entity.setHeight(record.getHeight() != null ? record.getHeight().toString() : null);

            // 图片URL列表转JSON字符串
//            if (record.getFpcImageUrls() != null && !record.getFpcImageUrls().isEmpty()) {
//                entity.setFpcImageUrls(JsonUtil.toJsonString(record.getFpcImageUrls()));
//            }

            // 设置默认值
            entity.setIsPgProduct("0"); // 默认为竞品
            entity.setModeling("0"); // 默认未建模
            entity.setIsPosm("0"); // 默认不是POSM
            entity.setIsTraxCreate("0"); // 默认不是Trax新建

            // 设置创建和更新时间
            entity.setCreateTime(now);
            entity.setUpdateTime(now);

            entityList.add(entity);
        }

        return entityList;
    }

    /**
     * 批量保存或更新数据
     * @param entityList 实体列表
     * @return 成功保存的数量
     */
    private int batchSaveOrUpdateData(List<PgRtirMd> entityList) {
        int successCount = 0;

        for (PgRtirMd entity : entityList) {
            try {
                // 检查是否已存在
                PgRtirMd existingEntity = pgRtirMdService.getById(entity.getGtinCode());
                if (existingEntity != null) {
                    // 更新现有记录
                    entity.setCreateTime(existingEntity.getCreateTime()); // 保持原创建时间
                    entity.setUpdateTime(new Date()); // 更新修改时间
                    pgRtirMdService.updateById(entity);
                } else {
                    // 新增记录
                    pgRtirMdService.save(entity);
                }
                successCount++;
            } catch (Exception e) {
                log.error("保存数据失败，GTIN编码：{}，错误：{}", entity.getGtinCode(), e.getMessage());
            }
        }

        return successCount;
    }

    /**
     * 定时同步所有第三方API数据（自动分页获取）
     * @return 同步结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String syncAllDataFromThirdPartyApi() {
        try {
            log.info("开始定时同步第三方API所有数据");

            int totalRecords = 0;
            int totalSaved = 0;
            int currentPage = 1;
            int pageSize = 50; // 每页50条，避免单次请求数据过多
            boolean hasMoreData = true;

            while (hasMoreData) {
                log.info("正在获取第{}页数据，每页{}条", currentPage, pageSize);

                // 构建请求参数
                PgRtirApiRequestDTO requestDTO = new PgRtirApiRequestDTO();
                requestDTO.setCurrent(currentPage);
                requestDTO.setPageSize(pageSize);
                requestDTO.setProductNameCn("");
                requestDTO.setBrandCode("");
                requestDTO.setCategoryCode("");
                // 不设置筛选条件，获取所有数据

                // 调用第三方API（带鉴权）
                String responseJson = callThirdPartyApiWithAuth(requestDTO);
                log.info("responseJson: {}", responseJson);
                hasMoreData = false;

//                if (StringUtils.isBlank(responseJson)) {
//                    log.error("第三方API返回空响应，页码：{}", currentPage);
//                    break;
//                }
//
//                // 解析响应数据
//                PgRtirApiResponseDTO responseDTO = JsonUtil.jsonToPojo(responseJson, PgRtirApiResponseDTO.class);
//                if (responseDTO == null || !Boolean.TRUE.equals(responseDTO.getSuccess()) || responseDTO.getCode() != 200) {
//                    log.error("第三方API调用失败，页码：{}，响应：{}", currentPage, responseJson);
//                    break;
//                }
//
//                PgRtirApiResponseDTO.PgRtirApiDataDTO data = responseDTO.getData();
//                if (data == null || data.getRecords() == null) {
//                    log.warn("第三方API返回数据为空，页码：{}", currentPage);
//                    break;
//                }
//
//                // 如果当前页没有数据，说明已经获取完所有数据
//                if (data.getRecords().isEmpty()) {
//                    log.info("第{}页没有数据，数据获取完毕", currentPage);
//                    hasMoreData = false;
//                    break;
//                }
//
//                // 转换并保存数据
//                List<PgRtirMd> entityList = convertApiRecordsToEntities(data.getRecords());
//                int savedCount = batchSaveOrUpdateData(entityList);
//
//                totalRecords += data.getRecords().size();
//                totalSaved += savedCount;
//
//                log.info("第{}页处理完成，获取{}条数据，成功保存{}条", currentPage, data.getRecords().size(), savedCount);
//
//                // 判断是否还有更多数据
//                // 如果当前页数据量小于每页大小，说明是最后一页
//                if (data.getRecords().size() < pageSize) {
//                    hasMoreData = false;
//                    log.info("当前页数据量({})小于每页大小({})，数据获取完毕", data.getRecords().size(), pageSize);
//                } else {
//                    // 继续获取下一页
//                    currentPage++;
//
//                    // 添加短暂延迟，避免对第三方API造成压力
//                    try {
//                        Thread.sleep(1000); // 延迟1秒
//                    } catch (InterruptedException e) {
//                        Thread.currentThread().interrupt();
//                        log.warn("线程被中断", e);
//                    }
//                }
            }

            String result = String.format("定时同步完成，共处理%d页数据，获取%d条记录，成功保存%d条",
                    currentPage, totalRecords, totalSaved);
            log.info(result);
            return result;

        } catch (Exception e) {
            log.error("定时同步第三方API数据异常", e);
            throw new RuntimeException("定时同步数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 调用第三方API（带鉴权）
     * @param requestDTO 请求参数
     * @return API响应JSON字符串
     */
    private String callThirdPartyApiWithAuth(PgRtirApiRequestDTO requestDTO) {
        try {
            // 鉴权参数
            String timestamp = DateUtil.convert2String(new Date(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat());
            String apiKey = API_KEY;
            String nonceStr = CheckDigestTool.getUUID();
            String secret = SECRET;

            // 构建签名参数
            Map<String, String> params = new HashMap<>();
            String requestBody = JsonUtil.toJsonString(requestDTO);
            params.put("api_key", apiKey);
            params.put("nonce_str", nonceStr);
            params.put("timestamp", timestamp);
            params.put("body", requestBody);

            // 生成签名
            String sign = ApimSignUtil.sign(params, secret);
            log.info("API鉴权签名生成成功，sign={}", sign);

            // 构建请求URL（带鉴权参数）
            String baseUrl = BASE_URL;
            String encodedTimestamp = timestamp.replace(" ", "%20");
            String apiUrl = String.format("%s?api_key=%s&nonce_str=%s&timestamp=%s&sign=%s",
                    baseUrl, apiKey, nonceStr, encodedTimestamp, sign);

            log.info("调用第三方API，URL={}", apiUrl);
            log.info("请求体={}", requestBody);

            // 使用自定义的HTTP客户端发送带header的POST请求
            String responseJson = postJsonWithHeaders(apiUrl, requestBody);

            log.info("第三方API响应={}", responseJson);
            return responseJson;

        } catch (Exception e) {
            log.error("调用第三方API异常", e);
            throw new RuntimeException("调用第三方API失败：" + e.getMessage(), e);
        }
    }

    /**
     * 发送带请求头的POST JSON请求
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @return 响应字符串
     */
    private String postJsonWithHeaders(String url, String jsonBody) {
        try {
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(
                    okhttp3.MediaType.parse("application/json; charset=utf-8"), jsonBody);

            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .addHeader("Ocp-Apim-Subscription-Key", SUB_SCRIPTION_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();

            try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("HTTP请求失败，状态码：{}", response.code());
                    throw new RuntimeException("HTTP请求失败，状态码：" + response.code());
                }
                return response.body().string();
            }
        } catch (Exception e) {
            log.error("发送HTTP请求异常", e);
            throw new RuntimeException("发送HTTP请求失败：" + e.getMessage(), e);
        }
    }
}
