//package com.lenztech.bi.enterprise.service.impl;
//
//import com.google.common.collect.ImmutableMap;
//import com.google.common.collect.Lists;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.loreal.OuLaiYaSkuListDTO;
//import com.lenztech.bi.enterprise.entity.OuLaiYaSkuEntity;
//import com.lenztech.bi.enterprise.mapper.bienterprise.OuLaiYaSkuMapper;
//import com.lenztech.bi.enterprise.service.LorealEnterpriseService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @version V1.0
// * @date 2019-10-23 16:32
// * @since JDK 1.8
// */
//@Service
//@Slf4j
//public class LorealEnterpriseServiceImpl implements LorealEnterpriseService {
//
//    private OuLaiYaSkuMapper ouLaiYaSkuMapper;
//
//    @Autowired
//    public LorealEnterpriseServiceImpl(OuLaiYaSkuMapper ouLaiYaSkuMapper) {
//        this.ouLaiYaSkuMapper = ouLaiYaSkuMapper;
//    }
//
//    @Override
//    public ResponseData<OuLaiYaSkuListDTO> listOuLaiYaSku(String responseId) {
//        OuLaiYaSkuListDTO ouLaiYaSkuListDTO = new OuLaiYaSkuListDTO();
//        List<OuLaiYaSkuEntity> ouLaiYaSkuEntities = ouLaiYaSkuMapper.listOuLaiYaSku(ImmutableMap.<String,Object>builder()
//                .put("responseId",responseId)
//                .build());
//        ouLaiYaSkuListDTO.setOuLaiYaSkuEntities(CollectionUtils.isNotEmpty(ouLaiYaSkuEntities) ? ouLaiYaSkuEntities : Lists.newArrayList());
//        return ResponseData.success(ouLaiYaSkuListDTO);
//    }
//
//}
