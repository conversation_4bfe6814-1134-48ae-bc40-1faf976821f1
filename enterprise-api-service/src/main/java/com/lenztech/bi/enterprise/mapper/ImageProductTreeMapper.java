package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.DisplayProduct;
import com.lenztech.bi.enterprise.entity.ImageProductTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 产品SKU对应关系
 * @Author: <PERSON><PERSON><PERSON>e
 * @Date: 27/12/18 下午5:06
 */
@DS("task")
public interface ImageProductTreeMapper {

//    /**
//     * 查询图像产品信息
//     *
//     * @param responseId 答卷Id
//     * @return List<ImageProductTree>
//     */
//    List<ImageProductTree> getImageProductTreeByResponseId(String responseId);
//
//	/**
//	 * 通过父节点id获取子节点id集合
//	 * @param parentId
//	 * @return
//	 */
//	List<String> getImageProductTreeIdListByParentId(@Param("parentId") String parentId);
//
//
//	/**
//	 * 通过任务Id获取设置的识别目标sku信息
//	 * @param taskId
//	 * @return List<ImageProductTree>
//	 */
//	List<ImageProductTree> getImageProductTreeListByTaskId(@Param("taskId") String taskId);

//	/**
//	 * 根据产品id查询显示产品名称查询
//	 * @param list
//	 * @return
//	 */
//	List<DisplayProduct> getDisplayProductByProductIdList(List<Integer> list);


	List<ImageProductTree> getImageProductTreeByProductIdList(@Param("list") List<Integer> productIdList);
}
