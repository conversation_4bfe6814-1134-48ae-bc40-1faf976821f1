package com.lenztech.bi.enterprise.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lenztech.bi.enterprise.dto.cloudPatrolShop.*;
import com.lenztech.bi.enterprise.service.CloudPatrolShopService;
import com.lenztech.bi.enterprise.utils.DateUtil;
import com.lenztech.bi.enterprise.utils.OkHttpUtil;
import com.trax.lenz.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CloudPatrolShopServiceImpl implements CloudPatrolShopService {

    @Value("${cloudPatrolShop.storeInfoUrl}")
    private String storeInfoUrl;
    @Value("${cloudPatrolShop.storeImageUrl}")
    private String storeImageUrl;
    @Value("${cloudPatrolShop.storeIndexUrl}")
    private String storeIndexUrl;

    @Override
    public List<StoreListResp> getStoreList(String provinceName, String cityName, String address) {
        Map<String, String> params = new HashMap<>();
        params.put("provinceName", provinceName);
        params.put("cityName", cityName);
        params.put("address", address);

        return remoteGetData(storeInfoUrl, params, StoreListResp.class);
    }

    @Override
    public List<StoreImageListResp> getStoreImageList(String storeId) {
        Map<String, String> params = new HashMap<>();
        params.put("storeId", storeId);

        List<StoreImageListResp> dataList = remoteGetData(storeImageUrl, params, StoreImageListResp.class);

        // 按照questionId&采集时间 groupBy
        Map<String, List<StoreImageListResp>> questionIdMap = dataList.stream().collect(Collectors.groupingBy(resp -> resp.getQuestionId() + DateUtil.getFormatDate(resp.getCreateTime(), DateUtil.DTFormat.yyyyMMdd.toString())));

        List<StoreImageListResp> groupByList = new ArrayList<>();

        questionIdMap.forEach((key, respList) -> {
            List<StoreImageResp> allImageList = new ArrayList<>();
            respList.forEach(resp -> allImageList.addAll(resp.getImageList()));

            StoreImageListResp resp = respList.get(0);
            StoreImageListResp groupByResp = new StoreImageListResp()
                    .setQuestionId(resp.getQuestionId())
                    .setTitle(resp.getTitle())
                    .setCreateTime(resp.getCreateTime())
                    .setImageList(allImageList);
            groupByList.add(groupByResp);
        });

        return groupByList.stream().sorted(Comparator.comparing(StoreImageListResp::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());
    }

    @Override
    public List<StoreIndicatorResp> getStoreIndicator(String storeId) {
        Map<String, String> params = new HashMap<>();
        params.put("storeId", storeId);

        List<StoreIndicatorResp> dataList = remoteGetData(storeIndexUrl, params, StoreIndicatorResp.class);

        return dataList.stream().sorted(Comparator.comparing(StoreIndicatorResp::getDate)).collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private <T> List<T> remoteGetData(String url, Map<String, String> params, Class<T> clazz) {
        String bodyString = OkHttpUtil.get(url, params);

        if (StringUtils.isBlank(bodyString)) {
            return new ArrayList<>();
        }

        BiResultResp<JSONArray> biResultResp = JSONObject.parseObject(bodyString, BiResultResp.class);

        if (!biResultResp.isOk()) {
            log.error("request failed，code：{}，msg：{}", biResultResp.getCode(), biResultResp.getMsg());
            throw new BusinessException("request failed，code：" + biResultResp.getCode() + "，msg：" + biResultResp.getMsg());
        }

        if (null == biResultResp.getDatas() || biResultResp.getDatas().isEmpty()) {
            return new ArrayList<>();
        }

        return JSONObject.parseArray(biResultResp.getDatas().toJSONString(), clazz);
    }
}
