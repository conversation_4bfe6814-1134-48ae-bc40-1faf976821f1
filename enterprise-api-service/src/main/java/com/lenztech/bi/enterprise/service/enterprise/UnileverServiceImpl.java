package com.lenztech.bi.enterprise.service.enterprise;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.optimus.DynamicSubAnswerDTO;
import com.lenztech.bi.enterprise.dto.unilever.UnileverProductDistributionDTO;
import com.lenztech.bi.enterprise.dto.unilever.UnileverStoreStatusDTO;
import com.lenztech.bi.enterprise.entity.lenzbi.PpzBackfillSetting;
import com.lenztech.bi.enterprise.entity.lenzbi.PpzUnileverEmailRecord;
import com.lenztech.bi.enterprise.entity.ppz.AnswerDO;
import com.lenztech.bi.enterprise.entity.ppz.BusinessQuestionRadioOptionDO;
import com.lenztech.bi.enterprise.entity.ppz.QuestionDynamicCircularRelDO;
import com.lenztech.bi.enterprise.entity.ppz.QuestionDynamicCircularTitleDO;
import com.lenztech.bi.enterprise.entity.ppz.QuestionFillBlankDO;
import com.lenztech.bi.enterprise.entity.ppz.TaskAddressDO;
import com.lenztech.bi.enterprise.mapper.ppz.AnswerMapper;
import com.lenztech.bi.enterprise.mapper.ppz.BusinessQuestionRadioOptionMapper;
import com.lenztech.bi.enterprise.mapper.ppz.QuestionDynamicCircularRelMapper;
import com.lenztech.bi.enterprise.mapper.ppz.QuestionDynamicCircularTitleMapper;
import com.lenztech.bi.enterprise.mapper.ppz.QuestionFillBlankMapper;
import com.lenztech.bi.enterprise.mapper.ppz.TaskAddressMapper;
import com.lenztech.bi.enterprise.service.lenzbi.PpzBackfillSettingService;
import com.lenztech.bi.enterprise.service.lenzbi.PpzUnileverEmailRecordService;
import com.lenztech.bi.enterprise.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class UnileverServiceImpl {

    @Resource
    private AnswerMapper answerMapper;

    @Resource
    private QuestionDynamicCircularTitleMapper circularTitleMapper;

    @Resource
    private QuestionDynamicCircularRelMapper circularRelMapper;

    @Resource
    private QuestionFillBlankMapper questionFillBlankMapper;

    @Resource
    private TaskAddressMapper businessTaskAddressMapper;

    @Resource
    private BusinessQuestionRadioOptionMapper businessQuestionRadioOptionMapper;

    @Resource
    private PpzUnileverEmailRecordService pgUnileverEmailRecordService;

    @Resource
    private PpzBackfillSettingService ppzBackfillSettingService;

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${unilever.email.to:}")
    private String toEmails;

    @Value("${unilever.email.cc:}")
    private String ccEmails;

    @Value("${unilever.excel.export-path:/data/temp/}")
    private String excelExportPath;


    /**
     * 处理Unilever数据并生成Excel报告（一次发一封邮件，合并所有执行记录）
     *
     * @param taskAddressIdList 指定的执行ID列表，如果不传则查询昨天的数据
     */
    public void processUnileverData(List<Long> taskAddressIdList) {

        // 1. 提前查出门店状态题选项内容
        List<PpzBackfillSetting> allSettings = ppzBackfillSettingService.list(
                new LambdaQueryWrapper<PpzBackfillSetting>().eq(PpzBackfillSetting::getProjectCode, "unilever")
        );
        if (allSettings.isEmpty()) {
            log.warn("未找到可用的配置");
            return;
        }
        Map<String, String> configMap = allSettings.stream().collect(Collectors.toMap(
                PpzBackfillSetting::getSettingKey,
                PpzBackfillSetting::getSettingValue
        ));

        // 2. 查询执行记录
        List<TaskAddressDO> execList;
        if (CollectionUtils.isEmpty(taskAddressIdList)) {
            // 查询昨天审核通过的执行
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String startTime = yesterday.atStartOfDay().toString().replace("T", " ");
            String endTime = LocalDate.now().atStartOfDay().toString().replace("T", " ");
            String taskId = allSettings.get(0).getTaskId();
            execList = businessTaskAddressMapper.selectYesterdayAuditedTaskAddress(taskId, startTime, endTime);
        } else {
            // 查询指定执行记录
            execList = businessTaskAddressMapper.selectList(
                    new LambdaQueryWrapper<TaskAddressDO>()
                            .in(TaskAddressDO::getTaskAddressId, taskAddressIdList)
            );
        }
        if (CollectionUtils.isEmpty(execList)) {
            log.info("未找到需要处理的执行记录");
            return;
        }

        // 3. 提前查出门店状态题选项内容
        String storeStatusQid = configMap.get("store_status_qid");
        Map<String, String> storeStatusOptionMap = new HashMap<>();
        if (StringUtils.isNotBlank(storeStatusQid)) {
            List<BusinessQuestionRadioOptionDO> options = businessQuestionRadioOptionMapper.selectList(
                    new LambdaQueryWrapper<BusinessQuestionRadioOptionDO>()
                            .eq(BusinessQuestionRadioOptionDO::getQuestionId, storeStatusQid)
            );
            storeStatusOptionMap = options.stream().collect(Collectors.toMap(
                    o -> String.valueOf(o.getOptionId()),
                    BusinessQuestionRadioOptionDO::getContent,
                    (a, b) -> a
            ));
        }

        // 只查本次循环题相关title/rel
        String dynamicQidStr = configMap.get("product_dynamic_qid");
        List<QuestionDynamicCircularTitleDO> titles = circularTitleMapper.selectList(
                new LambdaQueryWrapper<QuestionDynamicCircularTitleDO>()
                        .eq(QuestionDynamicCircularTitleDO::getQuestionId, dynamicQidStr)
        );
        List<QuestionDynamicCircularRelDO> rels = circularRelMapper.selectList(
                new LambdaQueryWrapper<QuestionDynamicCircularRelDO>()
                        .eq(QuestionDynamicCircularRelDO::getQuestionId, dynamicQidStr)
        );
        Set<String> subQids = rels.stream().map(rel -> String.valueOf(rel.getRelQuestionId())).collect(Collectors.toSet());
        Map<Long, QuestionFillBlankDO> subQuestionMap = new HashMap<>();
        if (!subQids.isEmpty()) {
            LambdaQueryWrapper<QuestionFillBlankDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(QuestionFillBlankDO::getQuestionId, subQids);
            List<QuestionFillBlankDO> subQuestions = questionFillBlankMapper.selectList(queryWrapper);
            for (QuestionFillBlankDO q : subQuestions) {
                subQuestionMap.put(q.getQuestionId(), q);
            }
        }

        // 4. 汇总所有执行的数据
        List<UnileverStoreStatusDTO> storeStatusList = new ArrayList<>();
        List<UnileverProductDistributionDTO> productDistributionList = new ArrayList<>();
        List<Long> processedTaskAddressIds = new ArrayList<>();
        List<Long> processedTaskIds = new ArrayList<>();

        for (TaskAddressDO exec : execList) {
            Pair<UnileverStoreStatusDTO, List<UnileverProductDistributionDTO>> result = processSingleExecutionForBatch(exec, configMap, storeStatusOptionMap, titles, rels, subQuestionMap);
            if (result.getLeft() != null) {
                storeStatusList.add(result.getLeft());
            }
            if (result.getRight() != null) {
                productDistributionList.addAll(result.getRight());
            }
            processedTaskAddressIds.add(exec.getTaskAddressId());
            if (exec.getTaskId() != null) {
                processedTaskIds.add(exec.getTaskId());
            }
        }

        if (storeStatusList.isEmpty()) {
            log.info("无可处理数据");
            return;
        }

        // 5. 生成Excel文件
        String excelFilePath = null;
        try {
            excelFilePath = generateExcelFileBatch(storeStatusList, productDistributionList);
        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            return;
        }

        // 6. 发送邮件
        try {
            sendEmailWithAttachment(excelFilePath, processedTaskAddressIds);
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return;
        }

        // 7. 批量保存邮件发送记录
        saveEmailRecords(processedTaskAddressIds, processedTaskIds);
    }

    /**
     * 批量处理单个执行，返回DTO
     */
    private Pair<UnileverStoreStatusDTO, List<UnileverProductDistributionDTO>> processSingleExecutionForBatch(TaskAddressDO exec,
                                                                                                              Map<String, String> settingMap,
                                                                                                              Map<String, String> storeStatusOptionMap,
                                                                                                              List<QuestionDynamicCircularTitleDO> titles,
                                                                                                              List<QuestionDynamicCircularRelDO> rels,
                                                                                                              Map<Long, QuestionFillBlankDO> subQuestionMap) {
        Long taskId = exec.getTaskId();
        Long taskAddressId = exec.getTaskAddressId();

        // 第一个sheet
        UnileverStoreStatusDTO storeStatusDTO = new UnileverStoreStatusDTO();
        storeStatusDTO.setStoreCode(exec.getStoreCode());
        storeStatusDTO.setStoreName(exec.getStoreName());
        storeStatusDTO.setProvince(exec.getProvinceName());
        storeStatusDTO.setCity(exec.getCityName());
        Map<String, String> baseData = getBaseData(settingMap, taskId, taskAddressId); // storeStatusQid、secondaryDisplayAreaQid
        storeStatusDTO.setEvidenceLink("https://ppz-manager-ui.langjtech.com/mediaList?id=" + taskAddressId + "&fullRecording=true");
        storeStatusDTO.setStoreStatus(storeStatusOptionMap.getOrDefault(baseData.get("storeStatus"), ""));
        storeStatusDTO.setSecondaryDisplayArea(baseData.get("secondaryDisplayArea"));

        // 第二个sheet
        List<DynamicSubAnswerDTO> productDynamicList = getDynamicSubAnswersWithTaskId(settingMap.get("product_dynamic_qid"), taskAddressId, titles, rels, subQuestionMap);
        List<UnileverProductDistributionDTO> productDistributionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productDynamicList)) {
            Map<String, List<DynamicSubAnswerDTO>> productGroups = productDynamicList.stream()
                    .collect(Collectors.groupingBy(DynamicSubAnswerDTO::getCircularTitle));
            for (Map.Entry<String, List<DynamicSubAnswerDTO>> entry : productGroups.entrySet()) {
                UnileverProductDistributionDTO productDTO = new UnileverProductDistributionDTO();
                productDTO.setStoreCode(exec.getStoreCode());
                productDTO.setStoreName(exec.getStoreName());
                productDTO.setProvince(exec.getProvinceName());
                productDTO.setCity(exec.getCityName());
                for (DynamicSubAnswerDTO subAnswer : entry.getValue()) {
                    String subQuestionTitle = subAnswer.getSubQuestionTitle();
                    String answer = subAnswer.getAnswer();
                    if (StringUtils.isNotBlank(subQuestionTitle) && StringUtils.isNotBlank(answer)) {
                        if ("Skucode".equals(subQuestionTitle)) {
                            productDTO.setSkuCode(answer);
                        } else if ("Barcode".equals(subQuestionTitle)) {
                            productDTO.setBarcode(answer);
                        } else if ("产品中文名称".equals(subQuestionTitle)) {
                            productDTO.setProductName(answer);
                        } else if ("产品类型".equals(subQuestionTitle)) {
                            productDTO.setProductType(answer);
                        }
                    }
                }
                if (StringUtils.isNotBlank(productDTO.getSkuCode()) ||
                        StringUtils.isNotBlank(productDTO.getBarcode()) ||
                        StringUtils.isNotBlank(productDTO.getProductName()) ||
                        StringUtils.isNotBlank(productDTO.getProductType())) {
                    productDistributionList.add(productDTO);
                }
            }
        }
        return Pair.of(storeStatusDTO, productDistributionList);
    }

    /**
     * 获取基础数据（只查evidenceLinkQid、storeStatusQid、secondaryDisplayAreaQid）
     */
    private Map<String, String> getBaseData(Map<String, String> setting, Long taskId, Long taskAddressId) {
        Map<String, String> baseData = new HashMap<>();
        List<String> normalQids = new ArrayList<>();
        normalQids.add(setting.get("store_status_qid"));
        normalQids.add(setting.get("secondary_display_area_qid"));
        normalQids = normalQids.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalQids)) {
            return baseData;
        }
        List<AnswerDO> answerList = answerMapper.selectList(
                new LambdaQueryWrapper<AnswerDO>()
                        .eq(AnswerDO::getTaskId, taskId)
                        .eq(AnswerDO::getTaskAddressId, taskAddressId)
                        .in(AnswerDO::getQuestionId, normalQids)
        );
        Map<String, AnswerDO> answerMap = answerList.stream().collect(Collectors.toMap(a -> String.valueOf(a.getQuestionId()), a -> a, (a, b) -> a));
        baseData.put("storeStatus", getAnswerFromMap(answerMap, setting.get("store_status_qid")));
        baseData.put("secondaryDisplayArea", getAnswerFromMap(answerMap, setting.get("secondary_display_area_qid")));
        return baseData;
    }

    /**
     * 从答案Map中获取指定题目的答案
     */
    private String getAnswerFromMap(Map<String, AnswerDO> answerMap, String qidStr) {
        if (qidStr == null) return null;
        AnswerDO answer = answerMap.get(qidStr);
        if (answer == null) return null;
        return StringUtils.isNotBlank(answer.getResponseText()) ? answer.getResponseText() : answer.getOptionId();
    }

    /**
     * 查找动态循环题下所有循环项-子题-答案
     */
    private List<DynamicSubAnswerDTO> getDynamicSubAnswersWithTaskId(String dynamicQidStr, Long taskAddressId,
                                                                     List<QuestionDynamicCircularTitleDO> titles,
                                                                     List<QuestionDynamicCircularRelDO> rels,
                                                                     Map<Long, QuestionFillBlankDO> subQuestionMap) {
        try {
            // 一次性查出所有相关答案
            Set<Long> titleIds = titles.stream().map(QuestionDynamicCircularTitleDO::getId).collect(Collectors.toSet());
            Set<Long> relQuestionIds = rels.stream().map(QuestionDynamicCircularRelDO::getRelQuestionId).collect(Collectors.toSet());
            List<AnswerDO> answerList = answerMapper.selectList(
                    new LambdaQueryWrapper<AnswerDO>()
                            .eq(AnswerDO::getQuestionId, dynamicQidStr)
                            .eq(AnswerDO::getTaskAddressId, taskAddressId)
                            .in(AnswerDO::getDynamicCircularTitleId, titleIds)
                            .in(AnswerDO::getSubQuestionId, relQuestionIds)
                            .eq(AnswerDO::getDeleted, 0)
            );
            // key: questionId+titleId_subquestionId
            Map<String, AnswerDO> answerMap = answerList.stream()
                    .collect(Collectors.toMap(
                            a -> a.getQuestionId() + "_" + a.getDynamicCircularTitleId() + "_" + a.getSubQuestionId(),
                            a -> a,
                            (a, b) -> a
                    ));

            // 优化：提前将titles转为Map，避免每次遍历
            Map<Long, QuestionDynamicCircularTitleDO> titleMap = titles.stream()
                    .collect(Collectors.toMap(QuestionDynamicCircularTitleDO::getId, t -> t, (a, b) -> a));
            // answerMap的key: questionId_titleId_subquestionId
            List<DynamicSubAnswerDTO> list = new ArrayList<>();
            for (AnswerDO a : answerMap.values()) {
                Long titleId = a.getDynamicCircularTitleId();
                Long subQuestionId = a.getSubQuestionId();
                // 查找title对象
                QuestionDynamicCircularTitleDO title = titleMap.get(titleId);
                // 查找子题对象
                QuestionFillBlankDO subQ = subQuestionMap.get(subQuestionId);
                DynamicSubAnswerDTO dto = new DynamicSubAnswerDTO();
                dto.setCircularTitle(title != null ? title.getTitle() : null);
                dto.setSubQuestionTitle(subQ != null ? subQ.getTitle() : null);
                dto.setAnswer(a.getResponseText());
                list.add(dto);
            }
            return list;
        } catch (Exception e) {
            log.error("处理动态循环题答案异常！", e);
        }
        return new ArrayList<>();
    }

    /**
     * 批量生成Excel文件
     */
    private String generateExcelFileBatch(List<UnileverStoreStatusDTO> storeStatusList, List<UnileverProductDistributionDTO> productDistributionList) throws Exception {
        String fileName = "Unilever_Report_" + DateUtil.getCurrentDate("yyyyMMddHHmmss") + ".xlsx";
        String filePath = excelExportPath + (excelExportPath.endsWith("/") ? "" : "/") + fileName;
        File dir = new File(excelExportPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        org.apache.poi.xssf.usermodel.XSSFWorkbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook();
        // Sheet1
        org.apache.poi.ss.usermodel.Sheet sheet1 = workbook.createSheet("门店状态及二陈结果");
        org.apache.poi.ss.usermodel.Row headerRow1 = sheet1.createRow(0);
        headerRow1.createCell(0).setCellValue("门店编码");
        headerRow1.createCell(1).setCellValue("门店名称");
        headerRow1.createCell(2).setCellValue("省份");
        headerRow1.createCell(3).setCellValue("城市");
        headerRow1.createCell(4).setCellValue("证据链接");
        headerRow1.createCell(5).setCellValue("门店状态");
        headerRow1.createCell(6).setCellValue("二陈平米数");
        for (int i = 0; i < storeStatusList.size(); i++) {
            UnileverStoreStatusDTO dto = storeStatusList.get(i);
            org.apache.poi.ss.usermodel.Row row = sheet1.createRow(i + 1);
            row.createCell(0).setCellValue(dto.getStoreCode() != null ? dto.getStoreCode() : "");
            row.createCell(1).setCellValue(dto.getStoreName() != null ? dto.getStoreName() : "");
            row.createCell(2).setCellValue(dto.getProvince() != null ? dto.getProvince() : "");
            row.createCell(3).setCellValue(dto.getCity() != null ? dto.getCity() : "");
            row.createCell(4).setCellValue(dto.getEvidenceLink() != null ? dto.getEvidenceLink() : "");
            row.createCell(5).setCellValue(dto.getStoreStatus() != null ? dto.getStoreStatus() : "");
            row.createCell(6).setCellValue(dto.getSecondaryDisplayArea() != null ? dto.getSecondaryDisplayArea() : "");
        }
        // Sheet2
        org.apache.poi.ss.usermodel.Sheet sheet2 = workbook.createSheet("产品分销");
        org.apache.poi.ss.usermodel.Row headerRow2 = sheet2.createRow(0);
        headerRow2.createCell(0).setCellValue("门店编码");
        headerRow2.createCell(1).setCellValue("门店名称");
        headerRow2.createCell(2).setCellValue("省份");
        headerRow2.createCell(3).setCellValue("城市");
        headerRow2.createCell(4).setCellValue("Skucode");
        headerRow2.createCell(5).setCellValue("Barcode");
        headerRow2.createCell(6).setCellValue("产品中文名称");
        headerRow2.createCell(7).setCellValue("产品类型");
        for (int i = 0; i < productDistributionList.size(); i++) {
            UnileverProductDistributionDTO product = productDistributionList.get(i);
            org.apache.poi.ss.usermodel.Row row = sheet2.createRow(i + 1);
            row.createCell(0).setCellValue(product.getStoreCode() != null ? product.getStoreCode() : "");
            row.createCell(1).setCellValue(product.getStoreName() != null ? product.getStoreName() : "");
            row.createCell(2).setCellValue(product.getProvince() != null ? product.getProvince() : "");
            row.createCell(3).setCellValue(product.getCity() != null ? product.getCity() : "");
            row.createCell(4).setCellValue(product.getSkuCode() != null ? product.getSkuCode() : "");
            row.createCell(5).setCellValue(product.getBarcode() != null ? product.getBarcode() : "");
            row.createCell(6).setCellValue(product.getProductName() != null ? product.getProductName() : "");
            row.createCell(7).setCellValue(product.getProductType() != null ? product.getProductType() : "");
        }
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(filePath)) {
            workbook.write(fos);
        }
        workbook.close();
        return filePath;
    }

    /**
     * 发送带附件的邮件（一次发一封，支持多个执行ID）
     */
    private void sendEmailWithAttachment(String filePath, List<Long> taskAddressIdList) throws Exception {
        if (StringUtils.isBlank(toEmails)) {
            log.warn("邮件收件人配置为空，跳过邮件发送，taskAddressIds: {}", taskAddressIdList);
            return;
        }
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setFrom(fromEmail);
        helper.setTo(toEmails.split(","));
        if (StringUtils.isNotBlank(ccEmails)) {
            helper.setCc(ccEmails.split(","));
        }
        helper.setSubject("【联合利华】口腔报告-" + DateUtil.getCurrentDate("yyyy-MM-dd"));
        helper.setText("附件为一审通过的数据报告，请查收");
        FileSystemResource file = new FileSystemResource(new File(filePath));
        helper.addAttachment(file.getFilename(), file);
        mailSender.send(message);
        log.info("邮件发送成功，taskAddressIds: {}", taskAddressIdList);
//        new File(filePath).delete();
    }

    /**
     * 批量保存邮件发送记录
     */
    private void saveEmailRecords(List<Long> taskAddressIds, List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskAddressIds)) return;
        List<PpzUnileverEmailRecord> records = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < taskAddressIds.size(); i++) {
            PpzUnileverEmailRecord record = new PpzUnileverEmailRecord();
            record.setTaskAddressId(taskAddressIds.get(i));
            record.setTaskId(taskIds.size() > i ? String.valueOf(taskIds.get(i)) : null);
            record.setEmailDate(now);
            record.setCreateTime(now);
            record.setUpdateTime(now);
            records.add(record);
        }
        pgUnileverEmailRecordService.saveBatch(records);
        log.info("批量保存邮件发送记录成功，数量: {}", records.size());
    }
} 