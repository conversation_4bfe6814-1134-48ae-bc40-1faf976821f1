package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TBfdProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String responseId;

    /**
     * 图片ID
     */
    private String imageId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 坐标
     */
    private String coordinate;

    /**
     * 产品识别高
     */
    private Integer patchHeight;

    /**
     * 产品识别宽
     */
    private Integer patchWidth;

    /**
     * 产品所在层
     */
    private Integer layer;

    /**
     * 产品所在列
     */
    private Integer columnNumber;

    /**
     * 重复表示
     */
    private String inrange;

    /**
     * 场景
     */
    private String scene;

    /**
     * 是否是排面sku
     */
    private Integer ifFacing;

    /**
     * sku所占排面数
     */
    private Integer facingCount;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
