package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.http.request.AppealQueryRequest;
import com.lenztech.bi.enterprise.http.request.AppealSubmitRequest;

/**
 * @description:申诉API接口
 * <AUTHOR>
 */
public interface ApiAppealService {
    /**
     * @description:添加申诉
     * @param appealSubmitRequest
     * @return
     */
    ResponseData addAppeal(AppealSubmitRequest appealSubmitRequest,Boolean flag) throws Exception;
    /**
     * @description:申诉结果查询
     * @return
     */
    ResponseData queryAppealResult(AppealQueryRequest appealQueryRequest) throws Exception;


}
