//package com.lenztech.bi.enterprise.service.impl;
//
//import com.google.common.base.Strings;
//import com.lenztech.bi.enterprise.dto.CompanyUserDTO;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.bi.CompanyDTO;
//import com.lenztech.bi.enterprise.mapper.bienterprise.ArgusCompanyDao;
//import com.lenztech.bi.enterprise.service.ArgusCompanyService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.util.List;
//
///**
// * @program: bi-service
// * @description: argus关于公司接口实现
// * @author: longgang
// * @create: 2020-09-08 10:24
// **/
//@Service
//public class ArgusCompanyServiceImpl implements ArgusCompanyService {
//
//    @Autowired
//    private ArgusCompanyDao argusCompanyDao;
//
//
//    @Override
//    public String getRealName(String account, Integer companyId) {
//        String realName = argusCompanyDao.findRealNameByAaccount(account, companyId);
//        return realName;
//    }
//
//    @Override
//    public ResponseData<List<CompanyDTO>> getCompanyMessage(String message) {
//        if (Strings.isNullOrEmpty(message)) {
//            return ResponseData.success();
//        }
//        List<CompanyDTO> companyDTOList = argusCompanyDao.getCompanyMessage(message);
//        return ResponseData.success(companyDTOList);
//    }
//
//    @Override
//    public ResponseData<Integer> getCompanyId(String message) {
//        if (Strings.isNullOrEmpty(message)) {
//            return ResponseData.success();
//        }
//        List<CompanyDTO> companyDTOList = argusCompanyDao.getCompanyId(message);
//        if (CollectionUtils.isEmpty(companyDTOList)) {
//            return ResponseData.success(0);
//        }
//        return ResponseData.success(companyDTOList.get(0).getCompanyId());
//    }
//
//    @Override
//    public CompanyDTO getCompanyMessageByName(String companyName) {
//        CompanyDTO companyDTO = argusCompanyDao.getCompanyMessageByName(companyName);
//        return companyDTO;
//    }
//
//    @Override
//    public List<CompanyUserDTO> getCompanyUser(String account) {
//        List<CompanyUserDTO> companyUser = argusCompanyDao.getCompanyUser(account);
//        return companyUser;
//    }
//}
