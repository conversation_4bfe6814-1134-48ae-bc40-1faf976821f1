package com.lenztech.bi.enterprise.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lenztech.bi.enterprise.dto.car.AllPostCodeRateDTO;
import com.lenztech.bi.enterprise.dto.car.AreaDTO;
import com.lenztech.bi.enterprise.dto.car.AreaEnum;
import com.lenztech.bi.enterprise.dto.car.CarData;
import com.lenztech.bi.enterprise.dto.car.CarDataDTO;
import com.lenztech.bi.enterprise.dto.car.CarDemoConstant;
import com.lenztech.bi.enterprise.dto.car.CarDetailDTO;
import com.lenztech.bi.enterprise.dto.car.CarDetailData;
import com.lenztech.bi.enterprise.dto.car.CarPatternDTO;
import com.lenztech.bi.enterprise.dto.car.CarProportionData;
import com.lenztech.bi.enterprise.dto.car.CarSellRateDTO;
import com.lenztech.bi.enterprise.dto.car.CarStatisticsDataDTO;
import com.lenztech.bi.enterprise.dto.car.CarStatisticsDataReq;
import com.lenztech.bi.enterprise.dto.car.LineChartDTO;
import com.lenztech.bi.enterprise.dto.car.LineChartData;
import com.lenztech.bi.enterprise.dto.car.LineChartStatisticsDTO;
import com.lenztech.bi.enterprise.dto.car.OldPeopleData;
import com.lenztech.bi.enterprise.dto.car.PeopleExecuteStatisticsDTO;
import com.lenztech.bi.enterprise.dto.car.PeopleNumDTO;
import com.lenztech.bi.enterprise.dto.car.PostCodeDTO;
import com.lenztech.bi.enterprise.dto.car.ProvinceData;
import com.lenztech.bi.enterprise.dto.car.ProvincePostCodeDTO;
import com.lenztech.bi.enterprise.dto.car.SellStatisticsDTO;
import com.lenztech.bi.enterprise.service.ICarDemoService;
import com.lenztech.bi.enterprise.utils.GpsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/1 13:51
 **/

@Service
public class CarDemoServiceImpl implements ICarDemoService {

    @Override
    public LineChartStatisticsDTO getPeoplePerformAndStoreCover(String carType) {
        LineChartStatisticsDTO dto = new LineChartStatisticsDTO();
        List<LineChartData> lineChartDataList = Lists.newArrayList();
        if (StringUtils.equals(carType, "1")) {
            lineChartDataList = JSONObject.parseArray(CarDemoConstant.NEW_LINE_CHART_DATA, LineChartData.class);
        } else {
            lineChartDataList = JSONObject.parseArray(CarDemoConstant.OLD_LINE_CHART_DATA, LineChartData.class);
        }
        List<LineChartDTO> peoplePerformList = Lists.newArrayList();
        List<LineChartDTO> postCodeRateList = Lists.newArrayList();
        Map<String, List<LineChartData>> areaMap = lineChartDataList.stream().collect(Collectors.groupingBy(LineChartData::getArea));
        for (String area : areaMap.keySet()) {
            LineChartDTO peoplePerform = new LineChartDTO();
            LineChartDTO postCodeRate = new LineChartDTO();
            peoplePerform.setName(AreaEnum.getNameCnByNameEn(area.trim()));
            postCodeRate.setName(AreaEnum.getNameCnByNameEn(area.trim()));
            List<Integer> peoplePerformDataList = Lists.newArrayList();
            List<Integer> postCodeRateDataList = Lists.newArrayList();
            for (LineChartData lineChartData : areaMap.get(area)) {
                peoplePerformDataList.add(lineChartData.getPeopleNum());
                postCodeRateDataList.add(lineChartData.getPeopleCodeNum());
            }
            peoplePerform.setDataList(peoplePerformDataList);
            postCodeRate.setDataList(postCodeRateDataList);
            peoplePerformList.add(peoplePerform);
            postCodeRateList.add(postCodeRate);
        }
        dto.setPeoplePerformList(peoplePerformList);
        dto.setPostCodeRateList(postCodeRateList);

        PeopleNumDTO peopleNumDTO = new PeopleNumDTO();
        int totalPeople = lineChartDataList.stream().mapToInt(LineChartData::getPeopleNum).sum();
        peopleNumDTO.setTotalNum(totalPeople);
        List<AreaDTO> areaDTOList = Lists.newArrayList();
        for (String area : areaMap.keySet()) {
            AreaDTO areaDTO = new AreaDTO();
            areaDTO.setName(AreaEnum.getNameCnByNameEn(area.trim()));
            areaDTO.setValue(areaMap.get(area).stream().mapToInt(LineChartData::getPeopleNum).sum());
            areaDTOList.add(areaDTO);
        }
        peopleNumDTO.setAreaList(areaDTOList);
        dto.setExecutePeopleNumData(peopleNumDTO);

        PostCodeDTO postCodeDTO = new PostCodeDTO();
        int postCodeQuantity = 0;
        int responseTranslateNum = 0;
        int postCodeTranslateNum = 0;
        double postCodeTranslateRate = 0;
        int scanCodeNum = 0;
        double scanCodeRate = 0;
        int IPZShopping = 0;
        int fission = 0;
        int other = 0;
        for (LineChartData lineChartData : lineChartDataList) {
            postCodeQuantity += lineChartData.getPeopleCodeNum();
            responseTranslateNum += lineChartData.getResponseTranslateNum();
            postCodeTranslateNum += lineChartData.getPostCodeNum();
            scanCodeNum += lineChartData.getScanCodeNum();
            IPZShopping += lineChartData.getIPZShopping();
            fission += lineChartData.getFission();
            other += lineChartData.getOther();
        }
        scanCodeRate = new BigDecimal(scanCodeNum).divide(new BigDecimal(postCodeQuantity), 2, BigDecimal.ROUND_DOWN).doubleValue();
        postCodeTranslateRate = new BigDecimal(postCodeTranslateNum).divide(new BigDecimal(scanCodeNum), 2, BigDecimal.ROUND_DOWN).doubleValue();
        postCodeDTO.setPostCodeQuantity(postCodeQuantity);
        postCodeDTO.setPostCodeTranslateNum(postCodeTranslateNum);
        postCodeDTO.setPostCodeTranslateRate(postCodeTranslateRate);
        postCodeDTO.setScanCodeNum(scanCodeNum);
        postCodeDTO.setScanCodeRate(scanCodeRate);
        postCodeDTO.setIPZShopping(IPZShopping);
        postCodeDTO.setFission(fission);
        postCodeDTO.setOther(other);
        postCodeDTO.setResponseTranslateNum(responseTranslateNum);
        dto.setPostCodeData(postCodeDTO);
        return dto;
    }

    @Override
    public PeopleExecuteStatisticsDTO getDataByPeoplePerform(String carType, String area) {
        PeopleExecuteStatisticsDTO dto = new PeopleExecuteStatisticsDTO();

        List<OldPeopleData> peopleDataList = Lists.newArrayList();
        if (StringUtils.equals(carType, "1")) {
            peopleDataList = JSONObject.parseArray(CarDemoConstant.NEW_PEOPLE_EXCUTE_DATA, OldPeopleData.class);
        } else {
            peopleDataList = JSONObject.parseArray(CarDemoConstant.OLD_PEOPLE_EXCUTE_DATA, OldPeopleData.class);
        }
        List<OldPeopleData> areaList = peopleDataList.stream().filter(data -> StringUtils.equals(data.getCity().trim(), area)).collect(Collectors.toList());
        dto.setCityList(areaList.stream().map(OldPeopleData::getCity).collect(Collectors.toList()));
        List<Integer> targetCompleteList = Lists.newArrayList();
        List<Integer> practicalCompleteList = Lists.newArrayList();
        List<Double> completeRateList = Lists.newArrayList();
        int totalTargetComplete = 0;
        int totalPracticalComplete = 0;
        double totalCompleteRate = 0;
        for (OldPeopleData data : areaList) {
            practicalCompleteList.add(data.getPracticalComplete());
            totalPracticalComplete += data.getPracticalComplete();
            if (StringUtils.equals(carType, "2")) {
                targetCompleteList.add(data.getTargetComplete());
                completeRateList.add(data.getCompleteRate());
                totalTargetComplete += data.getTargetComplete();
            }

        }
        dto.setTargetCompleteList(targetCompleteList);
        dto.setPracticalCompleteList(practicalCompleteList);
        dto.setCompleteRateList(completeRateList);


        dto.setTotalPracticalComplete(totalPracticalComplete);
        if (StringUtils.equals(carType, "2")) {
            totalCompleteRate = new BigDecimal(totalPracticalComplete).divide(new BigDecimal(totalTargetComplete), 2, BigDecimal.ROUND_DOWN).doubleValue();
            dto.setTotalTargetComplete(totalTargetComplete);
            dto.setTotalCompleteRate(totalCompleteRate);
        }
        List<SellStatisticsDTO> sellStatisticsDTOList = Lists.newArrayList();
        for (OldPeopleData data : areaList) {
            SellStatisticsDTO sellStatisticsDTO = new SellStatisticsDTO();
            sellStatisticsDTO.setArea(data.getArea());
            sellStatisticsDTO.setCity(data.getCity());
            sellStatisticsDTO.setPracticalComplete(data.getPracticalComplete());
            if (StringUtils.equals(carType, "2")) {
                sellStatisticsDTO.setTargetComplete(data.getTargetComplete());
                sellStatisticsDTO.setCompleteRate(data.getCompleteRate());
            }
            sellStatisticsDTOList.add(sellStatisticsDTO);
        }
        dto.setStatisticsDataList(sellStatisticsDTOList);
        return dto;
    }

    @Override
    public PostCodeDTO getAddressPostCodeRate(String address) {
        List<ProvinceData> provinceDataList = JSONObject.parseArray(CarDemoConstant.PROVINCE_DATA, ProvinceData.class);
        List<ProvinceData> addressList = provinceDataList.stream().filter(data -> StringUtils.equals(data.getProvince(), address)).collect(Collectors.toList());
        PostCodeDTO dto = getPostCodeDTO(addressList, CarDemoConstant.ADDRESS);
        return dto;
    }

    @Override
    public AllPostCodeRateDTO getAllPostCodeRate() {
        AllPostCodeRateDTO dto = new AllPostCodeRateDTO();
        List<ProvinceData> provinceDataList = JSONObject.parseArray(CarDemoConstant.PROVINCE_DATA, ProvinceData.class);

        PostCodeDTO totalData = getPostCodeDTO(provinceDataList, CarDemoConstant.ALL);
        dto.setTotalData(totalData);

        Map<String, List<ProvinceData>> provinceMap = provinceDataList.stream().collect(Collectors.groupingBy(ProvinceData::getProvince));
        List<PostCodeDTO> provinceList = Lists.newArrayList();

        for (String province : provinceMap.keySet()) {
            PostCodeDTO provinceData = getPostCodeDTO(provinceMap.get(province), CarDemoConstant.PROVINCE);
            provinceList.add(provinceData);
        }
        dto.setProvinceDataList(provinceList);
        return dto;
    }

    @Override
    public ProvincePostCodeDTO getProvincePostCodeRate(String province) {
        ProvincePostCodeDTO dto = new ProvincePostCodeDTO();
        List<ProvinceData> provinceDataList = JSONObject.parseArray(CarDemoConstant.PROVINCE_DATA, ProvinceData.class);
        String allProvinceName = getProvinceName(province);

        Map<String, List<ProvinceData>> provinceMap = provinceDataList.stream().filter(data -> StringUtils.equals(data.getProvince(), allProvinceName)).collect(Collectors.groupingBy(ProvinceData::getCity));
        List<PostCodeDTO> provinceList = Lists.newArrayList();

        for (String city : provinceMap.keySet()) {
            PostCodeDTO provinceData = getPostCodeDTO(provinceMap.get(city), CarDemoConstant.CITY);
            provinceList.add(provinceData);
        }
        dto.setCityDataList(provinceList);
        return dto;
    }

    private String getProvinceName(String province) {
        if (province.contains("北京") || province.contains("上海") || province.contains("天津") || province.contains("重庆")) {
            return province + "市";
        } else {
            return province + "省";
        }
    }

    private PostCodeDTO getPostCodeDTO(List<ProvinceData> provinceDataList, String type) {
        PostCodeDTO result = new PostCodeDTO();
        if (CollectionUtils.isEmpty(provinceDataList)) {
            return result;
        }

        int postCodePeopleNum = 0;
        int postCodeNum = 0;
        int scanCodeNum = 0;
        int responseTranslateNum = 0;
        int fission = 0;
        int postCodeTranslateNum = 0;
        int IPZShopping = 0;
        int other = 0;

        for (ProvinceData provinceData : provinceDataList) {
            postCodePeopleNum += Integer.parseInt(provinceData.getPostCodePeopleNum());
            postCodeNum += Integer.parseInt(provinceData.getPostCodeNum());
            scanCodeNum += Integer.parseInt(provinceData.getScanCodeNum());
            responseTranslateNum += Integer.parseInt(provinceData.getResponseTranslateNum());
            fission += Integer.parseInt(provinceData.getFission());
            postCodeTranslateNum += Integer.parseInt(provinceData.getPostCodeTranslateNum());
            IPZShopping += Integer.parseInt(provinceData.getIPZShopping());
            other += Integer.parseInt(provinceData.getOther());
        }


        result.setPostCodeQuantity(postCodeNum);
        result.setResponseTranslateNum(responseTranslateNum);
        result.setPostCodeTranslateNum(postCodeTranslateNum);
        result.setScanCodeNum(scanCodeNum);
        result.setPeopleNum(postCodePeopleNum);
        result.setIPZShopping(IPZShopping);
        result.setFission(fission);
        result.setOther(other);


        Double postCodeTranslateRae = new BigDecimal(postCodeTranslateNum).divide(new BigDecimal(scanCodeNum), 2, BigDecimal.ROUND_DOWN).doubleValue();
        Double scanCodeRate = new BigDecimal(scanCodeNum).divide(new BigDecimal(postCodeNum), 2, BigDecimal.ROUND_DOWN).doubleValue();
        result.setPostCodeTranslateRate(postCodeTranslateRae);
        result.setScanCodeRate(scanCodeRate);


        if (StringUtils.equals(type, CarDemoConstant.PROVINCE)) {
            String province = provinceDataList.get(0).getProvince().substring(0, provinceDataList.get(0).getProvince().length() - 1);
            result.setProvince(province);
        }


        if (StringUtils.equals(type, CarDemoConstant.CITY)) {
            result.setProvince(provinceDataList.get(0).getCity());
            List<String> addressList = provinceDataList.stream().map(ProvinceData::getAddress).collect(Collectors.toList());
            List<String> coordinateList = Lists.newArrayList();
            for (String address : addressList) {
                String coordinate = GpsUtil.getAddressDepositoryDealWithResult(address);
                coordinateList.add(coordinate);
            }
            result.setCoordinate(coordinateList);
            result.setAddressList(addressList);
        }

        if (StringUtils.equals(type, CarDemoConstant.ADDRESS)) {
            result.setAddress(provinceDataList.get(0).getAddress());
        }

        return result;
    }

    @Override
    public CarSellRateDTO getCarSellRate() {
        CarSellRateDTO dto = new CarSellRateDTO();
        List<CarProportionData> carProportionDataList = JSONObject.parseArray(CarDemoConstant.CAR_PROPORTION_DATA, CarProportionData.class);
        List<String> brandList = carProportionDataList.stream().map(CarProportionData::getBrand).collect(Collectors.toList());
        dto.setBrandList(brandList);
        List<AreaDTO> areaDTOList = Lists.newArrayList();
        for (CarProportionData carProportionData : carProportionDataList) {
            AreaDTO areaDTO = new AreaDTO();
            areaDTO.setName(carProportionData.getBrand());
            areaDTO.setValue(carProportionData.getPracticalComplete());
            areaDTOList.add(areaDTO);
        }
        dto.setSellNumList(areaDTOList);
        return dto;
    }

    @Override
    public CarStatisticsDataDTO getStatisticDataByBrand(CarStatisticsDataReq req) {
        CarStatisticsDataDTO dto = new CarStatisticsDataDTO();
        List<CarData> carDataList = JSONObject.parseArray(CarDemoConstant.CAR_DATA, CarData.class);
        if (StringUtils.isNotBlank(req.getArea())) {
            carDataList = carDataList.stream().filter(data -> StringUtils.equals(req.getArea(), data.getArea())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(req.getBrand())) {
            carDataList = carDataList.stream().filter(data -> StringUtils.equals(req.getBrand(), data.getBrand())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(req.getCity())) {
            carDataList = carDataList.stream().filter(data -> StringUtils.equals(req.getCity(), data.getCity())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(req.getModel())) {
            carDataList = carDataList.stream().filter(data -> StringUtils.equals(req.getModel(), data.getModel())).collect(Collectors.toList());
        }
        List<CarData> carList = carDataList;

        if (CollectionUtils.isEmpty(carList)) {
            return dto;
        }

        dto.setTotal(carList.size());

        dto.setCityList(carList.stream().map(CarData::getCity).distinct().collect(Collectors.toList()));
        Map<String, List<CarData>> cityMap = carList.stream().collect(Collectors.groupingBy(CarData::getCity));
        List<Integer> targetComplete = Lists.newArrayList();
        for (String city : cityMap.keySet()) {
            int sum = cityMap.get(city).stream().mapToInt(CarData::getTargetNum).sum();
            targetComplete.add(sum);
        }
        dto.setTargetComplete(targetComplete);
        List<Integer> practicalComplete = Lists.newArrayList();
        for (String city : cityMap.keySet()) {
            int sum = cityMap.get(city).stream().mapToInt(CarData::getSuccessResponseNum).sum();
            practicalComplete.add(sum);
        }
        dto.setPracticalComplete(practicalComplete);
        List<Double> completeRate = Lists.newArrayList();
        for (int i = 0; i < targetComplete.size(); i++) {
            Double rate = new BigDecimal(targetComplete.get(i)).divide(new BigDecimal(practicalComplete.get(i)), 2, BigDecimal.ROUND_DOWN).doubleValue();
            completeRate.add(rate);
        }
        dto.setCompleteRate(completeRate);

        List<CarData> pageData = Lists.newArrayList();
        int count = req.getPageNo() * req.getPageSize() - 1;
        if (count > carList.size()) {
            pageData = carList.subList((req.getPageNo() - 1) * req.getPageSize(), carList.size() - 1);
        } else {
            pageData = carList.subList((req.getPageNo() - 1) * req.getPageSize(), req.getPageNo() * req.getPageSize() - 1);
        }
        List<CarDataDTO> result = getCarStatisticsDataList(pageData);
        dto.setStatisticsDataList(result);
        return dto;
    }

    private List<CarDataDTO> getCarStatisticsDataList(List<CarData> pageData) {
        List<CarDataDTO> result = Lists.newArrayList();
        for (CarData carData : pageData) {
            CarDataDTO dto = new CarDataDTO();
            dto.setBrand(carData.getBrand());
            dto.setPattern(carData.getModel());
            dto.setArea(AreaEnum.getNameCnByNameEn(carData.getArea().trim()));
            dto.setCity(carData.getCity());
            dto.setPostCodeNum(carData.getPostCodeNum());
            dto.setScanCodeNum(carData.getScanCodeNum());
            dto.setResponseNum(carData.getSuccessResponseNum());
            dto.setTranslateType(carData.getTranslateWay());
            dto.setTypeRate(new BigDecimal(carData.getWayRate()).setScale(2, BigDecimal.ROUND_DOWN).toString());
            result.add(dto);
        }
        return result;
    }

    @Override
    public CarDetailDTO getDataByCarPattern(String pattern, Integer pageSize, Integer pageNo) {
        CarDetailDTO result = new CarDetailDTO();
        List<CarDetailData> carDetailDataList1 = JSONObject.parseArray(CarDemoConstant.CAR_DETAIL_DATA_1, CarDetailData.class);

        List<CarDetailData> carDetailDataList2 = JSONObject.parseArray(CarDemoConstant.CAR_DETAIL_DATA_2, CarDetailData.class);

        List<CarDetailData> carDetailDataList3 = JSONObject.parseArray(CarDemoConstant.CAR_DETAIL_DATA_3, CarDetailData.class);

        List<CarDetailData> carDetailDataList4 = JSONObject.parseArray(CarDemoConstant.CAR_DETAIL_DATA_4, CarDetailData.class);

        List<CarDetailData> carDetailDataList5 = JSONObject.parseArray(CarDemoConstant.CAR_DETAIL_DATA_5, CarDetailData.class);
        List<CarDetailData> carDetailDataList = Lists.newArrayList();
        carDetailDataList.addAll(carDetailDataList1);
        carDetailDataList.addAll(carDetailDataList2);
        carDetailDataList.addAll(carDetailDataList3);
        carDetailDataList.addAll(carDetailDataList4);
        carDetailDataList.addAll(carDetailDataList5);
        List<CarDetailData> dataList = carDetailDataList.stream().filter(data -> pattern.contains(data.getCXMC())).collect(Collectors.toList());
        result.setTotal(dataList.size());
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        List<CarDetailData> pageData = Lists.newArrayList();
        int count = pageSize * pageNo - 1;
        if (count > dataList.size()) {
            pageData = dataList.subList((pageNo - 1) * pageSize, dataList.size() - 1);
        } else {
            pageData = dataList.subList((pageNo - 1) * pageSize, pageNo * pageSize - 1);
        }
        List<CarPatternDTO> list = getCarDetailList(pageData);
        result.setDataList(list);
        return result;
    }


    private List<CarPatternDTO> getCarDetailList(List<CarDetailData> pageData) {
        List<CarPatternDTO> result = Lists.newArrayList();
        for (CarDetailData detail : pageData) {
            CarPatternDTO dto = new CarPatternDTO();
            dto.setResponseId(detail.getResponseId());
            dto.setLicensePlatNumber(detail.getLicensePlatNumber());
            dto.setVINCode(detail.getVINCode());
            dto.setCJMC(detail.getCJMC());
            dto.setPPMC(detail.getPPMC());
            dto.setCXMC(detail.getCXMC());
            dto.setND(detail.getND());
            dto.setPL(detail.getPL());
            dto.setCarName(detail.getBaiduRecognizeName());
            dto.setSource(detail.getSource());
            dto.setResponseAddress(detail.getResponseAddress());
            dto.setLicensePlatNumberAddress(detail.getLicensePlatNumberAddress());
            dto.setVINCodeAddress(detail.getVINCodeAddress());
            result.add(dto);
        }
        return result;
    }
}
