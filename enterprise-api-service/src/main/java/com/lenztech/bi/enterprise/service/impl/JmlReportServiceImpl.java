package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.jml.JmlBiTarget;
import com.lenztech.bi.enterprise.dto.jml.JmlBiTargetResp;
import com.lenztech.bi.enterprise.mapper.JmlReportMapper;
import com.lenztech.bi.enterprise.service.JmlReportService;
import com.lenztech.bi.enterprise.service.ShardingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 今麦郎bi指标ServiceImpl
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@Service
@RefreshScope
public class JmlReportServiceImpl implements JmlReportService {

    public static final Logger logger = LoggerFactory.getLogger(JmlReportServiceImpl.class);

    @Autowired
    private JmlReportMapper jmlReportMapper;

    @Autowired
    private ShardingService shardingService;

    @Value("${sharding.jmlEffectiveDay:''}")
    private String effectiveDay;


    /**
     * 查询指标集合
     *
     * @param responseId
     * @return JmlBiTargetResp
     */
    @Override
    public JmlBiTargetResp getBiTargetList(String responseId) {
        logger.info("responseId:" + responseId);
        JmlBiTargetResp jmlBiTargetResp = new JmlBiTargetResp();
        try {
            List<JmlBiTarget> jmlBiTargetList = jmlReportMapper.getBiTargetList(responseId);
            jmlBiTargetResp.setResponseId(responseId);
            jmlBiTargetResp.setTargetList(jmlBiTargetList);
        } catch (Exception e) {
            logger.error("/getBiTargetList========", e);
        }
        return jmlBiTargetResp;
    }

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return JmlBiTargetResp
     */
    @Override
    public JmlBiTargetResp getBiTargetListV2(String responseId) {
        logger.info("responseId:" + responseId);
        String shardingMonth = shardingService.getShardingKey(responseId, effectiveDay);
        JmlBiTargetResp jmlBiTargetResp = new JmlBiTargetResp();
        try {
            List<JmlBiTarget> jmlBiTargetList = jmlReportMapper.getBiTargetListV2(responseId, shardingMonth);
            jmlBiTargetResp.setResponseId(responseId);
            jmlBiTargetResp.setTargetList(jmlBiTargetList);
        } catch (Exception e) {
            logger.error("/getBiTargetListV2========", e);
        }
        return jmlBiTargetResp;
    }

}
