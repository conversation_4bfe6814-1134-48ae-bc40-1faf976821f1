//package com.lenztech.bi.enterprise.service;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.google.common.base.Preconditions;
//import com.google.common.base.Strings;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import com.lenztech.bi.enterprise.comon.BiReportSkuTypeEnum;
//import com.lenztech.bi.enterprise.comon.Constant;
//import com.lenztech.bi.enterprise.dto.CommonListRet;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.bi.*;
//import com.lenztech.bi.enterprise.entity.*;
//import com.lenztech.bi.enterprise.mapper.*;
//import com.lenztech.bi.enterprise.mapper.lenzbi.QingpiTarProductListMapper;
//import com.lenztech.bi.enterprise.mapper.task.*;
//import com.lenztech.bi.enterprise.utils.*;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.text.SimpleDateFormat;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * @Description:
// * @Author: zhangjie
// * @Date: 3/11/20 PM1:59
// */
//@Slf4j
//@Service
//public class BiSettingService {
//
//    public static final Logger LOGGER = LoggerFactory.getLogger(BiSettingService.class);
//
//    @Value("${h5.recongnitionDetailServiceUrl}")
//    private String recongnitionDetailServiceUrl;
//
//    @Value("${h5.appealServiceUrl}")
//    private String appealServiceUrl;
//
//    @Value("${h5.heapServiceUrl}")
//    private String heapServiceUrl;
//
//    /**
//     * 前端保留3位数
//     */
//    private final int roundScale = 3;
//
//    @Autowired
//    private ImageProductTreeMapper imageProductTreeMapper;
//
//    @Autowired
//    private TImageProductTreeMapper tImageProductTreeMapper;
//
//    @Autowired
//    private QingpiTarProductListMapper qingpiTarProductListMapper;
//
//    @Autowired
//    private BiReportSkuMapper biReportSkuMapper;
//
//    @Autowired
//    private BiReportBrandMapper biReportBrandMapper;
//
//    @Autowired
//    private BiReportCategoryMapper biReportCategoryMapper;
//
//    @Autowired
//    private BiStoreReportMapper biStoreReportMapper;
//
//    @Autowired
//    private BiStoreDetailReportMapper biStoreDetailReportMapper;
//
//    @Autowired
//    private TResponseMapper responseMapper;
//
//    @Autowired
//    private TTaskMapper taskMapper;
//
//    @Autowired
//    private TTasklaunchMapper tasklaunchMapper;
//
//    @Autowired
//    private TEnterpriseuserMapper enterpriseuserMapper;
//
//    @Autowired
//    private BiBrandService brandService;
//
//    @Autowired
//    private BiCategoryService categoryService;
//
//    @Autowired
//    private TImageProductMapper tImageProductMapper;
//
//
//    /**
//     * 获取任务基本信息
//     * @param targetListReq
//     * @return
//     */
//    public ResponseData getTaskInfo(SkuTargetListReq targetListReq) {
//        Preconditions.checkArgument(targetListReq.getTaskId() != null, "缺少参数");
//
//        TaskInfoResp taskInfoResp = new TaskInfoResp();
//        try {
//            LambdaQueryWrapper<TTask> taskLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            taskLambdaQueryWrapper.eq(TTask::getId, targetListReq.getTaskId());
//            TTask task = taskMapper.selectOne(taskLambdaQueryWrapper);
//
//            LambdaQueryWrapper<TEnterpriseuser> enterpriseuserLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            enterpriseuserLambdaQueryWrapper.eq(TEnterpriseuser::getId, task.getOwnerId());
//            TEnterpriseuser enterpriseuser = enterpriseuserMapper.selectOne(enterpriseuserLambdaQueryWrapper);
//
//            taskInfoResp.setTaskId(targetListReq.getTaskId());
//            taskInfoResp.setTaskName(task.getTaskname());
//            taskInfoResp.setFirmAccount(enterpriseuser.getEmail());
//
//            return ResponseData.success().data(taskInfoResp);
//        } catch (Exception ex){
//            LOGGER.error("【getTaskInfo】", ex);
//        }
//
//        return ResponseData.failure();
//    }
//
//    public ResponseData getTargetSkuList(SkuTargetListReq targetListReq) {
//        Preconditions.checkArgument(targetListReq.getTaskId() != null, "缺少参数");
//
//        try {
//            String taskId = targetListReq.getTaskId();
//            // 响应sku信息
//            List<ReportSkuListItem> skuListItems = new ArrayList<>();
//
//            List<ImageProductTree> imageProductTreeList = imageProductTreeMapper.getImageProductTreeListByTaskId(taskId);
//
//            // 已经设置报表的sku
//            List<BiReportSku> biReportSkuList = getSkuInReport(taskId);
//            List<Integer> skuIdListInReport = getSkuIdListInReport(biReportSkuList);
//
//            // 识别目标中的sku
//            List<Integer> skuIdListInRecognition = new ArrayList<>();
//
//
//            HashMap<Integer, BiReportBrand> brandIdHashMap = brandService.brandIdHashMap(taskId);
//            HashMap<Integer,BiReportCategory> categoryHashMap = categoryService.categoryIdHashMap(taskId);
//
//            if (CollectionUtils.isNotEmpty(biReportSkuList)){
//                for (BiReportSku biReportSku : biReportSkuList){
//                    ReportSkuListItem skuListItem = new ReportSkuListItem();
//                    CglibCopyBeanUtil.basicCopyBean(biReportSku, skuListItem);
//                    skuListItem.setId(biReportSku.getTreeId());
//                    skuListItem.setBrandId(biReportSku.getBandId());
//
//                    BiReportBrand brand = brandIdHashMap.get(biReportSku.getBandId());
//                    BiReportCategory category = categoryHashMap.get(biReportSku.getCategoryId());
//
//                    skuListItem.setBrand(Constant.LINE_HORIZ);
//                    if (brand != null){
//                        skuListItem.setBrand(brand.getName());
//                    }
//
//                    skuListItem.setCategory(Constant.LINE_HORIZ);
//                    if (category != null){
//                        skuListItem.setCategory(category.getName());
//                    }
//                    skuListItem.setSkuType(biReportSku.getType());
//                    skuListItem.setSkuTypeDes(BiReportSkuTypeEnum.getDesByTypeValue(biReportSku.getType()));
//                    if (biReportSku.getType().intValue() == BiReportSkuTypeEnum.UNCATELOGUED.getValue().intValue()){
//                        skuListItem.setSkuTypeDes(Constant.LINE_HORIZ);
//                    }
//
//                    skuListItems.add(skuListItem);
//                }
//            }
//
//            if (CollectionUtils.isNotEmpty(imageProductTreeList)){
//                for (ImageProductTree imageProductTree : imageProductTreeList){
//                    Integer treeId = Integer.valueOf(imageProductTree.getId());
//                    skuIdListInRecognition.add(treeId);
//                    // 如果该sku未设置到报表，需新增
//                    if (!skuIdListInReport.contains(treeId)){
//                        BiReportSku record = new BiReportSku();
//                        record.setTaskId(targetListReq.getTaskId());
//                        record.setTreeId(treeId);
//                        record.setName(imageProductTree.getName());
//                        record.setType(0);
//
//                        biReportSkuMapper.insertSelective(record);
//
//
//                        ReportSkuListItem skuListItem = new ReportSkuListItem();
//                        CglibCopyBeanUtil.basicCopyBean(imageProductTree, skuListItem);
//                        skuListItem.setId(treeId);
//
//                        skuListItems.add(skuListItem);
//                    }
//                }
//            }
//            // 已经删除的sku
//            List<Integer> skuIdListInReportDelete = new ArrayList<>();
//            for (Integer skuIdInReport : skuIdListInReport){
//                if (!skuIdListInRecognition.contains(skuIdInReport)) {
//                    skuIdListInReportDelete.add(skuIdInReport);
//                }
//            }
//            // 返回数据删除
//            for (int n = (skuListItems.size() - 1); n >= 0; n--) {
//                ReportSkuListItem  skuItem = skuListItems.get(n);
//                if (skuIdListInReportDelete.contains(skuItem.getId())){
//                    skuListItems.remove(skuItem);
//                }
//            }
//
//            // 库中删除
//            if (CollectionUtils.isNotEmpty(skuIdListInReportDelete)) {
//                BiReportSkuExample example = new BiReportSkuExample();
//                example.createCriteria().andTaskIdEqualTo(taskId).andTreeIdIn(skuIdListInReportDelete);
//
//                biReportSkuMapper.deleteByExample(example);
//            }
//
//            return ResponseData.success().data(new CommonListRet<>(skuListItems));
//        } catch (Exception ex) {
//            LOGGER.error("getTargetSkuList", ex);
//        }
//        return ResponseData.failure();
//    }
//
//    /**
//     * 下载sku excel模板
//     * @param taskId 任务id
//     * @param response http响应
//     */
//    @Deprecated
//    public void downloadSkuExcel(String taskId, HttpServletResponse response) {
//
//        Preconditions.checkArgument(taskId != null, "缺少参数");
//
//        BiReportSkuExample biReportSkuExample = new BiReportSkuExample();
//        biReportSkuExample.createCriteria().andTaskIdEqualTo(taskId);
//        List<BiReportSku> biReportSkuList = biReportSkuMapper.selectByExample(biReportSkuExample);
//
//        List<ReportSkuExcelItem> reportSkuExcelItemList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(biReportSkuList)){
//            String fileName = taskId + "SKUlist";
//            String sheetName = "sheet1";
//
//            for (BiReportSku biReportSku : biReportSkuList){
//                ReportSkuExcelItem excelItem = new ReportSkuExcelItem();
//                CglibCopyBeanUtil.basicCopyBean(biReportSku, excelItem);
//
//                reportSkuExcelItemList.add(excelItem);
//            }
//            try {
//                ExcelUtils.exportExcel(reportSkuExcelItemList, null, sheetName, ReportSkuExcelItem.class, fileName, response);
//            } catch (IOException e) {
//                LOGGER.error("exportExcel",e);
//            }
//        }
//    }
//
//    /**
//     * 上传Excel
//     * @param taskId 任务id
//     * @param file 文件
//     */
//    @Deprecated
//    public ResponseData uploadSkuExcel(String taskId, MultipartFile file){
//        Preconditions.checkArgument(!Strings.isNullOrEmpty(taskId), "缺少参数");
//
//        try {
//            List<ReportSkuExcelItem> reportSkuExcelItemList = ExcelUtils.importExcel(file, ReportSkuExcelItem.class);
//            if (CollectionUtils.isEmpty(reportSkuExcelItemList)){
//                return ResponseData.failure().msg("未解析出数据!");
//            }
//            // 已经设置报表的sku
//            // 识别目标sku id
//            List<Integer> treeIdList = getSkuIdByTaskId(taskId);
//            List<Integer> brandIdList = getBrandIdByTaskId(taskId);
//            List<Integer> categoryIdList = getCategoryIdByTaskId(taskId);
//
//            String errorMsg = checkSkuExcelData(reportSkuExcelItemList, taskId, treeIdList, brandIdList, categoryIdList);
//            // 校验失败
//            if (!Strings.isNullOrEmpty(errorMsg)){
//                return ResponseData.failure().msg(errorMsg);
//            }
//            for (ReportSkuExcelItem skuExcelItem : reportSkuExcelItemList){
//                BiReportSku biReportSkuRecord = new BiReportSku();
//                CglibCopyBeanUtil.basicCopyBean(skuExcelItem, biReportSkuRecord);
//
//                BiReportSkuExample biReportSkuExample = new BiReportSkuExample();
//                biReportSkuExample.createCriteria().andTaskIdEqualTo(skuExcelItem.getTaskId()).andTreeIdEqualTo(skuExcelItem.getTreeId());
//
//                biReportSkuMapper.updateByExampleSelective(biReportSkuRecord, biReportSkuExample);
//            }
//            LOGGER.info(JsonUtil.toJsonString(reportSkuExcelItemList));
//
//            return ResponseData.success();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return ResponseData.failure();
//    }
//
//    /**
//     * 品类新增修改
//     * @param skuCategoryReq
//     * @return
//     */
//    public ResponseData categoryUpdate(SkuCategoryReq skuCategoryReq){
//        Preconditions.checkArgument(skuCategoryReq !=null && skuCategoryReq.getCategoryName() !=null, "缺少参数");
//
//        try {
//            BiReportCategoryExample example = new BiReportCategoryExample();
//
//            BiReportCategory biReportCategory = new BiReportCategory();
//            biReportCategory.setName(skuCategoryReq.getCategoryName());
//            biReportCategory.setTaskId(skuCategoryReq.getTaskId());
//            if (skuCategoryReq.getId() != 0){
//                example.createCriteria().andTaskIdEqualTo(skuCategoryReq.getTaskId()).andNameEqualTo(skuCategoryReq.getCategoryName())
//                        .andIdNotEqualTo(skuCategoryReq.getId());
//                List<BiReportCategory> biReportCategoryList = biReportCategoryMapper.selectByExample(example);
//                if (CollectionUtils.isNotEmpty(biReportCategoryList)) {
//                    return ResponseData.failure().msg("品类不可重复！");
//                }
//
//                //修改
//                biReportCategory.setId(skuCategoryReq.getId());
//                BiReportCategoryExample biReportCategoryExample = new BiReportCategoryExample();
//                biReportCategoryExample.createCriteria().andIdEqualTo(skuCategoryReq.getId());
//
//                biReportCategoryMapper.updateByExampleSelective(biReportCategory, biReportCategoryExample);
//            } else {
//                example.createCriteria().andTaskIdEqualTo(skuCategoryReq.getTaskId()).andNameEqualTo(skuCategoryReq.getCategoryName());
//                List<BiReportCategory> biReportCategoryList = biReportCategoryMapper.selectByExample(example);
//                if (CollectionUtils.isNotEmpty(biReportCategoryList)) {
//                    return ResponseData.failure().msg("品类不可重复！");
//                }
//                //新增
//                biReportCategoryMapper.insertSelective(biReportCategory);
//            }
//            return ResponseData.success();
//        } catch (Exception ex){
//            LOGGER.error("categoryUpdate",ex);
//        }
//        return ResponseData.failure();
//
//    }
//    /**
//     * 品类删除
//     * @param delReq
//     * @return
//     */
//    public ResponseData categoryDel(SkuBrandCategoryDelReq delReq){
//        Preconditions.checkArgument(delReq !=null && delReq.getId() != 0, "缺少参数");
//        try {
//            BiReportSkuExample biReportSkuExample = new BiReportSkuExample();
//            biReportSkuExample.createCriteria().andCategoryIdEqualTo(delReq.getId());
//            List<BiReportSku> biReportSkuList = biReportSkuMapper.selectByExample(biReportSkuExample);
//            if (CollectionUtils.isNotEmpty(biReportSkuList)){
//                return ResponseData.failure().msg("该品类仍在使用中，不可删除！");
//            }
//
//            BiReportCategoryExample example = new BiReportCategoryExample();
//            example.createCriteria().andIdEqualTo(delReq.getId());
//            biReportCategoryMapper.deleteByExample(example);
//            return ResponseData.success();
//        } catch (Exception ex){
//            LOGGER.error("categoryDel",ex);
//        }
//        return ResponseData.failure();
//    }
//
//    /**
//     * 品类列表
//     * @param taskIdReq
//     * @return
//     */
//    public ResponseData categoryList(CommonTaskIdReq taskIdReq){
//        Preconditions.checkArgument(taskIdReq != null && !Strings.isNullOrEmpty(taskIdReq.getTaskId()), "缺少参数");
//        try {
//            BiReportCategoryExample example = new BiReportCategoryExample();
//            example.createCriteria().andTaskIdEqualTo(taskIdReq.getTaskId());
//
//            List<BiReportCategory> categoryList = biReportCategoryMapper.selectByExample(example);
//            return ResponseData.success().data(new CommonListRet<>(categoryList));
//        } catch (Exception ex){
//            LOGGER.error("categoryList",ex);
//        }
//        return ResponseData.failure();
//    }
//
//
//    /**
//     * 品牌列表
//     * @param taskIdReq
//     * @return
//     */
//    public ResponseData brandList(CommonTaskIdReq taskIdReq){
//        Preconditions.checkArgument(taskIdReq != null && !Strings.isNullOrEmpty(taskIdReq.getTaskId()), "缺少参数");
//        try {
//            BiReportBrandExample example = new BiReportBrandExample();
//            example.createCriteria().andTaskIdEqualTo(taskIdReq.getTaskId());
//
//            List<BiReportBrand> brandList = biReportBrandMapper.selectByExample(example);
//            return ResponseData.success().data(new CommonListRet<>(brandList));
//        } catch (Exception ex){
//            LOGGER.error("brandList",ex);
//        }
//        return ResponseData.failure();
//    }
//
//
//    /**
//     * 品类删除
//     * @param delReq
//     * @return
//     */
//    public ResponseData brandDel(SkuBrandCategoryDelReq delReq){
//        Preconditions.checkArgument(delReq !=null && delReq.getId() != 0, "缺少参数");
//        try {
//            BiReportSkuExample biReportSkuExample = new BiReportSkuExample();
//            biReportSkuExample.createCriteria().andBandIdEqualTo(delReq.getId());
//            List<BiReportSku> biReportSkuList = biReportSkuMapper.selectByExample(biReportSkuExample);
//            if (CollectionUtils.isNotEmpty(biReportSkuList)){
//                return ResponseData.failure().msg("该品牌仍在使用中，不可删除！");
//            }
//
//            BiReportBrandExample example = new BiReportBrandExample();
//            example.createCriteria().andIdEqualTo(delReq.getId());
//            biReportBrandMapper.deleteByExample(example);
//
//            return ResponseData.success();
//        } catch (Exception ex){
//            LOGGER.error("brandDel",ex);
//        }
//
//        return ResponseData.failure();
//    }
//
//    /**
//     * 品牌更新或新增
//     * @param skuBrandReq
//     * @return
//     */
//    public ResponseData brandUpdate(SkuBrandReq skuBrandReq){
//        Preconditions.checkArgument(skuBrandReq !=null && !Strings.isNullOrEmpty(skuBrandReq.getBrandName()) && !Strings.isNullOrEmpty(skuBrandReq.getTaskId()), "缺少参数");
//
//        try{
//            BiReportBrandExample example = new BiReportBrandExample();
//
//            BiReportBrand biReportBrand = new BiReportBrand();
//            biReportBrand.setName(skuBrandReq.getBrandName());
//            biReportBrand.setTaskId(skuBrandReq.getTaskId());
//
//            if (skuBrandReq.getId() != 0){
//                example.createCriteria().andTaskIdEqualTo(skuBrandReq.getTaskId()).andNameEqualTo(skuBrandReq.getBrandName())
//                        .andIdNotEqualTo(skuBrandReq.getId());
//                List<BiReportBrand> biReportBrandList = biReportBrandMapper.selectByExample(example);
//                if (CollectionUtils.isNotEmpty(biReportBrandList)) {
//                    return ResponseData.failure().msg("品牌不可重复！");
//                }
//                //修改
//                biReportBrand.setId(skuBrandReq.getId());
//
//                BiReportBrandExample biReportBrandExample = new BiReportBrandExample();
//                biReportBrandExample.createCriteria().andTaskIdEqualTo(skuBrandReq.getTaskId()).andIdEqualTo(skuBrandReq.getId());
//                biReportBrandMapper.updateByExampleSelective(biReportBrand, biReportBrandExample);
//
//            } else {
//                example.createCriteria().andTaskIdEqualTo(skuBrandReq.getTaskId()).andNameEqualTo(skuBrandReq.getBrandName());
//                List<BiReportBrand> biReportBrandList = biReportBrandMapper.selectByExample(example);
//                if (CollectionUtils.isNotEmpty(biReportBrandList)) {
//                    return ResponseData.failure().msg("品牌不可重复！");
//                }
//
//                //新增
//                biReportBrandMapper.insertSelective(biReportBrand);
//            }
//            return ResponseData.success();
//        } catch (Exception ex){
//            LOGGER.error("brandUpdate",ex);
//        }
//
//        return ResponseData.failure();
//
//    }
//
//    /**
//     * sku更新
//     * @param updateReq
//     * @return
//     */
//    public ResponseData skuUpdate(SkuUpdateReq updateReq){
//        Preconditions.checkArgument(updateReq != null && updateReq.getId() != 0 && !Strings.isNullOrEmpty(updateReq.getTaskId()), "缺少参数");
//
//        List<Integer> skuTypeList = Arrays.asList(BiReportSkuTypeEnum.UNCATELOGUED.getValue(), BiReportSkuTypeEnum.SELF_PRODUCT.getValue(), BiReportSkuTypeEnum.COMP_PRODUCT.getValue(), BiReportSkuTypeEnum.MUST_PRODUCT.getValue());
//        final String errorMsg = "本品/竞品中只能填0或1或2或3！";
//
//        if (!skuTypeList.contains(updateReq.getSkuType())){
//            return ResponseData.failure().msg(errorMsg);
//        }
//        try {
//            BiReportSku reportSkuRecord = new BiReportSku();
//            reportSkuRecord.setBandId(updateReq.getBrandId());
//            reportSkuRecord.setCategoryId(updateReq.getCategoryId());
//            reportSkuRecord.setType(updateReq.getSkuType());
//
//            BiReportSkuExample example = new BiReportSkuExample();
//            example.createCriteria().andTreeIdEqualTo(updateReq.getId()).andTaskIdEqualTo(updateReq.getTaskId());
//
//            biReportSkuMapper.updateByExampleSelective(reportSkuRecord, example);
//
//            return ResponseData.success();
//        } catch (Exception ex){
//            LOGGER.error("brandDel",ex);
//        }
//
//        return ResponseData.failure();
//    }
//
//    /**
//     *
//     * @param detailReq 请求信息
//     * @return
//     */
//    public ResponseData reportDetail(BiReportDetailReq detailReq){
//        Preconditions.checkArgument(detailReq !=null && !Strings.isNullOrEmpty(detailReq.getResponseId()) , "缺少参数");
//
//        String responseId = detailReq.getResponseId();
//        SimpleDateFormat dateformatterSdf = DateUtil.getSimpleDateFormat(DateUtil.DTFormat.yyyy_MM_dd.getFormat());
//
//        DateTimeFormatter dateFormatDay = DateTimeFormatter.ofPattern(DateUtil.DTFormat.yyyy_MM_dd.getFormat());
//        DateTimeFormatter dateFormatHourTime = DateTimeFormatter.ofPattern(DateUtil.DTFormat.HH_mm_ss.getFormat());
//        try {
//            LambdaQueryWrapper<TResponse> responseLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            responseLambdaQueryWrapper.eq(TResponse::getId, responseId);
//            TResponse tResponse = responseMapper.selectOne(responseLambdaQueryWrapper);
//            if (tResponse == null){
//                return ResponseData.failure().msg("查不到该答卷");
//            }
//
//            LambdaQueryWrapper<BiStoreReport> relationWrapper = new LambdaQueryWrapper<>();
//            relationWrapper.eq(BiStoreReport::getResponseId, detailReq.getResponseId());
//            relationWrapper.orderByAsc(BiStoreReport::getCategoryId);
//
//            List<BiStoreReport> selectList = biStoreReportMapper.selectList(relationWrapper);
//            //查询地址
//            LambdaQueryWrapper<TTasklaunch> tasklaunchLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            tasklaunchLambdaQueryWrapper.eq(TTasklaunch::getTaskid, tResponse.getTaskid());
//            TTasklaunch tasklaunch = tasklaunchMapper.selectOne(tasklaunchLambdaQueryWrapper);
//
//            if (CollectionUtils.isNotEmpty(selectList)){
//                BiReportDetailResp detailResp = new BiReportDetailResp();
//                BiStoreReport biStoreReportBasic = selectList.get(0);
//
//                String enterDate = dateTimeFormat(dateFormatDay, biStoreReportBasic.getEnterTime());
//                String leaveDate = dateTimeFormat(dateFormatDay, biStoreReportBasic.getLeaveTime());
//
//                detailResp.setEnterTime(dateTimeFormat(dateFormatHourTime, biStoreReportBasic.getEnterTime()));
//                detailResp.setVisitDate(dateTimeFormat(dateFormatDay, biStoreReportBasic.getEnterTime()));
//                detailResp.setLeaveTime(dateTimeFormat(dateFormatHourTime, biStoreReportBasic.getLeaveTime()));
//                detailResp.setAccount(tResponse.getPhone());
//                detailResp.setStoreName(tasklaunch.getReallyAddress() == null ? "" : tasklaunch.getReallyAddress());
//                detailResp.setOperatingTime(DateUtil.intToStringDuration(Math.abs(biStoreReportBasic.getOperatingTime())));
//                // 按跨天计算
//                detailResp.setOtherDayNum(DateUtil.diffDay(dateformatterSdf.parse(enterDate), dateformatterSdf.parse(leaveDate)));
//
//                detailResp.setTabList(initTabList(responseId, tResponse.getTaskidOwner(), tResponse.getPhone()));
//                detailResp.setKpiGroupList(initKpiGroup(selectList));
//
//                detailResp.setSkuGroupList(initSkuGroup(responseId));
//
//                return ResponseData.success().data(detailResp);
//            } else {
//                return ResponseData.failure().msg("暂无结果");
//            }
//        } catch (Exception ex){
//            LOGGER.error("reportDetail", ex);
//        }
//        return ResponseData.failure();
//    }
//
//    /**
//     * 初始化标签页
//     * @param responseId 答卷id
//     * @param taskId 任务id
//     * @param account 账号
//     * @return
//     */
//    private List<BiReportDetailTab> initTabList(String responseId, String taskId, String account){
//        List<BiReportDetailTab> biReportDetailTabList = new ArrayList<>();
//
//        BiReportDetailTab tabDetail = new BiReportDetailTab();
//        tabDetail.setTabText("详情");
//        tabDetail.setTabUrl("DETAIL");
//        biReportDetailTabList.add(tabDetail);
//
//        BiReportDetailTab tabRecongnition = new BiReportDetailTab();
//        tabRecongnition.setTabText("识别明细");
//        tabRecongnition.setTabUrl(recongnitionDetailServiceUrl.replace(Constant.REPLACER_RESPONSE_ID, responseId));
//        biReportDetailTabList.add(tabRecongnition);
//
//        BiReportDetailTab tabAppeal = new BiReportDetailTab();
//        tabAppeal.setTabText("申诉页面");
//        tabAppeal.setTabUrl(appealServiceUrl.replace(Constant.REPLACER_RESPONSE_ID, responseId).replace(Constant.REPLACER_TASK_ID, taskId).replace(Constant.REPLACER_ACCOUNT, account));
//        biReportDetailTabList.add(tabAppeal);
//
//        BiReportDetailTab tabHeap = new BiReportDetailTab();
//        tabHeap.setTabText("地堆页面");
//        tabHeap.setTabUrl(heapServiceUrl.replace(Constant.REPLACER_RESPONSE_ID, responseId).replace(Constant.REPLACER_TASK_ID, taskId).replace(Constant.REPLACER_ACCOUNT, account));
//        biReportDetailTabList.add(tabHeap);
//
//        return biReportDetailTabList;
//    }
//
//    /**
//     * 解析kpi分组信息
//     * @param biStoreReportList
//     * @return
//     */
//    private List<BiReportDetailKpiGroup> initKpiGroup(List<BiStoreReport> biStoreReportList){
//        if (CollectionUtils.isEmpty(biStoreReportList)){
//            return new ArrayList<>();
//        }
//        List<BiReportDetailKpiGroup> biReportDetailKpiGroupList = new ArrayList<>();
//        for (BiStoreReport biStoreReport: biStoreReportList){
//            BiReportDetailKpiGroup kpiGroup = new BiReportDetailKpiGroup();
//            kpiGroup.setGroupName(biStoreReport.getCategory());
//
//            //保留小数
//            biStoreReport.setDistRate(Arith.round(biStoreReport.getDistRate(), roundScale));
//            biStoreReport.setCompetingProductSos(Arith.round(biStoreReport.getCompetingProductSos(), roundScale));
//            biStoreReport.setProductSos(Arith.round(biStoreReport.getProductSos(), roundScale));
//
//            //缺货率= 1 - 必备分销率
//            double outOfStockRate = Arith.sub(1, biStoreReport.getDistRate().doubleValue());
//            biStoreReport.setOutOfStockRate(Arith.round(Arith.doubleToBigDecimal(outOfStockRate), roundScale));
//
//            CglibCopyBeanUtil.basicCopyBean(biStoreReport, kpiGroup);
//
//
//            biReportDetailKpiGroupList.add(kpiGroup);
//        }
//        return biReportDetailKpiGroupList;
//    }
//
//    /**
//     * sku分组信息
//     * @param responseId 答卷Id
//     * @return
//     */
//    private List<BiReportDetailSkuGroup> initSkuGroup(String responseId){
//        List<BiReportDetailSkuGroup> biReportDetailSkuGroupList = new ArrayList<>();
//
//        LambdaQueryWrapper<BiStoreDetailReport> relationWrapper = new LambdaQueryWrapper<>();
//        relationWrapper.eq(BiStoreDetailReport::getResponseId, responseId);
//        relationWrapper.orderByAsc(BiStoreDetailReport::getBrand).orderByAsc(BiStoreDetailReport::getCategory);
//
//        List<BiStoreDetailReport> detailReportList = biStoreDetailReportMapper.selectList(relationWrapper);
//        if (CollectionUtils.isNotEmpty(detailReportList)){
//            // 产品id和名称映射
//            Map<Integer, String> productIdToNameMap = new HashMap<>();
////            List<Integer> productIdList = detailReportList.stream().map(BiStoreDetailReport::getProductId).collect(Collectors.toList());
////            if (CollectionUtils.isNotEmpty(productIdList)){
////                List<DisplayProduct> displayProductList = imageProductTreeMapper.getDisplayProductByProductIdList(productIdList);
////                if (CollectionUtils.isNotEmpty(displayProductList)){
////                    productIdToNameMap = displayProductList.stream().collect(Collectors.toMap(DisplayProduct::getDisplayId, DisplayProduct::getName));
////                }
////            }
//            BiReportDetailSkuGroup skuGroupMust = new BiReportDetailSkuGroup();
//            skuGroupMust.setGroupName("必备本品SKU表");
//            skuGroupMust.setSkuList(getDetailReportListByType(detailReportList, BiReportSkuTypeEnum.MUST_PRODUCT.getValue(), productIdToNameMap));
//            if (CollectionUtils.isNotEmpty(skuGroupMust.getSkuList())){
//                biReportDetailSkuGroupList.add(skuGroupMust);
//            }
//            BiReportDetailSkuGroup skuGroupSelf = new BiReportDetailSkuGroup();
//            skuGroupSelf.setGroupName("非必备本品SKU表");
//            skuGroupSelf.setSkuList(getDetailReportListByType(detailReportList, BiReportSkuTypeEnum.SELF_PRODUCT.getValue(), productIdToNameMap));
//            if (CollectionUtils.isNotEmpty(skuGroupSelf.getSkuList())){
//                biReportDetailSkuGroupList.add(skuGroupSelf);
//            }
//
//            BiReportDetailSkuGroup skuGroupComp = new BiReportDetailSkuGroup();
//            skuGroupComp.setGroupName("竞品SKU表");
//            skuGroupComp.setSkuList(getDetailReportListByType(detailReportList, BiReportSkuTypeEnum.COMP_PRODUCT.getValue(), productIdToNameMap));
//            if (CollectionUtils.isNotEmpty(skuGroupComp.getSkuList())){
//                biReportDetailSkuGroupList.add(skuGroupComp);
//            }
//
//            BiReportDetailSkuGroup skuGroupUnCatelogued = new BiReportDetailSkuGroup();
//            skuGroupUnCatelogued.setGroupName("-");
//            skuGroupUnCatelogued.setSkuList(getDetailReportListByType(detailReportList, BiReportSkuTypeEnum.UNCATELOGUED.getValue(), productIdToNameMap));
//            if (CollectionUtils.isNotEmpty(skuGroupUnCatelogued.getSkuList())){
//                biReportDetailSkuGroupList.add(skuGroupUnCatelogued);
//            }
//        }
//        return biReportDetailSkuGroupList;
//    }
//
//    /**
//     * 查找对应的sku
//     * @param detailReportList
//     * @param skuType  BiReportSkuTypeEnum
//     * @return
//     */
//    private List<BiReportDetailSkuItem> getDetailReportListByType(List<BiStoreDetailReport> detailReportList, int skuType, Map<Integer, String> productIdToNameMap){
//        if (CollectionUtils.isEmpty(detailReportList)){
//            return null;
//        }
//        List<BiReportDetailSkuItem> biReportDetailSkuItemArrayList = new ArrayList<>();
//        for (BiStoreDetailReport detailReport : detailReportList){
//            if (detailReport.getProductCheckType() == skuType){
//                Integer productId = detailReport.getProductId();
//                BiReportDetailSkuItem detailSkuItem = new BiReportDetailSkuItem();
//                detailSkuItem.setBrandCategoryName(detailReport.getBrand()+ "/" + detailReport.getCategory());
//                detailSkuItem.setBrand(detailReport.getBrand());
//                detailSkuItem.setCategory(detailReport.getCategory());
//                detailSkuItem.setExist(detailReport.getIsExist() == 1);
//                detailSkuItem.setSkuName(detailReport.getProductName());
//                detailSkuItem.setFacing(detailReport.getFacing());
//                detailSkuItem.setProductName(detailReport.getProductName());
//
////                // 商品名称
////                String productName = productIdToNameMap.get(productId);
////                if (!Strings.isNullOrEmpty(productName)){
////                    detailSkuItem.setProductName(productName);
////                }
//
//                biReportDetailSkuItemArrayList.add(detailSkuItem);
//            }
//        }
//        return biReportDetailSkuItemArrayList;
//    }
//
//    /**
//     * 格式化日期
//     * @param dateTimeFormatter
//     * @param localDateTime
//     * @return
//     */
//    private String dateTimeFormat(DateTimeFormatter dateTimeFormatter, LocalDateTime localDateTime){
//        if (localDateTime == null || dateTimeFormatter == null){
//            return null;
//        }
//        return dateTimeFormatter.format(localDateTime);
//    }
//
//    /**
//     * 校验Excel数据
//     * @param reportSkuExcelItemList
//     * @param taskId
//     * @param treeIdList
//     * @param brandIdList
//     * @param categoryIdList
//     * @return
//     */
//    private String checkSkuExcelData(List<ReportSkuExcelItem> reportSkuExcelItemList, String taskId, List<Integer> treeIdList, List<Integer> brandIdList, List<Integer> categoryIdList){
//        String errorMsg = null;
//        String errorBandCategory = "品牌与品类只能填已经配置的序号！";
//        List<Integer> skuTypeList = Arrays.asList(BiReportSkuTypeEnum.UNCATELOGUED.getValue(), BiReportSkuTypeEnum.SELF_PRODUCT.getValue(), BiReportSkuTypeEnum.COMP_PRODUCT.getValue(), BiReportSkuTypeEnum.MUST_PRODUCT.getValue());
//        for (ReportSkuExcelItem skuExcelItem : reportSkuExcelItemList){
//            if (Strings.isNullOrEmpty(skuExcelItem.getTaskId()) || skuExcelItem.getTreeId() == null){
//                errorMsg = "部分SKU的必填项缺失！";
//                break;
//            }
//            if (!taskId.equals(skuExcelItem.getTaskId())){
//                errorMsg = "部分行的taskid不属于本任务！";
//                break;
//            }
//            if (!treeIdList.contains(skuExcelItem.getTreeId())){
//                errorMsg = "部分行SKU未在本任务设置！";
//                break;
//            }
//            // 为空校验
//            if (skuExcelItem.getBandId() == null || skuExcelItem.getCategoryId() == null){
//                errorMsg = errorBandCategory;
//                break;
//            }
//            if (skuExcelItem.getBandId().intValue() != 0 && !brandIdList.contains(skuExcelItem.getBandId())){
//                errorMsg = errorBandCategory;
//                break;
//            }
//            if (skuExcelItem.getCategoryId().intValue() != 0 && !categoryIdList.contains(skuExcelItem.getCategoryId())){
//                errorMsg = errorBandCategory;
//                break;
//            }
//            if (skuExcelItem.getType() == null || !skuTypeList.contains(skuExcelItem.getType())){
//                errorMsg = "本品/竞品中只能填0或1或2或3！";
//            }
//        }
//        return errorMsg;
//    }
//
//
//    /**
//     * 获取设置的报表sku的sku id集合
//     * @param taskId 任务id
//     * @return
//     */
//    private List<Integer> getSkuIdByTaskId(String taskId){
//        // 识别目标sku id
//        List<Integer> treeIdList = new ArrayList<>();
//        List<ImageProductTree> imageProductTreeList = imageProductTreeMapper.getImageProductTreeListByTaskId(taskId);
//        if (CollectionUtils.isNotEmpty(imageProductTreeList)){
//            for (ImageProductTree imageProductTree : imageProductTreeList){
//                Integer treeId = Integer.valueOf(imageProductTree.getId());
//                treeIdList.add(treeId);
//            }
//        }
//        return treeIdList;
//    }
//
//    /**
//     * 获取设置的报表品牌id集合
//     * @param taskId 任务id
//     * @return
//     */
//    private List<Integer> getBrandIdByTaskId(String taskId){
//        // 品牌id
//        List<Integer> brandIdList = new ArrayList<>();
//
//        BiReportBrandExample biReportBrandExample = new BiReportBrandExample();
//        biReportBrandExample.createCriteria().andTaskIdEqualTo(taskId);
//        List<BiReportBrand> biReportBrandList = biReportBrandMapper.selectByExample(biReportBrandExample);
//        if (CollectionUtils.isNotEmpty(biReportBrandList)){
//            for (BiReportBrand biReportBrand : biReportBrandList){
//                brandIdList.add(biReportBrand.getId());
//            }
//        }
//
//        return brandIdList;
//    }
//    /**
//     * 获取设置的报表品类id集合
//     * @param taskId 任务id
//     * @return
//     */
//    private List<Integer> getCategoryIdByTaskId(String taskId){
//        // 品类id
//        List<Integer> categoryIdList = new ArrayList<>();
//
//        BiReportCategoryExample biReportCategoryExample = new BiReportCategoryExample();
//        biReportCategoryExample.createCriteria().andTaskIdEqualTo(taskId);
//        List<BiReportCategory> biReportCategoryList = biReportCategoryMapper.selectByExample(biReportCategoryExample);
//        if (CollectionUtils.isNotEmpty(biReportCategoryList)){
//            for (BiReportCategory biReportCategory : biReportCategoryList){
//                categoryIdList.add(biReportCategory.getId());
//            }
//        }
//        return categoryIdList;
//    }
//
//
//    /**
//     * 获取设置的报表sku的sku id集合
//     * @param biReportSkuList
//     * @return
//     */
//    private List<Integer> getSkuIdListInReport(List<BiReportSku> biReportSkuList){
//        if (CollectionUtils.isEmpty(biReportSkuList)){
//            return new ArrayList<>();
//        }
//        List<Integer> skuIdList = new ArrayList<>();
//        for (BiReportSku biReportSku : biReportSkuList){
//            skuIdList.add(biReportSku.getTreeId());
//        }
//
//        return skuIdList;
//    }
//
//    /**
//     * 查询sku信息
//     * @param taskId
//     * @return
//     */
//    private List<BiReportSku> getSkuInReport(String taskId){
//        //查询未设置的报表sku的sku信息
//        BiReportSkuExample biReportSkuExample = new BiReportSkuExample();
//        biReportSkuExample.createCriteria().andTaskIdEqualTo(taskId);
//        return biReportSkuMapper.selectByExample(biReportSkuExample);
//    }
//
//    /**
//     * 查询 SKU 报表信息
//     *
//     * @param getSkuDetailReq
//     * @return
//     */
//    public ResponseData getSkuDetail(GetSkuDetailReq getSkuDetailReq) {
//        GetSkuDetailResp getSkuDetailResp = new GetSkuDetailResp();
//
//        if (Objects.isNull(getSkuDetailReq) || StringUtils.isBlank(getSkuDetailReq.getResponseId())) {
//            return ResponseData.failure().msg("参数错误");
//        }
//
//        if (CollectionUtils.isEmpty(getSkuDetailReq.getImageGroupList())) {
//            return ResponseData.success().data(getSkuDetailResp);
//        }
//        List<GetSkuDetailResp.SkuDetailGroup> skuDetailGroupList = Lists.newArrayList();
//        for (GetSkuDetailReq.ImageGroup imageGroup : getSkuDetailReq.getImageGroupList()) {
//            GetSkuDetailResp.SkuDetailGroup skuDetailGroup = new GetSkuDetailResp.SkuDetailGroup();
//
//            Integer groupNo = imageGroup.getGroupNo();
//            skuDetailGroup.setGroupNo(groupNo);
//
//            if (CollectionUtils.isEmpty(imageGroup.getImageIds())) {
//                skuDetailGroupList.add(skuDetailGroup);
//                continue;
//            }
//
//            LambdaQueryWrapper<TImageProduct> productWrapper = new LambdaQueryWrapper<>();
//            productWrapper.eq(TImageProduct::getResponseId, getSkuDetailReq.getResponseId());
//            productWrapper.in(TImageProduct::getImageId, imageGroup.getImageIds());
//            productWrapper.ne(TImageProduct::getProductId, -500);
//            List<TImageProduct> tImageProductList = tImageProductMapper.selectList(productWrapper);
//
//            if (CollectionUtils.isEmpty(tImageProductList)) {
//                skuDetailGroupList.add(skuDetailGroup);
//                continue;
//            }
//
//            List<Integer> productIdList = tImageProductList.stream().map(TImageProduct::getProductId).distinct().collect(Collectors.toList());
//
//            LambdaQueryWrapper<BiStoreDetailReport> detailWrapper = new LambdaQueryWrapper<>();
//            detailWrapper.eq(BiStoreDetailReport::getResponseId, getSkuDetailReq.getResponseId());
//            List<BiStoreDetailReport> detailList = Lists.newArrayList();
//            detailWrapper.in(BiStoreDetailReport::getProductId, productIdList);
//            try {
//                detailList = biStoreDetailReportMapper.selectList(detailWrapper);
//            } catch (Exception e) {
//                log.error("bi_store_detail_report 查询失败, exception:", e);
//                continue;
//            }
//
//            // 产品id和名称映射
//            Map<Integer, List<DisplayProduct>> productIdToNameMap = Maps.newHashMap();
//            List<DisplayProduct> displayProductList = imageProductTreeMapper.getDisplayProductByProductIdList(productIdList);
//            if (CollectionUtils.isNotEmpty(displayProductList)){
//                productIdToNameMap = displayProductList.stream().collect(Collectors.groupingBy(DisplayProduct::getDisplayId));
//            }
//
//            List<GetSkuDetailResp.SkuDetail> skuList = wrapperSkuList(detailList, productIdToNameMap);
//            skuDetailGroup.setSkuDetailList(skuList);
//            skuDetailGroupList.add(skuDetailGroup);
//        }
//        getSkuDetailResp.setSkuDetailGroupList(skuDetailGroupList);
//
//        return ResponseData.success().data(getSkuDetailResp);
//    }
//
//    /**
//     * 包装sku信息返回结果
//     *
//     * @param detailList
//     * @param productIdToNameMap
//     * @return
//     */
//    private List<GetSkuDetailResp.SkuDetail> wrapperSkuList(List<BiStoreDetailReport> detailList, Map<Integer, List<DisplayProduct>> productIdToNameMap) {
//        List<GetSkuDetailResp.SkuDetail> skuList = Lists.newArrayList();
//        for (BiStoreDetailReport sku : detailList) {
//            GetSkuDetailResp.SkuDetail detail = new GetSkuDetailResp.SkuDetail();
//            detail.setSku(sku.getProductName());
//            detail.setProductName(productIdToNameMap.get(sku.getProductId()).get(0).getName());
//            detail.setBrand(sku.getBrand());
//            detail.setCategory(sku.getCategory());
//            detail.setProductCheckType(BiReportSkuTypeEnum.getDesByTypeValue(sku.getProductCheckType()));
//            detail.setDistribution(sku.getIsExist());
//            detail.setFacing(sku.getFacing());
//            skuList.add(detail);
//        }
//        return skuList;
//    }
//}
