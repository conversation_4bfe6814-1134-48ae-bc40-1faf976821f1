package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lenztech.bi.enterprise.dto.pg.PgRealogramDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgRealogramShelfDTO;
import com.lenztech.bi.enterprise.entity.PgOptimusRealogramDisplay;
import com.lenztech.bi.enterprise.entity.PgOptimusRealogramSequence;
import com.lenztech.bi.enterprise.entity.PgOptimusRealogramShelf;
import com.lenztech.bi.enterprise.mapper.PgOptimusRealogramDisplayMapper;
import com.lenztech.bi.enterprise.mapper.PgOptimusRealogramShelfMapper;
import com.lenztech.bi.enterprise.service.IPgOptimusRealogramDisplayService;
import com.lenztech.bi.enterprise.service.IPgOptimusRealogramSequenceService;
import com.lenztech.bi.enterprise.service.IPgOptimusRealogramShelfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PG Realogram 业务服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class PgRealogramService {

    @Autowired
    private PgOptimusRealogramDisplayMapper pgOptimusRealogramDisplayMapper;

    @Autowired
    private PgOptimusRealogramShelfMapper pgOptimusRealogramShelfMapper;

    @Autowired
    private IPgOptimusRealogramDisplayService realogramDisplayService;

    @Autowired
    private IPgOptimusRealogramShelfService realogramShelfService;

    @Autowired
    private IPgOptimusRealogramSequenceService realogramSequenceService;

    /**
     * 处理日期参数，如果为空则返回当前日期的前一天
     * @param searchDate 查询日期 格式：yyyy-MM-dd
     * @return 处理后的日期字符串
     */
    public String processSearchDate(String searchDate) {
        if (StringUtils.isBlank(searchDate)) {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            return yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return searchDate;
    }

    /**
     * 根据查询条件获取符合条件的record_id列表
     * @param searchDate 查询日期
     * @param responseGroupId group_id
     * @param requestType 请求类型 (display 或 shelf)
     * @return record_id列表
     */
    private List<Long> getValidRecordIds(String searchDate, String responseGroupId, String requestType) {
        // 构建查询条件
        LambdaQueryWrapper<PgOptimusRealogramSequence> queryWrapper = new LambdaQueryWrapper<>();

        // 按日期查询
        if (StringUtils.isNotBlank(searchDate)) {
//            queryWrapper.apply("DATE(insert_time) = {0}", searchDate);
            queryWrapper.eq(PgOptimusRealogramSequence::getExecDate, searchDate);
        }

        // 按responseGroupId查询
        if (StringUtils.isNotBlank(responseGroupId)) {
            queryWrapper.eq(PgOptimusRealogramSequence::getResponseGroupId, responseGroupId);
        }

        // 按请求类型查询
        if (StringUtils.isNotBlank(requestType)) {
            queryWrapper.eq(PgOptimusRealogramSequence::getRequestType, requestType);
        }

        // 只查询需要保留且处理成功的记录
        queryWrapper.eq(PgOptimusRealogramSequence::getIsRemainedScc, 1)
                   .eq(PgOptimusRealogramSequence::getProcessStatus, 1);

        // 只查询id字段
        queryWrapper.select(PgOptimusRealogramSequence::getId);

        List<PgOptimusRealogramSequence> sequences = realogramSequenceService.list(queryWrapper);
        return sequences.stream().map(PgOptimusRealogramSequence::getId).collect(Collectors.toList());
    }

    /**
     * 分页查询 Realogram Display 数据
     * @param searchDate 查询日期
     * @param responseId 答卷ID
     * @param storeCode 门店编码
     * @param category 品类名称
     * @param brand 品牌名称
     * @param skuName 产品名称
     * @param eanCode EAN产品编码
     * @param isPgProductFlag 是否是宝洁
     * @param visitYear 拜访年份
     * @param visitMonth 拜访月份
     * @param visitWeek 拜访周数
     * @param sceneType 货架品类
     * @param oos 是否缺货
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public IPage<PgRealogramDisplayDTO> getRealogramDisplayPageList(String searchDate, String responseId, String responseGroupId, String storeCode,
                                                                   String category, String brand, String skuName, String eanCode,
                                                                   Boolean isPgProductFlag, String visitYear, String visitMonth,
                                                                   String visitWeek, String sceneType, Boolean oos,
                                                                   Integer pageNum, Integer pageSize) {
        
        // 设置默认分页参数
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }

        // 处理查询日期
        String processedDate = processSearchDate(searchDate);

        // 先从序列表获取符合条件的record_id列表
        // 优先使用responseGroupId，如果没有则使用responseId（向后兼容）
        String groupId = StringUtils.isNotBlank(responseGroupId) ? responseGroupId : responseId;
        List<Long> validRecordIds = getValidRecordIds(processedDate, groupId, "");

        // 如果没有符合条件的记录，返回空结果
        if (validRecordIds.isEmpty()) {
            return new Page<>(pageNum, pageSize);
        }

        // 构建查询条件
        LambdaQueryWrapper<PgOptimusRealogramDisplay> queryWrapper = new LambdaQueryWrapper<>();

        // 使用id in (xxx, xxx, xxx)的方式查询
        queryWrapper.in(PgOptimusRealogramDisplay::getRecordId, validRecordIds);
        if (StringUtils.isNotBlank(storeCode)) {
            queryWrapper.like(PgOptimusRealogramDisplay::getStoreCode, storeCode);
        }
        if (StringUtils.isNotBlank(category)) {
            queryWrapper.like(PgOptimusRealogramDisplay::getCategory, category);
        }
        if (StringUtils.isNotBlank(brand)) {
            queryWrapper.like(PgOptimusRealogramDisplay::getBrand, brand);
        }
        if (StringUtils.isNotBlank(skuName)) {
            queryWrapper.like(PgOptimusRealogramDisplay::getSkuName, skuName);
        }
        if (StringUtils.isNotBlank(eanCode)) {
            queryWrapper.eq(PgOptimusRealogramDisplay::getEanCode, eanCode);
        }
        if (isPgProductFlag != null) {
            queryWrapper.eq(PgOptimusRealogramDisplay::getIsPgProductFlag, isPgProductFlag);
        }
        if (StringUtils.isNotBlank(visitYear)) {
            queryWrapper.eq(PgOptimusRealogramDisplay::getVisitYear, visitYear);
        }
        if (StringUtils.isNotBlank(visitMonth)) {
            queryWrapper.eq(PgOptimusRealogramDisplay::getVisitMonth, visitMonth);
        }
        if (StringUtils.isNotBlank(visitWeek)) {
            queryWrapper.eq(PgOptimusRealogramDisplay::getVisitWeek, visitWeek);
        }
        if (StringUtils.isNotBlank(sceneType)) {
            queryWrapper.eq(PgOptimusRealogramDisplay::getSceneType, sceneType);
        }
        if (oos != null) {
            queryWrapper.eq(PgOptimusRealogramDisplay::getOos, oos);
        }

        // 按访问时间倒序排序
        queryWrapper.orderByDesc(PgOptimusRealogramDisplay::getId);

        // 执行分页查询
        Page<PgOptimusRealogramDisplay> page = new Page<>(pageNum, pageSize);
        IPage<PgOptimusRealogramDisplay> resultPage = realogramDisplayService.page(page, queryWrapper);

        // 转换为DTO对象
        IPage<PgRealogramDisplayDTO> dtoPage = resultPage.convert(entity -> {
            PgRealogramDisplayDTO dto = new PgRealogramDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }
            if ("1".equals(dto.getSmartAttribute())){
                dto.setSmartAttribute("stock");
            }
            if ("0".equals(dto.getSmartAttribute())){
                dto.setSmartAttribute("");
            }

            return dto;
        });

        return dtoPage;
    }

    /**
     * 分页查询 Realogram Shelf 数据
     * @param searchDate 查询日期
     * @param responseId 答卷ID
     * @param storeCode 门店编码
     * @param category 品类名称
     * @param brand 品牌名称
     * @param skuName 产品名称
     * @param eanCode EAN产品编码
     * @param isPgProductFlag 是否是宝洁
     * @param visitYear 拜访年份
     * @param visitMonth 拜访月份
     * @param visitWeek 拜访周数
     * @param sceneType 货架品类
     * @param oos 是否缺货
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public IPage<PgRealogramShelfDTO> getRealogramShelfPageList(String searchDate, String responseId, String responseGroupId, String storeCode,
                                                               String category, String brand, String skuName, String eanCode,
                                                               Boolean isPgProductFlag, String visitYear, String visitMonth,
                                                               String visitWeek, String sceneType, Boolean oos,
                                                               Integer pageNum, Integer pageSize) {
        
        // 设置默认分页参数
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }

        // 处理查询日期
        String processedDate = processSearchDate(searchDate);

        // 先从序列表获取符合条件的record_id列表
        // 优先使用responseGroupId，如果没有则使用responseId（向后兼容）
        String groupId = StringUtils.isNotBlank(responseGroupId) ? responseGroupId : responseId;
        List<Long> validRecordIds = getValidRecordIds(processedDate, groupId, "");

        // 如果没有符合条件的记录，返回空结果
        if (validRecordIds.isEmpty()) {
            return new Page<>(pageNum, pageSize);
        }

        // 构建查询条件
        LambdaQueryWrapper<PgOptimusRealogramShelf> queryWrapper = new LambdaQueryWrapper<>();

        // 使用id in (xxx, xxx, xxx)的方式查询
        queryWrapper.in(PgOptimusRealogramShelf::getRecordId, validRecordIds);
        if (StringUtils.isNotBlank(storeCode)) {
            queryWrapper.like(PgOptimusRealogramShelf::getStoreCode, storeCode);
        }
        if (StringUtils.isNotBlank(category)) {
            queryWrapper.like(PgOptimusRealogramShelf::getCategory, category);
        }
        if (StringUtils.isNotBlank(brand)) {
            queryWrapper.like(PgOptimusRealogramShelf::getBrand, brand);
        }
        if (StringUtils.isNotBlank(skuName)) {
            queryWrapper.like(PgOptimusRealogramShelf::getSkuName, skuName);
        }
        if (StringUtils.isNotBlank(eanCode)) {
            queryWrapper.eq(PgOptimusRealogramShelf::getEanCode, eanCode);
        }
        if (isPgProductFlag != null) {
            queryWrapper.eq(PgOptimusRealogramShelf::getIsPgProductFlag, isPgProductFlag);
        }
        if (StringUtils.isNotBlank(visitYear)) {
            queryWrapper.eq(PgOptimusRealogramShelf::getVisitYear, visitYear);
        }
        if (StringUtils.isNotBlank(visitMonth)) {
            queryWrapper.eq(PgOptimusRealogramShelf::getVisitMonth, visitMonth);
        }
        if (StringUtils.isNotBlank(visitWeek)) {
            queryWrapper.eq(PgOptimusRealogramShelf::getVisitWeek, visitWeek);
        }
        if (StringUtils.isNotBlank(sceneType)) {
            queryWrapper.eq(PgOptimusRealogramShelf::getSceneType, sceneType);
        }
        if (oos != null) {
            queryWrapper.eq(PgOptimusRealogramShelf::getOos, oos);
        }

        // 按访问时间倒序排序
        queryWrapper.orderByDesc(PgOptimusRealogramShelf::getId);

        // 执行分页查询
        Page<PgOptimusRealogramShelf> page = new Page<>(pageNum, pageSize);
        IPage<PgOptimusRealogramShelf> resultPage = realogramShelfService.page(page, queryWrapper);

        // 转换为DTO对象
        IPage<PgRealogramShelfDTO> dtoPage = resultPage.convert(entity -> {
            PgRealogramShelfDTO dto = new PgRealogramShelfDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            if ("1".equals(dto.getSmartAttribute())){
                dto.setSmartAttribute("stock");
            }
            if ("0".equals(dto.getSmartAttribute())){
                dto.setSmartAttribute("");
            }

            return dto;
        });

        return dtoPage;
    }

    /**
     * 根据responseId获取Display数据
     * @param responseId 答卷ID
     * @return Display数据列表
     */
    public List<PgRealogramDisplayDTO> getRealogramDisplayByResponseId(String responseId) {
        if (StringUtils.isBlank(responseId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PgOptimusRealogramDisplay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PgOptimusRealogramDisplay::getResponseId, responseId)
                   .orderByDesc(PgOptimusRealogramDisplay::getVisitDatetime);

        List<PgOptimusRealogramDisplay> entityList = realogramDisplayService.list(queryWrapper);
        List<PgRealogramDisplayDTO> dtoList = new ArrayList<>();
        
        for (PgOptimusRealogramDisplay entity : entityList) {
            PgRealogramDisplayDTO dto = new PgRealogramDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            dtoList.add(dto);
        }
        
        return dtoList;
    }

    /**
     * 根据responseId获取Shelf数据
     * @param responseId 答卷ID
     * @return Shelf数据列表
     */
    public List<PgRealogramShelfDTO> getRealogramShelfByResponseId(String responseId) {
        if (StringUtils.isBlank(responseId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PgOptimusRealogramShelf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PgOptimusRealogramShelf::getResponseId, responseId)
                   .orderByDesc(PgOptimusRealogramShelf::getVisitDatetime);

        List<PgOptimusRealogramShelf> entityList = realogramShelfService.list(queryWrapper);
        List<PgRealogramShelfDTO> dtoList = new ArrayList<>();
        
        for (PgOptimusRealogramShelf entity : entityList) {
            PgRealogramShelfDTO dto = new PgRealogramShelfDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            dtoList.add(dto);
        }
        
        return dtoList;
    }
}
