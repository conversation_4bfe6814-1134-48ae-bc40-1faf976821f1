package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
public class WmddSkuDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * qid
     */
    private String questionId;

    private String responseId;

    /**
     * SKU名称
     */
    private String productName;

    /**
     * 排面
     */
    private Integer facingCount;

    /**
     * 层数
     */
    private String level;

    /**
     * 价格
     */
    private String price;

    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public Integer getFacingCount() {
        return facingCount;
    }

    public void setFacingCount(Integer facingCount) {
        this.facingCount = facingCount;
    }
    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }
    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    @Override
    public String toString() {
        return "WmddSkuDetail{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", productName=" + productName +
        ", facingCount=" + facingCount +
        ", level=" + level +
        ", price=" + price +
        ", updateTime=" + updateTime +
        "}";
    }
}
