package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-21
 */
public class TDaliPoc extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String taskId;

    private String rid;

    private String qid;

    private String storeName;

    private String productId;

    private String productName;

    private String imageId;

    private String imageUrl;

    private String scene;

    private String layer;

    private Integer isSku;

    private Integer amount;

    private Integer fenxiao;

    private Integer paimian;

    private Integer imageAmount;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }
    public String getQid() {
        return qid;
    }

    public void setQid(String qid) {
        this.qid = qid;
    }
    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
    public String getLayer() {
        return layer;
    }

    public void setLayer(String layer) {
        this.layer = layer;
    }
    public Integer getIsSku() {
        return isSku;
    }

    public void setIsSku(Integer isSku) {
        this.isSku = isSku;
    }
    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }
    public Integer getFenxiao() {
        return fenxiao;
    }

    public void setFenxiao(Integer fenxiao) {
        this.fenxiao = fenxiao;
    }
    public Integer getPaimian() {
        return paimian;
    }

    public void setPaimian(Integer paimian) {
        this.paimian = paimian;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getImageAmount() {
        return imageAmount;
    }

    public void setImageAmount(Integer imageAmount) {
        this.imageAmount = imageAmount;
    }

    @Override
    public String toString() {
        return "TDaliPoc{" +
        "id=" + id +
        ", taskId=" + taskId +
        ", rid=" + rid +
        ", qid=" + qid +
        ", productId=" + productId +
        ", productName=" + productName +
        ", imageId=" + imageId +
        ", imageUrl=" + imageUrl +
        ", scene=" + scene +
        ", layer=" + layer +
        ", isSku=" + isSku +
        ", amount=" + amount +
        ", fenxiao=" + fenxiao +
        ", paimian=" + paimian +

        "}";
    }
}
