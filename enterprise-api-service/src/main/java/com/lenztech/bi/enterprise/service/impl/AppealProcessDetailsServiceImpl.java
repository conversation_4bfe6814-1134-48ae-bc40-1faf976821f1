//package com.lenztech.bi.enterprise.service.impl;
//
//import com.lenztech.bi.enterprise.entity.AppealProcessDetails;
//import com.lenztech.bi.enterprise.mapper.bienterprise.AppealProcessDetailsMapper;
//import com.lenztech.bi.enterprise.service.IAppealProcessDetailsService;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.springframework.stereotype.Service;
//
///**
// * <p>
// * 申诉详情，多条数据对应申诉信息主表 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-08-28
// */
//@Service
//public class AppealProcessDetailsServiceImpl extends ServiceImpl<AppealProcessDetailsMapper, AppealProcessDetails> implements IAppealProcessDetailsService {
//
//}
