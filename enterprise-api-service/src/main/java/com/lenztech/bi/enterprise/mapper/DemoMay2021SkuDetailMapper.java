package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.DemoMay2021SkuDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
@DS("lenzbi")
public interface DemoMay2021SkuDetailMapper extends BaseMapper<DemoMay2021SkuDetail> {

    List<DemoMay2021SkuDetail> getDetailListByProductId(@Param("productId") String productId, @Param("nowTime") String nowTime);

    List<DemoMay2021SkuDetail> getTrendDetailListByProductId(@Param("productId") String productId);
}
