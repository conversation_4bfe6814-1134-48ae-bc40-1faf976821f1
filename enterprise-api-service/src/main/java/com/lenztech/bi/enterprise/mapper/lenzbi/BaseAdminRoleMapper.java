package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.BaseAdminRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseAdminRoleMapper extends BaseMapper<BaseAdminRole> {

    List<BaseAdminRole> getRoleList();

    List<BaseAdminRole> getRoles();

    int updateRole(BaseAdminRole role);

    int updateRoleStatus(@Param("id") Integer id, @Param("roleStatus") Integer roleStatus);

}