package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneInfo;
import com.lenztech.bi.enterprise.entity.BnhdPatchesResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-23
 */
@DS("lenzbi")
public interface BnhdPatchesResultMapper extends BaseMapper<BnhdPatchesResult> {

    List<ProductPriceInfo> getProductPriceInfo(@Param("responseId") String responseId);

    List<ProductPriceInfo> getRecognizeSceneProducts(@Param("responseId") String responseId, @Param("scene") String scene);

}
