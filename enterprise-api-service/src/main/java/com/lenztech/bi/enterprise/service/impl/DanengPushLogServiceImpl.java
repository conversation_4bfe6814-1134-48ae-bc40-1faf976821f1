package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lenztech.bi.enterprise.entity.DanengPushLog;
import com.lenztech.bi.enterprise.mapper.DanengPushLogMapper;
import com.lenztech.bi.enterprise.service.IDanengPushLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 丹能数据推送日志记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-24
 */
@Slf4j
@Service
public class DanengPushLogServiceImpl extends ServiceImpl<DanengPushLogMapper, DanengPushLog> implements IDanengPushLogService {

    @Autowired
    private DanengPushLogMapper danengPushLogMapper;

    /**
     * 批量大小
     */
    private static final int BATCH_SIZE = 500;

    /**
     * 批量保存推送日志
     *
     * @param logList 日志列表
     * @return 是否保存成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<DanengPushLog> logList) {
        if (logList == null || logList.isEmpty()) {
            return false;
        }

        try {
            // 分批处理，每批500条
            List<List<DanengPushLog>> partition = Lists.partition(logList, BATCH_SIZE);
            
            for (List<DanengPushLog> batch : partition) {
                danengPushLogMapper.insertBatch(batch);
            }
            
            log.info("批量插入日志成功，总数：{}", logList.size());
            return true;
        } catch (Exception e) {
            log.error("批量插入日志失败", e);
            return false;
        }
    }
} 