package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusBrandLevelDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusCategoryLevelDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusSkuLevelDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusSkuLevelShelfDTO;
import com.lenztech.bi.enterprise.entity.PgOptimusBrandLevelDisplayCount;
import com.lenztech.bi.enterprise.entity.PgOptimusCategoryLevelDisplayCount;
import com.lenztech.bi.enterprise.entity.PgOptimusRealogramSequence;
import com.lenztech.bi.enterprise.entity.PgOptimusSkuLevelDisplayData;
import com.lenztech.bi.enterprise.entity.PgOptimusSkuLevelShelfData;
import com.lenztech.bi.enterprise.mapper.PgOptimusBrandLevelDisplayCountMapper;
import com.lenztech.bi.enterprise.mapper.PgOptimusCategoryLevelDisplayCountMapper;
import com.lenztech.bi.enterprise.mapper.PgOptimusSkuLevelDisplayDataMapper;
import com.lenztech.bi.enterprise.mapper.PgOptimusSkuLevelShelfDataMapper;
import com.lenztech.bi.enterprise.service.IPgOptimusBrandLevelDisplayCountService;
import com.lenztech.bi.enterprise.service.IPgOptimusCategoryLevelDisplayCountService;
import com.lenztech.bi.enterprise.service.IPgOptimusRealogramSequenceService;
import com.lenztech.bi.enterprise.service.IPgOptimusSkuLevelDisplayDataService;
import com.lenztech.bi.enterprise.service.IPgOptimusSkuLevelShelfDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PgOptimusService {

    @Autowired
    private PgOptimusSkuLevelShelfDataMapper pgOptimusSkuLevelShelfDataMapper;

    @Autowired
    private PgOptimusSkuLevelDisplayDataMapper pgOptimusSkuLevelDisplayDataMapper;

    @Autowired
    private PgOptimusBrandLevelDisplayCountMapper pgOptimusBrandLevelDisplayCountMapper;

    @Autowired
    private PgOptimusCategoryLevelDisplayCountMapper pgOptimusCategoryLevelDisplayCountMapper;

    @Autowired
    private IPgOptimusBrandLevelDisplayCountService brandLevelDisplayCountService;

    @Autowired
    private IPgOptimusCategoryLevelDisplayCountService categoryLevelDisplayCountService;

    @Autowired
    private IPgOptimusSkuLevelDisplayDataService skuLevelDisplayDataService;

    @Autowired
    private IPgOptimusSkuLevelShelfDataService skuLevelShelfDataService;

    @Autowired
    private IPgOptimusRealogramSequenceService realogramSequenceService;

    /**
     * 处理日期参数，如果为空则返回当前日期的前一天
     * @param searchDate 查询日期 格式：yyyy-MM-dd
     * @return 处理后的日期字符串
     */
    public String processSearchDate(String searchDate) {
        if (StringUtils.isBlank(searchDate)) {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            return yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return searchDate;
    }

    /**
     * 将大的ID列表分割成小的块，每块最多500个ID，避免IN查询超时
     * @param ids ID列表
     * @param chunkSize 每块的大小，默认500
     * @return 分割后的ID块列表
     */
    private List<List<Long>> splitIntoChunks(List<Long> ids, int chunkSize) {
        List<List<Long>> chunks = new ArrayList<>();
        if (ids == null || ids.isEmpty()) {
            return chunks;
        }

        for (int i = 0; i < ids.size(); i += chunkSize) {
            int end = Math.min(i + chunkSize, ids.size());
            chunks.add(ids.subList(i, end));
        }
        return chunks;
    }

    /**
     * 根据查询条件获取符合条件的record_id列表
     * @param searchDate 查询日期
     * @param responseGroupId group_id
     * @return record_id列表
     */
    private List<Long> getValidRecordIds(String searchDate, String responseGroupId) {
        // 构建查询条件
        LambdaQueryWrapper<PgOptimusRealogramSequence> queryWrapper = new LambdaQueryWrapper<>();

        // 按日期查询
        if (StringUtils.isNotBlank(searchDate)) {
//            queryWrapper.apply("DATE(insert_time) = {0}", searchDate);
            queryWrapper.eq(PgOptimusRealogramSequence::getExecDate, searchDate);
        }

        // 按responseGroupId查询
        if (StringUtils.isNotBlank(responseGroupId)) {
            queryWrapper.eq(PgOptimusRealogramSequence::getResponseGroupId, responseGroupId);
        }

        // 只查询需要保留且处理成功的记录
        queryWrapper.eq(PgOptimusRealogramSequence::getIsRemainedScc, 1)
                   .eq(PgOptimusRealogramSequence::getProcessStatus, 1);

        // 只查询id字段
        queryWrapper.select(PgOptimusRealogramSequence::getId);

        List<PgOptimusRealogramSequence> sequences = realogramSequenceService.list(queryWrapper);
        return sequences.stream().map(PgOptimusRealogramSequence::getId).collect(Collectors.toList());
    }

    /**
     * 获取Brand级别二陈数据
     * @param searchDate 查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天
     * @param responseGroupId group_id
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 品牌级别二陈数据列表
     */
    public IPage<PgOptimusBrandLevelDisplayDTO> getBrandLevelDisplayBiResult(String searchDate, String responseGroupId, Integer pageNum, Integer pageSize) {
        // 处理日期参数
        searchDate = processSearchDate(searchDate);
        // 设置默认分页参数
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 先从序列表获取符合条件的record_id列表
        List<Long> validRecordIds = getValidRecordIds(searchDate, responseGroupId);

        // 如果没有符合条件的记录，返回空结果
        if (validRecordIds.isEmpty()) {
            return new Page<>(pageNum, pageSize);
        }

        // 将ID列表分割成小块，避免IN查询超时
        List<List<Long>> idChunks = splitIntoChunks(validRecordIds, 500);

        // 收集所有符合条件的数据
        List<PgOptimusBrandLevelDisplayCount> allResults = new ArrayList<>();

        for (List<Long> chunk : idChunks) {
            // 构建查询条件
            LambdaQueryWrapper<PgOptimusBrandLevelDisplayCount> queryWrapper = new LambdaQueryWrapper<>();

            // 使用分块的ID进行查询
            queryWrapper.in(PgOptimusBrandLevelDisplayCount::getRecordId, chunk);

            // 按访问时间倒序排序
            queryWrapper.orderByDesc(PgOptimusBrandLevelDisplayCount::getVisitDatetime);

            // 查询当前块的数据
            List<PgOptimusBrandLevelDisplayCount> chunkResults = brandLevelDisplayCountService.list(queryWrapper);
            allResults.addAll(chunkResults);
        }

        // 对所有结果进行排序
        allResults.sort((a, b) -> b.getVisitDatetime().compareTo(a.getVisitDatetime()));

        // 手动实现分页
        int total = allResults.size();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<PgOptimusBrandLevelDisplayCount> pageResults = new ArrayList<>();
        if (start < total) {
            pageResults = allResults.subList(start, end);
        }

        // 创建分页结果
        Page<PgOptimusBrandLevelDisplayCount> page = new Page<>(pageNum, pageSize, total);
        page.setRecords(pageResults);

        // 转换为DTO对象
        IPage<PgOptimusBrandLevelDisplayDTO> dtoPage = page.convert(entity -> {
            PgOptimusBrandLevelDisplayDTO dto = new PgOptimusBrandLevelDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }
            if (entity.getTotalDisplayCountBrand() != null){
                dto.setTotalDisplayCountCat(entity.getTotalDisplayCountBrand());
            }

            // 确保 execDate 字段被正确设置
            dto.setExecDate(entity.getExecDate());

            return dto;
        });

        return dtoPage;
    }

    /**
     * 根据responseId获取Brand级别二陈数据
     * @param responseId 答卷ID
     * @return 品牌级别二陈数据列表
     */
    public List<PgOptimusBrandLevelDisplayDTO> getBrandLevelDisplayByResponseId(String responseId) {
        if (StringUtils.isBlank(responseId)) {
            return new ArrayList<>();
        }

        // 构建查询条件
        LambdaQueryWrapper<PgOptimusBrandLevelDisplayCount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PgOptimusBrandLevelDisplayCount::getResponseId, responseId);
        queryWrapper.orderByDesc(PgOptimusBrandLevelDisplayCount::getVisitDatetime);

        // 执行查询
        List<PgOptimusBrandLevelDisplayCount> entityList = brandLevelDisplayCountService.list(queryWrapper);

        // 转换为DTO对象
        List<PgOptimusBrandLevelDisplayDTO> dtoList = new ArrayList<>();
        for (PgOptimusBrandLevelDisplayCount entity : entityList) {
            PgOptimusBrandLevelDisplayDTO dto = new PgOptimusBrandLevelDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            // 直接设置 execDate 字段，因为实体类和 DTO 类中的类型都是 Date
            dto.setExecDate(entity.getExecDate());

            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 获取Category级别二陈数据
     * @param searchDate 查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天
     * @param responseGroupId group_id
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 品类级别二陈数据列表
     */
    public IPage<PgOptimusCategoryLevelDisplayDTO> getCategoryLevelDisplayBiResult(String searchDate, String responseGroupId, Integer pageNum, Integer pageSize) {
        // 处理日期参数
        searchDate = processSearchDate(searchDate);
        // 设置默认分页参数
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 先从序列表获取符合条件的record_id列表
        List<Long> validRecordIds = getValidRecordIds(searchDate, responseGroupId);

        // 如果没有符合条件的记录，返回空结果
        if (validRecordIds.isEmpty()) {
            return new Page<>(pageNum, pageSize);
        }

        // 将ID列表分割成小块，避免IN查询超时
        List<List<Long>> idChunks = splitIntoChunks(validRecordIds, 500);

        // 收集所有符合条件的数据
        List<PgOptimusCategoryLevelDisplayCount> allResults = new ArrayList<>();

        for (List<Long> chunk : idChunks) {
            // 构建查询条件
            LambdaQueryWrapper<PgOptimusCategoryLevelDisplayCount> queryWrapper = new LambdaQueryWrapper<>();

            // 使用分块的ID进行查询
            queryWrapper.in(PgOptimusCategoryLevelDisplayCount::getRecordId, chunk);

            // 按访问时间倒序排序
            queryWrapper.orderByDesc(PgOptimusCategoryLevelDisplayCount::getVisitDatetime);

            // 查询当前块的数据
            List<PgOptimusCategoryLevelDisplayCount> chunkResults = categoryLevelDisplayCountService.list(queryWrapper);
            allResults.addAll(chunkResults);
        }

        // 对所有结果进行排序
        allResults.sort((a, b) -> b.getVisitDatetime().compareTo(a.getVisitDatetime()));

        // 手动实现分页
        int total = allResults.size();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<PgOptimusCategoryLevelDisplayCount> pageResults = new ArrayList<>();
        if (start < total) {
            pageResults = allResults.subList(start, end);
        }

        // 创建分页结果
        Page<PgOptimusCategoryLevelDisplayCount> page = new Page<>(pageNum, pageSize, total);
        page.setRecords(pageResults);

        // 转换为DTO对象
        IPage<PgOptimusCategoryLevelDisplayDTO> dtoPage = page.convert(entity -> {
            PgOptimusCategoryLevelDisplayDTO dto = new PgOptimusCategoryLevelDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            // 手动处理 LocalDate 到 Date 的转换
            if (entity.getExecDate() != null) {
                dto.setExecDate(java.sql.Date.valueOf(entity.getExecDate()));
            }

            return dto;
        });

        return dtoPage;
    }

    /**
     * 根据responseId获取Category级别二陈数据
     * @param responseId 答卷ID
     * @return 品类级别二陈数据列表
     */
    public List<PgOptimusCategoryLevelDisplayDTO> getCategoryLevelDisplayByResponseId(String responseId) {
        if (StringUtils.isBlank(responseId)) {
            return new ArrayList<>();
        }

        // 构建查询条件
        LambdaQueryWrapper<PgOptimusCategoryLevelDisplayCount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PgOptimusCategoryLevelDisplayCount::getResponseId, responseId);
        queryWrapper.orderByDesc(PgOptimusCategoryLevelDisplayCount::getVisitDatetime);

        // 执行查询
        List<PgOptimusCategoryLevelDisplayCount> entityList = categoryLevelDisplayCountService.list(queryWrapper);

        // 转换为DTO对象
        List<PgOptimusCategoryLevelDisplayDTO> dtoList = new ArrayList<>();
        for (PgOptimusCategoryLevelDisplayCount entity : entityList) {
            PgOptimusCategoryLevelDisplayDTO dto = new PgOptimusCategoryLevelDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            // 手动处理 LocalDate 到 Date 的转换
            if (entity.getExecDate() != null) {
                dto.setExecDate(java.sql.Date.valueOf(entity.getExecDate()));
            }

            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 获取SKU级别二陈数据
     * @param searchDate 查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天
     * @param responseGroupId group_id
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return SKU级别二陈数据列表
     */
    public IPage<PgOptimusSkuLevelDisplayDTO> getSkuLevelDisplayBiResult(String searchDate, String responseGroupId, Integer pageNum, Integer pageSize) {
        // 处理日期参数
        searchDate = processSearchDate(searchDate);
        // 设置默认分页参数
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 先从序列表获取符合条件的record_id列表
        List<Long> validRecordIds = getValidRecordIds(searchDate, responseGroupId);

        // 如果没有符合条件的记录，返回空结果
        if (validRecordIds.isEmpty()) {
            return new Page<>(pageNum, pageSize);
        }

        // 将ID列表分割成小块，避免IN查询超时
        List<List<Long>> idChunks = splitIntoChunks(validRecordIds, 500);

        // 收集所有符合条件的数据
        List<PgOptimusSkuLevelDisplayData> allResults = new ArrayList<>();

        for (List<Long> chunk : idChunks) {
            // 构建查询条件
            LambdaQueryWrapper<PgOptimusSkuLevelDisplayData> queryWrapper = new LambdaQueryWrapper<>();

            // 使用分块的ID进行查询
            queryWrapper.in(PgOptimusSkuLevelDisplayData::getRecordId, chunk);

            // 按访问时间倒序排序
            queryWrapper.orderByDesc(PgOptimusSkuLevelDisplayData::getVisitDatetime);

            // 查询当前块的数据
            List<PgOptimusSkuLevelDisplayData> chunkResults = skuLevelDisplayDataService.list(queryWrapper);
            allResults.addAll(chunkResults);
        }

        // 对所有结果进行排序
        allResults.sort((a, b) -> b.getVisitDatetime().compareTo(a.getVisitDatetime()));

        // 手动实现分页
        int total = allResults.size();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<PgOptimusSkuLevelDisplayData> pageResults = new ArrayList<>();
        if (start < total) {
            pageResults = allResults.subList(start, end);
        }

        // 创建分页结果
        Page<PgOptimusSkuLevelDisplayData> page = new Page<>(pageNum, pageSize, total);
        page.setRecords(pageResults);

        // 转换为DTO对象
        IPage<PgOptimusSkuLevelDisplayDTO> dtoPage = page.convert(entity -> {
            PgOptimusSkuLevelDisplayDTO dto = new PgOptimusSkuLevelDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            // 手动处理 LocalDate 到 Date 的转换
            if (entity.getExecDate() != null) {
                dto.setExecDate(java.sql.Date.valueOf(entity.getExecDate()));
            }

            return dto;
        });

        return dtoPage;
    }

    /**
     * 根据responseId获取SKU级别二陈数据
     * @param responseId 答卷ID
     * @return SKU级别二陈数据列表
     */
    public List<PgOptimusSkuLevelDisplayDTO> getSkuLevelDisplayByResponseId(String responseId) {
        if (StringUtils.isBlank(responseId)) {
            return new ArrayList<>();
        }

        // 构建查询条件
        LambdaQueryWrapper<PgOptimusSkuLevelDisplayData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PgOptimusSkuLevelDisplayData::getResponseId, responseId);
        queryWrapper.orderByDesc(PgOptimusSkuLevelDisplayData::getVisitDatetime);

        // 执行查询
        List<PgOptimusSkuLevelDisplayData> entityList = skuLevelDisplayDataService.list(queryWrapper);

        // 转换为DTO对象
        List<PgOptimusSkuLevelDisplayDTO> dtoList = new ArrayList<>();
        for (PgOptimusSkuLevelDisplayData entity : entityList) {
            PgOptimusSkuLevelDisplayDTO dto = new PgOptimusSkuLevelDisplayDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            // 手动处理 LocalDate 到 Date 的转换
            if (entity.getExecDate() != null) {
                dto.setExecDate(java.sql.Date.valueOf(entity.getExecDate()));
            }

            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 获取SKU级别货架数据
     * @param searchDate 查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天
     * @param responseGroupId group_id
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return SKU级别货架数据列表
     */
    public IPage<PgOptimusSkuLevelShelfDTO> getSkuLevelShelfBiResult(String searchDate, String responseGroupId, Integer pageNum, Integer pageSize) {
        // 处理日期参数
        searchDate = processSearchDate(searchDate);
        // 设置默认分页参数
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 先从序列表获取符合条件的record_id列表
        List<Long> validRecordIds = getValidRecordIds(searchDate, responseGroupId);

        // 如果没有符合条件的记录，返回空结果
        if (validRecordIds.isEmpty()) {
            return new Page<>(pageNum, pageSize);
        }

        // 将ID列表分割成小块，避免IN查询超时
        List<List<Long>> idChunks = splitIntoChunks(validRecordIds, 500);

        // 收集所有符合条件的数据
        List<PgOptimusSkuLevelShelfData> allResults = new ArrayList<>();

        for (List<Long> chunk : idChunks) {
            // 构建查询条件
            LambdaQueryWrapper<PgOptimusSkuLevelShelfData> queryWrapper = new LambdaQueryWrapper<>();

            // 使用分块的ID进行查询
            queryWrapper.in(PgOptimusSkuLevelShelfData::getRecordId, chunk);

            // 按访问时间倒序排序
            queryWrapper.orderByDesc(PgOptimusSkuLevelShelfData::getVisitDatetime);

            // 查询当前块的数据
            List<PgOptimusSkuLevelShelfData> chunkResults = skuLevelShelfDataService.list(queryWrapper);
            allResults.addAll(chunkResults);
        }

        // 对所有结果进行排序
        allResults.sort((a, b) -> b.getVisitDatetime().compareTo(a.getVisitDatetime()));

        // 手动实现分页
        int total = allResults.size();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<PgOptimusSkuLevelShelfData> pageResults = new ArrayList<>();
        if (start < total) {
            pageResults = allResults.subList(start, end);
        }

        // 创建分页结果
        Page<PgOptimusSkuLevelShelfData> page = new Page<>(pageNum, pageSize, total);
        page.setRecords(pageResults);

        // 转换为DTO对象
        IPage<PgOptimusSkuLevelShelfDTO> dtoPage = page.convert(entity -> {
            PgOptimusSkuLevelShelfDTO dto = new PgOptimusSkuLevelShelfDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            // 手动处理 LocalDate 到 Date 的转换
            if (entity.getExecDate() != null) {
                dto.setExecDate(java.sql.Date.valueOf(entity.getExecDate()));
            }

            return dto;
        });

        return dtoPage;
    }

    /**
     * 根据responseId获取SKU级别货架数据
     * @param responseId 答卷ID
     * @return SKU级别货架数据列表
     */
    public List<PgOptimusSkuLevelShelfDTO> getSkuLevelShelfByResponseId(String responseId) {
        if (StringUtils.isBlank(responseId)) {
            return new ArrayList<>();
        }

        // 构建查询条件
        LambdaQueryWrapper<PgOptimusSkuLevelShelfData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PgOptimusSkuLevelShelfData::getResponseId, responseId);
        queryWrapper.orderByDesc(PgOptimusSkuLevelShelfData::getVisitDatetime);

        // 执行查询
        List<PgOptimusSkuLevelShelfData> entityList = skuLevelShelfDataService.list(queryWrapper);

        // 转换为DTO对象
        List<PgOptimusSkuLevelShelfDTO> dtoList = new ArrayList<>();
        for (PgOptimusSkuLevelShelfData entity : entityList) {
            PgOptimusSkuLevelShelfDTO dto = new PgOptimusSkuLevelShelfDTO();
            BeanUtils.copyProperties(entity, dto, "visitDatetime");

            // 手动处理 LocalDateTime 到 Date 的转换
            if (entity.getVisitDatetime() != null) {
                dto.setVisitDatetime(Timestamp.valueOf(entity.getVisitDatetime()));
            }

            // 手动处理 LocalDate 到 Date 的转换
            if (entity.getExecDate() != null) {
                dto.setExecDate(java.sql.Date.valueOf(entity.getExecDate()));
            }

            dtoList.add(dto);
        }

        return dtoList;
    }
}
