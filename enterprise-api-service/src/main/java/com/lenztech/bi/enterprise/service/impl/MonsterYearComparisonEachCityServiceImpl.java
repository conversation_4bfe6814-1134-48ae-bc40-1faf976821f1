package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.entity.MonsterYearComparisonEachCity;
import com.lenztech.bi.enterprise.mapper.MonsterYearComparisonEachCityMapper;
import com.lenztech.bi.enterprise.service.IMonsterYearComparisonEachCityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 魔爪年度对比3 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@Service
public class MonsterYearComparisonEachCityServiceImpl extends ServiceImpl<MonsterYearComparisonEachCityMapper, MonsterYearComparisonEachCity> implements IMonsterYearComparisonEachCityService {

}
