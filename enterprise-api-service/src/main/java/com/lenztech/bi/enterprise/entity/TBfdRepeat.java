package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TBfdRepeat implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String responseId;

    private String groupId;

    /**
     * 图片ID
     */
    private String repeatId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
