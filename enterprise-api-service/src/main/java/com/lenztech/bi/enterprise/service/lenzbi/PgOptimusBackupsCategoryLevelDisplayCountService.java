package com.lenztech.bi.enterprise.service.lenzbi;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lenztech.bi.enterprise.entity.lenzbi.PgOptimusBackupsCategoryLevelDisplayCount;
import com.lenztech.bi.enterprise.mapper.lenzbi.PgOptimusBackupsCategoryLevelDisplayCountMapper;
import org.springframework.stereotype.Service;

@Service
public class PgOptimusBackupsCategoryLevelDisplayCountService extends ServiceImpl<PgOptimusBackupsCategoryLevelDisplayCountMapper, PgOptimusBackupsCategoryLevelDisplayCount> {
} 