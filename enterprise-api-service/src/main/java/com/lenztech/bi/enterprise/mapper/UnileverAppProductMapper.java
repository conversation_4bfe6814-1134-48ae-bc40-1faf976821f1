package com.lenztech.bi.enterprise.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.UnileverAppProductDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022-01-17 17:48
 */
public interface UnileverAppProductMapper extends BaseMapper<UnileverAppProductDetail> {

    @Select("select * from unilever_app_product_detail${shardingKey} where response_id = #{responseId}")
    List<UnileverAppProductDetail> getList(@Param("responseId") String responseId, @Param("shardingKey") String shardingKey);

}
