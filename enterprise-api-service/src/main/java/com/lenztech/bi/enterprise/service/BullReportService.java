package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.entity.BullPatchesResult;
import com.lenztech.bi.enterprise.entity.BullStoreRecord;
import com.lenztech.bi.enterprise.mapper.BullPatchesResultMapper;
import com.lenztech.bi.enterprise.mapper.BullStoreRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/9/22
 * Time: 14:09
 * 类功能:
 */
@Service
public class BullReportService {

    @Autowired
    BullStoreRecordMapper bullStoreRecordMapper;

    @Autowired
    BullPatchesResultMapper bullPatchesResultMapper;

    /**
     * 获取公牛门店信息
     * @param responseId
     * @return
     */
    public BullStoreRecord getStoreInfo(String responseId){

        LambdaQueryWrapper<BullStoreRecord> bullStoreRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bullStoreRecordLambdaQueryWrapper.eq(BullStoreRecord::getResponseId, responseId);
        BullStoreRecord bullStoreRecord = bullStoreRecordMapper.selectOne(bullStoreRecordLambdaQueryWrapper);

        return bullStoreRecord;
    }

    /**
     * 获取公牛识别产品信息
     * @param responseId
     * @return
     */
    public List<BullPatchesResult> getRecognizeProductsInfo(String responseId){
        LambdaQueryWrapper<BullPatchesResult> bullPatchesResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bullPatchesResultLambdaQueryWrapper.eq(BullPatchesResult::getResponseId, responseId);
        List<BullPatchesResult> bullPatchesResultList = bullPatchesResultMapper.selectList(bullPatchesResultLambdaQueryWrapper);
        return bullPatchesResultList;
    }



}
