package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lenztech.bi.enterprise.dto.chinashop.*;
import com.lenztech.bi.enterprise.entity.*;
import com.lenztech.bi.enterprise.mapper.*;
import com.lenztech.bi.enterprise.mapper.task.TAnswerMapper;
import com.lenztech.bi.enterprise.mapper.task.TResponseMapper;
import com.lenztech.bi.enterprise.service.IChinaShopService;
import com.lenztech.bi.enterprise.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/11/2 14:15
 **/

@Service
public class ChinaShopServiceImpl implements IChinaShopService {

    @Autowired
    private ChinashopdemoDetailMapper chinashopdemoDetailMapper;

    @Autowired
    private ChinashopdemoKpiMapper chinashopdemoKpiMapper;


    @Autowired
    private TResponseMapper tResponseMapper;

    @Autowired
    private TAnswerMapper tAnswerMapper;


    @Value("${china_shop_task_id}")
    private String chinaShopTaskId;

    @Value("${china_shop_task_id_v3}")
    private String chinaShopTaskIdV3;

    @Autowired
    private WmddKpiMapper wmddKpiMapper;

    @Autowired
    private WmddSkuDetailMapper wmddSkuDetailMapper;

    @Autowired
    private WmddQuestionMapMapper wmddQuestionMapMapper;

    @Autowired
    private DemoMay2021KpiMapper demoMay2021KpiMapper;

    @Autowired
    private DemoMay2021SkuDetailMapper demoMay2021SkuDetailMapper;

    @Autowired
    private DemoMay2021QuestionMapMapper demoMay2021QuestionMapMapper;


    @Override
    public ChinaShopDemoDTO getChinaShopResult() {
        ChinaShopDemoDTO result = new ChinaShopDemoDTO();

        IPage<TResponse> page = new Page<>(1, 50);
        LambdaQueryWrapper<TResponse> responseWrapper = new LambdaQueryWrapper<>();
        responseWrapper.eq(TResponse::getTaskidOwner, chinaShopTaskId);
        responseWrapper.orderByDesc(TResponse::getUpTime);
        page = tResponseMapper.selectPage(page, responseWrapper);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return result;
        }
        List<String> responseIdList = page.getRecords().stream().map(TResponse::getId).collect(Collectors.toList());

        LambdaQueryWrapper<ChinashopdemoKpi> kpiWrapper = new LambdaQueryWrapper<>();
        kpiWrapper.in(ChinashopdemoKpi::getResponseId, responseIdList);
        Map<String, ChinashopdemoKpi> kpiMap = chinashopdemoKpiMapper.selectList(kpiWrapper).stream().collect(Collectors.toMap(ChinashopdemoKpi::getResponseId, kpi -> kpi));

        // 组合历史数据
        result.setRecordList(mergeRecord(page.getRecords(), kpiMap));

        // 获取最新完成答卷
        String responseId = getLatestSuccess(page.getRecords(), kpiMap);
        if (StringUtils.isBlank(responseId)) {
            return result;
        }
        LambdaQueryWrapper<ChinashopdemoDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(ChinashopdemoDetail::getResponseId, responseId);
        detailWrapper.orderByAsc(ChinashopdemoDetail::getSeqNo);
        List<ChinashopdemoDetail> detailList = chinashopdemoDetailMapper.selectList(detailWrapper);

        // 组装明细
        result.setDetailList(mergeDetail(detailList));

        // 设置图片地址
        result.setStitchPictureUrl(kpiMap.get(responseId).getStitchURL());

        // 组装当前仪表盘
        result.setNowResult(mergeInstrument(kpiMap.get(responseId)));

        // 获取上次完成的的卷
        String second = getSecondSuccess(responseId, page.getRecords(), kpiMap);
        if (StringUtils.isNotBlank(second)) {
            result.setOldResult(mergeInstrument(kpiMap.get(second)));
        }

        // 组装缺货产品
        result.setStockProductList(mergeStockProduct(detailList));

        // 组装占有率
        result.setRateList(mergeRateList(page.getRecords(), kpiMap));

        // 组装错误价格表
        result.setWrongPriceList(mergeWrongPrice(detailList));

        return result;
    }

    @Override
    public ChinaShopDemoDTO getChinaShopResultV2(String questionId) {
        ChinaShopDemoDTO result = new ChinaShopDemoDTO();

        IPage<TAnswer> page = new Page<>(1, 50);
        LambdaQueryWrapper<TAnswer> tAnswerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tAnswerLambdaQueryWrapper.eq(TAnswer::getQid, questionId);
        tAnswerLambdaQueryWrapper.orderByDesc(TAnswer::getUpdateTime);
        page = tAnswerMapper.selectPage(page, tAnswerLambdaQueryWrapper);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return result;
        }
        List<String> responseIdList = page.getRecords().stream().map(TAnswer::getResponseId).collect(Collectors.toList());

        LambdaQueryWrapper<WmddKpi> kpiWrapper = new LambdaQueryWrapper<>();
        kpiWrapper.in(WmddKpi::getResponseId, responseIdList);
        kpiWrapper.eq(WmddKpi::getQuestionId, questionId);
        Map<String, WmddKpi> kpiMap = wmddKpiMapper.selectList(kpiWrapper).stream().collect(Collectors.toMap(WmddKpi::getResponseId, kpi -> kpi));

        // 组合历史数据
        result.setRecordList(mergeRecordV2(page.getRecords(), kpiMap));

        // 获取最新完成答卷
        String responseId = getLatestSuccessV2(page.getRecords(), kpiMap);
        if (StringUtils.isBlank(responseId)) {
            return result;
        }
        LambdaQueryWrapper<WmddSkuDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(WmddSkuDetail::getResponseId, responseId);
        detailWrapper.eq(WmddSkuDetail::getQuestionId, questionId);
        detailWrapper.orderByDesc(WmddSkuDetail::getFacingCount);
        List<WmddSkuDetail> detailList = wmddSkuDetailMapper.selectList(detailWrapper);

        // 组装明细
        result.setDetailList(mergeDetailV2(detailList));

        // 设置图片地址
        result.setStitchPictureUrl(kpiMap.get(responseId).getStitchUrl());

        // 组装当前仪表盘
        result.setNowResult(mergeInstrumentV2(kpiMap.get(responseId)));

        // 获取上次完成的问卷
        String second = getSecondSuccessV2(responseId, page.getRecords(), kpiMap);
        if (StringUtils.isNotBlank(second)) {
            result.setOldResult(mergeInstrumentV2(kpiMap.get(second)));
        }

        // 组装占有率
        result.setRateList(mergeRateListV2(page.getRecords(), kpiMap));

        return result;
    }

    public ChinaShopDemoDTO getChinaShopResultV3(String questionId) {
        ChinaShopDemoDTO result = new ChinaShopDemoDTO();

        IPage<TAnswer> page = new Page<>(1, 50);
        LambdaQueryWrapper<TAnswer> tAnswerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tAnswerLambdaQueryWrapper.eq(TAnswer::getQid, questionId);
        tAnswerLambdaQueryWrapper.orderByDesc(TAnswer::getUpdateTime);
        page = tAnswerMapper.selectPage(page, tAnswerLambdaQueryWrapper);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return result;
        }
        List<String> responseIdList = page.getRecords().stream().map(TAnswer::getResponseId).collect(Collectors.toList());

        LambdaQueryWrapper<DemoMay2021Kpi> kpiWrapper = new LambdaQueryWrapper<>();
        kpiWrapper.in(DemoMay2021Kpi::getResponseId, responseIdList);
        kpiWrapper.eq(DemoMay2021Kpi::getQuestionId, questionId);
        Map<String, DemoMay2021Kpi> kpiMap = demoMay2021KpiMapper.selectList(kpiWrapper).stream().collect(Collectors.toMap(DemoMay2021Kpi::getResponseId, kpi -> kpi));

        // 组合历史数据
        result.setRecordList(mergeRecordV3(page.getRecords(), kpiMap));

        // 获取最新完成答卷
        String responseId = getLatestSuccessV3(page.getRecords(), kpiMap);
        if (StringUtils.isBlank(responseId)) {
            return result;
        }
        LambdaQueryWrapper<DemoMay2021SkuDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021SkuDetail::getResponseId, responseId);
        detailWrapper.eq(DemoMay2021SkuDetail::getQuestionId, questionId);
        detailWrapper.orderByAsc(DemoMay2021SkuDetail::getFacingCount);
        List<DemoMay2021SkuDetail> detailList = demoMay2021SkuDetailMapper.selectList(detailWrapper);

        // 组装明细
        result.setDetailList(mergeDetailV3(detailList));

        // 设置图片地址
        result.setStitchPictureUrl(kpiMap.get(responseId).getStitchUrl());

        // 组装当前仪表盘
        result.setNowResult(mergeInstrumentV3(kpiMap.get(responseId)));

        // 获取上次完成的的卷
        String second = getSecondSuccessV3(responseId, page.getRecords(), kpiMap);
        if (StringUtils.isNotBlank(second)) {
            result.setOldResult(mergeInstrumentV3(kpiMap.get(second)));
        }

        // 组装缺货产品
        result.setStockProductList(mergeStockProductV3(detailList));

        // 组装占有率
        result.setRateList(mergeRateListV3(page.getRecords(), kpiMap));

        // 组装错误价格表
        result.setWrongPriceList(mergeWrongPriceV3(detailList));

        //组装报警记录
        result.setAlarmRecord(mergeAlarmRecord(questionId));

        return result;
    }

    /**
     * 报警记录  为总数累加
     * @return
     */
    private AlarmRecord mergeAlarmRecord(String questionId){

        LambdaQueryWrapper<DemoMay2021Kpi> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021Kpi::getQuestionId, questionId);
        detailWrapper.gt(DemoMay2021Kpi::getShortageRatio, 0);
        Integer alarmCountShortageSku = demoMay2021KpiMapper.selectCount(detailWrapper);
        detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021Kpi::getQuestionId, questionId);
        detailWrapper.gt(DemoMay2021Kpi::getLowFacingSkuCount, 0);
        Integer alarmCountLowFacing = demoMay2021KpiMapper.selectCount(detailWrapper);
        detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(DemoMay2021Kpi::getQuestionId, questionId);
        detailWrapper.gt(DemoMay2021Kpi::getWrongPriceSkuCount, 0);
        Integer alarmCountWrongPrice = demoMay2021KpiMapper.selectCount(detailWrapper);

        AlarmRecord alarmRecord = new AlarmRecord();
        alarmRecord.setAlarmCountLowFacing(alarmCountLowFacing);
        alarmRecord.setAlarmCountShortageSku(alarmCountShortageSku);
        alarmRecord.setAlarmCountWrongPrice(alarmCountWrongPrice);
        return alarmRecord;
    }

    private List<WrongPrice> mergeWrongPrice(List<ChinashopdemoDetail> detailList) {
        List<WrongPrice> priceList = Lists.newArrayList();
        for (ChinashopdemoDetail record : detailList) {
            if (StringUtils.isBlank(record.getPriceWrong())){
                continue;
            }
            WrongPrice wrongPrice = new WrongPrice();
            wrongPrice.setProductName(record.getProductName());
            wrongPrice.setWrongPrice(record.getPriceWrong());
            wrongPrice.setCorrectPrice(record.getPriceStandard());
            priceList.add(wrongPrice);
        }
        return priceList;
    }

    private List<WrongPrice> mergeWrongPriceV3(List<DemoMay2021SkuDetail> detailList) {
        List<WrongPrice> priceList = Lists.newArrayList();
        for (DemoMay2021SkuDetail record : detailList) {
            //非错误价格
            if (record.getIfWrongPrice() == 0){
                continue;
            }
            WrongPrice wrongPrice = new WrongPrice();
            wrongPrice.setProductName(record.getProductName());
            wrongPrice.setWrongPrice(record.getPrice());
            wrongPrice.setCorrectPrice(String.valueOf(record.getStandardPrice()));
            wrongPrice.setIsPsku(record.getIfPsku());
            priceList.add(wrongPrice);
        }
        return priceList;
    }


    private List<StockProduct> mergeStockProduct(List<ChinashopdemoDetail> detailList) {
        List<StockProduct> stockProductList = Lists.newArrayList();
        List<ChinashopdemoDetail> list = detailList.stream().filter(detail -> detail.getFaceingCount() == 0).collect(Collectors.toList());
        for (ChinashopdemoDetail chinashopdemoDetail : list) {
            StockProduct product = new StockProduct();
            product.setProductName(chinashopdemoDetail.getProductName());
            product.setPSku(chinashopdemoDetail.getIfPsku());
            stockProductList.add(product);
        }
        return stockProductList;
    }

    private List<StockProduct> mergeStockProductV3(List<DemoMay2021SkuDetail> detailList) {
        List<StockProduct> stockProductList = Lists.newArrayList();
        List<DemoMay2021SkuDetail> list = detailList.stream().filter(detail -> detail.getFacingCount() == 0).collect(Collectors.toList());
        for (DemoMay2021SkuDetail demoMay2021SkuDetail : list) {
            StockProduct product = new StockProduct();
            product.setProductName(demoMay2021SkuDetail.getProductName());
            product.setPSku(demoMay2021SkuDetail.getIfPsku());
            product.setFacingCount(demoMay2021SkuDetail.getFacingCount());
            product.setStandardFacingCount(demoMay2021SkuDetail.getStandardFacingCount());
            stockProductList.add(product);
        }
        return stockProductList;
    }


    private List<ShelfOccupyRate> mergeRateList(List<TResponse> records, Map<String, ChinashopdemoKpi> kpiMap) {
        List<ShelfOccupyRate> rateList = Lists.newArrayList();
        // 最多只要4个点 计数用的
        int i = 0;
        for (TResponse record : records) {
            if (!Objects.isNull(kpiMap.get(record.getId()))) {
                ShelfOccupyRate rate = new ShelfOccupyRate();
                rate.setStartTime(DateUtil.convert2String(record.getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
                rate.setRate(1 - new BigDecimal(kpiMap.get(record.getId()).getShortageRate()).setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
                rateList.add(rate);
                i++;
            }
            if (i >= 4) {
                break;
            }
        }

        return  rateList.stream().sorted((o1, o2) -> DateUtil.convert2Date(o1.getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()).compareTo(DateUtil.convert2Date(o2.getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()))).collect(Collectors.toList());
    }

    private List<ShelfOccupyRate> mergeRateListV2(List<TAnswer> records, Map<String, WmddKpi> kpiMap) {
        List<ShelfOccupyRate> rateList = Lists.newArrayList();
        // 最多只要7个点 计数用的
        int i = 0;
        for (TAnswer record : records) {
            if (!Objects.isNull(kpiMap.get(record.getResponseId()))) {
                ShelfOccupyRate rate = new ShelfOccupyRate();
                rate.setStartTime(DateUtil.convert2String(record.getUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
                rate.setRate(1 - new BigDecimal(String.valueOf(kpiMap.get(record.getResponseId()).getShortageRatio())).setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
                rateList.add(rate);
                i++;
            }
            if (i >= 7) {
                break;
            }
        }

        return  rateList.stream().sorted((o1, o2) -> DateUtil.convert2Date(o1.getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()).compareTo(DateUtil.convert2Date(o2.getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()))).collect(Collectors.toList());
    }

    private List<ShelfOccupyRate> mergeRateListV3(List<TAnswer> records, Map<String, DemoMay2021Kpi> kpiMap) {
        List<ShelfOccupyRate> rateList = Lists.newArrayList();
        // 最多只要4个点 计数用的
        int i = 0;
        for (TAnswer record : records) {
            if (!Objects.isNull(kpiMap.get(record.getResponseId()))) {
                ShelfOccupyRate rate = new ShelfOccupyRate();
                rate.setStartTime(DateUtil.convert2String(record.getUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
                rate.setRate(1 - kpiMap.get(record.getResponseId()).getShortageRatio().setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
                rateList.add(rate);
                i++;
            }
            if (i >= 4) {
                break;
            }
        }

        return  rateList.stream().sorted((o1, o2) -> DateUtil.convert2Date(o1.getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()).compareTo(DateUtil.convert2Date(o2.getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()))).collect(Collectors.toList());
    }


    private String getSecondSuccess(String responseId, List<TResponse> records, Map<String, ChinashopdemoKpi> kpiMap) {
        String second = "";
        for (TResponse record : records) {
            if (!Objects.isNull(kpiMap.get(record.getId())) && !StringUtils.equals(record.getId(), responseId)) {
                second = record.getId();
                break;
            }
        }
        return second;
    }

    private String getSecondSuccessV2(String responseId, List<TAnswer> records, Map<String, WmddKpi> kpiMap) {
        String second = "";
        for (TAnswer record : records) {
            if (!Objects.isNull(kpiMap.get(record.getResponseId())) && !StringUtils.equals(record.getResponseId(), responseId)) {
                second = record.getResponseId();
                break;
            }
        }
        return second;
    }

    private String getSecondSuccessV3(String responseId, List<TAnswer> records, Map<String, DemoMay2021Kpi> kpiMap) {
        String second = "";
        for (TAnswer record : records) {
            if (!Objects.isNull(kpiMap.get(record.getResponseId())) && !StringUtils.equals(record.getResponseId(), responseId)) {
                second = record.getResponseId();
                break;
            }
        }
        return second;
    }

    private InstrumentResult mergeInstrument(ChinashopdemoKpi chinashopdemoKpi) {
        InstrumentResult result = new InstrumentResult();
        result.setPutAwayRate(new BigDecimal(chinashopdemoKpi.getPSKUdist()).setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
        result.setStockRate(new BigDecimal(chinashopdemoKpi.getShortageRate()).setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
        result.setDisplay(chinashopdemoKpi.getIfDisplayValid());
        result.setWrongPrice(chinashopdemoKpi.getWrongPricingCount());
        return result;
    }

    private InstrumentResult mergeInstrumentV2(WmddKpi wmddKpi) {
        InstrumentResult result = new InstrumentResult();
        result.setStockRate(new BigDecimal(String.valueOf(wmddKpi.getShortageRatio())).setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
        result.setSkuOutOfStock(wmddKpi.getShortageSku());
        result.setNumberOfRows(wmddKpi.getFacingCount());
        result.setPskuWrongPrice(wmddKpi.getWrongPricePskuCount());
        result.setPskuLowRowNoodles(wmddKpi.getLowFacingPskuCount());
        result.setPskuOutOfStock(wmddKpi.getShortagePsku());
        return result;
    }

    private InstrumentResult mergeInstrumentV3(DemoMay2021Kpi demoMay2021Kpi) {
        InstrumentResult result = new InstrumentResult();
        result.setStockRate(demoMay2021Kpi.getShortageRatio().setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
        result.setSkuOutOfStock(demoMay2021Kpi.getShortageSkuCount());
        result.setWrongPrice(demoMay2021Kpi.getWrongPriceSkuCount());
        result.setWrongPriceRate(demoMay2021Kpi.getWrongPriceSkuRatio().setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
        result.setPotentialLoss(String.valueOf(demoMay2021Kpi.getPotentialLoss()));
        return result;
    }

    private String getLatestSuccess(List<TResponse> records, Map<String, ChinashopdemoKpi> kpiMap) {
        String responseId = "";
        for (TResponse record : records) {
            if (!Objects.isNull(kpiMap.get(record.getId()))) {
                responseId = record.getId();
                break;
            }
        }
        return responseId;
    }

    private String getLatestSuccessV2(List<TAnswer> records, Map<String, WmddKpi> kpiMap) {
        String responseId = "";
        for (TAnswer record : records) {
            if (!Objects.isNull(kpiMap.get(record.getResponseId()))) {
                responseId = record.getResponseId();
                break;
            }
        }
        return responseId;
    }

    private String getLatestSuccessV3(List<TAnswer> records, Map<String, DemoMay2021Kpi> kpiMap) {
        String responseId = "";
        for (TAnswer record : records) {
            if (!Objects.isNull(kpiMap.get(record.getResponseId()))) {
                responseId = record.getResponseId();
                break;
            }
        }
        return responseId;
    }

    private List<ChinaShopDetail> mergeDetail(List<ChinashopdemoDetail> detailList) {
        List<ChinaShopDetail> list = Lists.newArrayList();
        for (ChinashopdemoDetail detail : detailList) {
            ChinaShopDetail result = new ChinaShopDetail();
            result.setProductName(detail.getProductName());
            result.setFacingNum(detail.getFaceingCount());
            result.setRow(detail.getLevels());
            result.setPSku(detail.getIfPsku());
            list.add(result);
        }
        return list;
    }

    private List<ChinaShopDetail> mergeDetailV2(List<WmddSkuDetail> detailList) {
        List<ChinaShopDetail> list = Lists.newArrayList();
        for (WmddSkuDetail detail : detailList) {
            ChinaShopDetail result = new ChinaShopDetail();
            result.setProductName(detail.getProductName());
            result.setFacingNum(detail.getFacingCount());
            result.setRow(detail.getLevel());
            result.setPrice(detail.getPrice());
            list.add(result);
        }
        return list;
    }

    private List<ChinaShopDetail> mergeDetailV3(List<DemoMay2021SkuDetail> detailList) {
        List<ChinaShopDetail> list = Lists.newArrayList();
        for (DemoMay2021SkuDetail detail : detailList) {
            ChinaShopDetail result = new ChinaShopDetail();
            result.setProductName(detail.getProductName());
            result.setFacingNum(detail.getFacingCount());
            result.setRow(detail.getLevel());
            result.setPSku(detail.getIfPsku());
            list.add(result);
        }
        return list;
    }

    private List<HistoryRecord> mergeRecord(List<TResponse> records, Map<String, ChinashopdemoKpi> kpiMap) {
        List<HistoryRecord> recordList = Lists.newArrayList();
        // 第一个
        HistoryRecord first = new HistoryRecord();
        first.setStartTime(DateUtil.convert2String(records.get(0).getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
        first.setStatus(Objects.isNull(kpiMap.get(records.get(0).getId())) ? 0 : 1);
        recordList.add(first);
        if (Objects.isNull(records.get(1))) {
            return recordList;
        }
        // 第二个
        HistoryRecord second = new HistoryRecord();
        second.setStartTime(DateUtil.convert2String(records.get(1).getStartTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
        second.setStatus(Objects.isNull(kpiMap.get(records.get(1).getId())) ? 0 : 1);
        recordList.add(second);
        return recordList;
    }

    private List<HistoryRecord> mergeRecordV2(List<TAnswer> records, Map<String, WmddKpi> kpiMap) {
        List<HistoryRecord> recordList = Lists.newArrayList();
        // 第一个
        HistoryRecord first = new HistoryRecord();
        first.setStartTime(DateUtil.convert2String(records.get(0).getUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
        first.setStatus(Objects.isNull(kpiMap.get(records.get(0).getResponseId())) ? 0 : 1);
        recordList.add(first);
        if (Objects.isNull(records.get(1))) {
            return recordList;
        }
        // 第二个
        HistoryRecord second = new HistoryRecord();
        second.setStartTime(DateUtil.convert2String(records.get(1).getUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
        second.setStatus(Objects.isNull(kpiMap.get(records.get(1).getResponseId())) ? 0 : 1);
        recordList.add(second);
        return recordList;
    }

    private List<HistoryRecord> mergeRecordV3(List<TAnswer> records, Map<String, DemoMay2021Kpi> kpiMap) {
        List<HistoryRecord> recordList = Lists.newArrayList();
        // 第一个
        HistoryRecord first = new HistoryRecord();
        first.setStartTime(DateUtil.convert2String(records.get(0).getUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
        first.setStatus(Objects.isNull(kpiMap.get(records.get(0).getResponseId())) ? 0 : 1);
        recordList.add(first);
        if (Objects.isNull(records.get(1))) {
            return recordList;
        }
        // 第二个
        HistoryRecord second = new HistoryRecord();
        second.setStartTime(DateUtil.convert2String(records.get(1).getUpdateTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
        second.setStatus(Objects.isNull(kpiMap.get(records.get(1).getResponseId())) ? 0 : 1);
        recordList.add(second);
        return recordList;
    }

    /**
     * 获取店内区域列表
     * @return
     */
    public List<WmddQuestionMapDTO> getWmddQuestionMapList() {

        List<WmddQuestionMapDTO> wmddQuestionMapDTOList = new ArrayList<>();
        LambdaQueryWrapper<WmddQuestionMap> wmddQuestionMapLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //取最近七条
        wmddQuestionMapLambdaQueryWrapper.orderByDesc(WmddQuestionMap::getId).last("limit 7");
        List<WmddQuestionMap> wmddQuestionMapList = wmddQuestionMapMapper.selectList(wmddQuestionMapLambdaQueryWrapper);
        for (WmddQuestionMap wmddQuestionMap : wmddQuestionMapList){
            String lastVisitTime = tResponseMapper.selectLastVisitTimeByQid(wmddQuestionMap.getQuestionId());
            WmddQuestionMapDTO wmddQuestionMapDTO = new WmddQuestionMapDTO();
            wmddQuestionMapDTO.setQuestionId(wmddQuestionMap.getQuestionId());
            wmddQuestionMapDTO.setQuestionName(wmddQuestionMap.getQuestionName());
            if (StringUtils.isNotBlank(lastVisitTime)){
                wmddQuestionMapDTO.setLastVisitTime(lastVisitTime);
            }else {
                wmddQuestionMapDTO.setLastVisitTime("无");
            }
            wmddQuestionMapDTOList.add(wmddQuestionMapDTO);
        }

        return wmddQuestionMapDTOList;
    }

    /**
     * 获取店内区域列表 V3
     * @return
     */
    public List<WmddQuestionMapDTO> getChinaShopQuestionMapListV3() {

        List<WmddQuestionMapDTO> chinaShopQuestionMapDTOList = new ArrayList<>();
        LambdaQueryWrapper<DemoMay2021QuestionMap> demoMay2021QuestionMapLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //取最近七条
        demoMay2021QuestionMapLambdaQueryWrapper.orderByDesc(DemoMay2021QuestionMap::getId).last("limit 7");
        List<DemoMay2021QuestionMap> demoMay2021QuestionMapList = demoMay2021QuestionMapMapper.selectList(demoMay2021QuestionMapLambdaQueryWrapper);
        for (DemoMay2021QuestionMap demoMay2021QuestionMap : demoMay2021QuestionMapList){
            String lastVisitTime = tResponseMapper.selectLastVisitTimeByQid(demoMay2021QuestionMap.getQuestionId());
            WmddQuestionMapDTO wmddQuestionMapDTO = new WmddQuestionMapDTO();
            wmddQuestionMapDTO.setQuestionId(demoMay2021QuestionMap.getQuestionId());
            wmddQuestionMapDTO.setQuestionName(demoMay2021QuestionMap.getQuestionName());
            if (StringUtils.isNotBlank(lastVisitTime)){
                wmddQuestionMapDTO.setLastVisitTime(lastVisitTime);
            }else {
                wmddQuestionMapDTO.setLastVisitTime("无");
            }
            chinaShopQuestionMapDTOList.add(wmddQuestionMapDTO);
        }
        return chinaShopQuestionMapDTOList;
    }
}
