package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-23
 */
public class BnhdStoreRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * rid
     */
    private String responseId;

    /**
     * 门店编号
     */
    private String storeName;

    /**
     * 拍摄照片
     */
    private Integer picCount;

    /**
     * 翻拍照片
     */
    private Integer remakeCount;

    /**
     * 货架照片是否完整
     */
    @TableField("if_shelf_full")
    private Integer ifShelfFull;


    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getPicCount() {
        return picCount;
    }

    public void setPicCount(Integer picCount) {
        this.picCount = picCount;
    }
    public Integer getRemakeCount() {
        return remakeCount;
    }

    public void setRemakeCount(Integer remakeCount) {
        this.remakeCount = remakeCount;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIfShelfFull() {
        return ifShelfFull;
    }

    public void setIfShelfFull(Integer ifShelfFull) {
        this.ifShelfFull = ifShelfFull;
    }
}
