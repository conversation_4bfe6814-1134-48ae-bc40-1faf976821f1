package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
public class WmddSkuList extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer productId;

    private String productName;

    private String facingMin;

    private BigDecimal correctPrice;

    private Boolean isPsku;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public String getFacingMin() {
        return facingMin;
    }

    public void setFacingMin(String facingMin) {
        this.facingMin = facingMin;
    }
    public BigDecimal getCorrectPrice() {
        return correctPrice;
    }

    public void setCorrectPrice(BigDecimal correctPrice) {
        this.correctPrice = correctPrice;
    }
    public Boolean getPsku() {
        return isPsku;
    }

    public void setPsku(Boolean isPsku) {
        this.isPsku = isPsku;
    }

    @Override
    public String toString() {
        return "WmddSkuList{" +
        "id=" + id +
        ", productId=" + productId +
        ", productName=" + productName +
        ", facingMin=" + facingMin +
        ", correctPrice=" + correctPrice +
        ", isPsku=" + isPsku +
        "}";
    }
}
