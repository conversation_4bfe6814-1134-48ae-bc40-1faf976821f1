package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.byhealth.ByhealthBiResult;
import com.lenztech.bi.enterprise.dto.common.CommonBiImage;
import com.lenztech.bi.enterprise.dto.common.CommonBiProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: zhangjie
 * @Date: 29/12/18 上午11:31
 */
@Mapper
@DS("lenzbi")
public interface ByhealthReportMapper {

    /**
     * 查询图片集合
     *
     * @param responseId 答卷Id
     * @return List<CommonBiImage>
     */
    List<ByhealthBiResult> getTargetList(@Param("responseId") String responseId);

}
