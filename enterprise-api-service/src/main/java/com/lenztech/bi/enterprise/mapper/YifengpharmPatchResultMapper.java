package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.entity.YifengpharmPatchResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@DS("lenzbi")
public interface YifengpharmPatchResultMapper extends BaseMapper<YifengpharmPatchResult> {

    List<ProductPriceInfo> getProductPriceInfo(@Param("responseId") String responseId);

}
