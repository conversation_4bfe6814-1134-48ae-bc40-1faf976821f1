package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.util.Date;

/**
 * <p>
 * 申诉信息主表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
public class AppealInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 申诉主键
     */
    @TableId(value = "appeal_id", type = IdType.AUTO)
    private Integer appealId;

    /**
     * 对应的客户
     */
    private Integer companyId;

    /**
     * 问卷id
     */
    private String taskidOwner;

    /**
     * 问卷名称，冗余字段
     */
    private String tasknameOwner;

    /**
     * 问卷答题开始时间（冗余字段）
     */
    @TableField("startTime")
    private String startTime;

    /**
     * 网点名称
     */
    private String branchName;

    /**
     * 核心机构号（冗余字段）
     */
    @TableField("addressIDnum")
    private String addressIDnum;

    /**
     * 答卷Id
     */
    private String responseId;

    /**
     * 申诉名称
     */
    private String appealName;

    /**
     * 申诉描述
     */
    private String appealContent;

    /**
     * 申诉图片 ,用英文分号分割
     */
    private String appealImages;

    /**
     * 申诉附件
     */
    private String appealFiles;

    /**
     * 进度，流程走到了哪一步，关联申诉流程环节表。 跟node_index一致。
     */
    private Integer appealProgress;

    /**
     * 审核状态， 0：审核中、1：已审核 2放弃初审
     */
    private Integer checkState;

    /**
     * 审核结果，0：未通过、1：已通过
     */
    private Integer checkResult;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 申诉的问题编号
     */
    private String questionId;

    /**
     * 申诉的问题名称，冗余字段。需要拼接题号，以“题号.标题”的形式出现
     */
    private String questionName;

    /**
     * 类型（0：文本，1：单选，2：多选，3：图片,5：音频，6：视频）
     */
    private String questionType;

    /**
     * 对应问题的答案
     */
    private String questionAnswer;

    /**
     * 是否需要再次申诉（0需要再次申诉，1不需要再次申诉）
     */
    private Integer appleagain;

    /**
     * 1是图像识别申诉
     */
    private Integer shensuType;

    public Integer getAppealId() {
        return appealId;
    }

    public void setAppealId(Integer appealId) {
        this.appealId = appealId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getTaskidOwner() {
        return taskidOwner;
    }

    public void setTaskidOwner(String taskidOwner) {
        this.taskidOwner = taskidOwner;
    }

    public String getTasknameOwner() {
        return tasknameOwner;
    }

    public void setTasknameOwner(String tasknameOwner) {
        this.tasknameOwner = tasknameOwner;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getAddressIDnum() {
        return addressIDnum;
    }

    public void setAddressIDnum(String addressIDnum) {
        this.addressIDnum = addressIDnum;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String getAppealName() {
        return appealName;
    }

    public void setAppealName(String appealName) {
        this.appealName = appealName;
    }

    public String getAppealContent() {
        return appealContent;
    }

    public void setAppealContent(String appealContent) {
        this.appealContent = appealContent;
    }

    public String getAppealImages() {
        return appealImages;
    }

    public void setAppealImages(String appealImages) {
        this.appealImages = appealImages;
    }

    public String getAppealFiles() {
        return appealFiles;
    }

    public void setAppealFiles(String appealFiles) {
        this.appealFiles = appealFiles;
    }

    public Integer getAppealProgress() {
        return appealProgress;
    }

    public void setAppealProgress(Integer appealProgress) {
        this.appealProgress = appealProgress;
    }

    public Integer getCheckState() {
        return checkState;
    }

    public void setCheckState(Integer checkState) {
        this.checkState = checkState;
    }

    public Integer getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(Integer checkResult) {
        this.checkResult = checkResult;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getQuestionAnswer() {
        return questionAnswer;
    }

    public void setQuestionAnswer(String questionAnswer) {
        this.questionAnswer = questionAnswer;
    }

    public Integer getAppleagain() {
        return appleagain;
    }

    public void setAppleagain(Integer appleagain) {
        this.appleagain = appleagain;
    }

    public Integer getShensuType() {
        return shensuType;
    }

    public void setShensuType(Integer shensuType) {
        this.shensuType = shensuType;
    }
}
