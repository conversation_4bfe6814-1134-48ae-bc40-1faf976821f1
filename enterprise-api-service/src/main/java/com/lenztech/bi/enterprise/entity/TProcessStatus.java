package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TProcessStatus implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String taskId;

    private String responseId;

    private String qid;

    private Integer status;

    private LocalDateTime startTime;

    private LocalDateTime updateTime;


}
