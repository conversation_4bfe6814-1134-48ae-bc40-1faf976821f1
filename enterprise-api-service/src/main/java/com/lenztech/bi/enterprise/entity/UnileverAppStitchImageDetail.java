package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 联合利华拼接图
 *
 * <AUTHOR>
 * @since 2022-01-17 14:55:30
 */
@Data
public class UnileverAppStitchImageDetail implements Serializable {
    private static final long serialVersionUID = -39791981003313458L;
    /**
    * 自增id
    */
    private Integer id;
    /**
    * 答卷id
    */
    private String responseId;
    /**
    * 问题id
    */
    private String questionId;

    /**
     * 组号
     */
    private Integer groupNo;

    /**
    * 拼接url
    */
    private String jointUrl;

    /**
    * 创建时间
    */
    private Date createTime;

}