package com.lenztech.bi.enterprise.controller.enterprise;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.service.enterprise.UnileverServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/unilever")
@Slf4j
public class UnileverController {

    @Autowired
    private UnileverServiceImpl unileverService;

    /**
     * 处理昨天的Unilever数据并发送邮件
     */
    @GetMapping("/process")
    public ResponseData<String> processUnileverData() {
        try {
            log.info("开始处理昨天的Unilever数据");
            new Thread(() -> unileverService.processUnileverData(null)).start();
            return ResponseData.success().data("处理完成");
        } catch (Exception e) {
            log.error("处理昨天的Unilever数据失败", e);
            return ResponseData.failure().msg("处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理指定执行ID的Unilever数据并发送邮件
     * @param taskAddressIdList 执行ID列表
     */
    @PostMapping("/processById")
    public ResponseData<String> processUnileverDataById(@RequestBody List<Long> taskAddressIdList) {
        try {
            log.info("开始处理指定执行ID的Unilever数据，taskAddressIds: {}", taskAddressIdList);
            new Thread(() -> unileverService.processUnileverData(taskAddressIdList)).start();
            return ResponseData.success().data("处理完成");
        } catch (Exception e) {
            log.error("处理指定执行ID的Unilever数据失败，taskAddressIds: {}", taskAddressIdList, e);
            return ResponseData.failure().msg("处理失败: " + e.getMessage());
        }
    }
}