package com.lenztech.bi.enterprise.controller.enterprise;

import com.lenztech.bi.enterprise.service.enterprise.OptimusServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/optimus")
public class OptimusController {

    @Autowired
    private OptimusServiceImpl optimusService;

    @PostMapping("/calc")
    public void calc() {
        optimusService.calc(null);
    }

    @PostMapping("/calcById")
    public void calcById(@RequestBody List<Long> taskAddressIds) {
        optimusService.calc(taskAddressIds);
    }
} 