//package com.lenztech.bi.enterprise.service;
//
//import com.lenztech.bi.enterprise.entity.BiReportBrand;
//import com.lenztech.bi.enterprise.entity.BiReportBrandExample;
//import com.lenztech.bi.enterprise.mapper.BiReportBrandMapper;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.HashMap;
//import java.util.List;
//
///**
// * @Description: bi 品牌
// * @Author: zhangjie
// * @Date: 3/26/20 PM1:59
// */
//@Service
//public class BiBrandService {
//
//
//    @Autowired
//    private BiReportBrandMapper biReportBrandMapper;
//
//
//    /**
//     * 品牌id与品牌信息对应关系
//     * @param taskId
//     * @return
//     */
//    public HashMap<Integer, BiReportBrand> brandIdHashMap(String taskId){
//        HashMap<Integer,BiReportBrand> brandIdBrandMap = new HashMap<>();
//
//        List<BiReportBrand> brandList = getBrandList(taskId);
//        if (CollectionUtils.isNotEmpty(brandList)){
//            for (BiReportBrand biReportBrand : brandList){
//                brandIdBrandMap.put(biReportBrand.getId(), biReportBrand);
//            }
//        }
//        return brandIdBrandMap;
//    }
//
//    /**
//     * 获取品牌列表
//     * @param taskId
//     * @return
//     */
//    public List<BiReportBrand> getBrandList(String taskId){
//        BiReportBrandExample example = new BiReportBrandExample();
//        example.createCriteria().andTaskIdEqualTo(taskId);
//
//        List<BiReportBrand> brandList = biReportBrandMapper.selectByExample(example);
//
//        return brandList;
//    }
//
//}
