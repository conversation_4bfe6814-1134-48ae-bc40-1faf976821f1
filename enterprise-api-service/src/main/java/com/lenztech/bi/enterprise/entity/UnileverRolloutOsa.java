package com.lenztech.bi.enterprise.entity;

public class UnileverRolloutOsa {
    private Integer id;

    private String responseId;

    private String cotcCategoryName;

    private String cotcCategoryDetail;

    private String brand;

    private String cotcCode;

    private String cotcSkuDescription;

    private String categoryCode;

    private String categoryName;

    private String categoryNameEn;

    private String availability;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId == null ? null : responseId.trim();
    }

    public String getCotcCategoryName() {
        return cotcCategoryName;
    }

    public void setCotcCategoryName(String cotcCategoryName) {
        this.cotcCategoryName = cotcCategoryName == null ? null : cotcCategoryName.trim();
    }

    public String getCotcCategoryDetail() {
        return cotcCategoryDetail;
    }

    public void setCotcCategoryDetail(String cotcCategoryDetail) {
        this.cotcCategoryDetail = cotcCategoryDetail == null ? null : cotcCategoryDetail.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getCotcCode() {
        return cotcCode;
    }

    public void setCotcCode(String cotcCode) {
        this.cotcCode = cotcCode == null ? null : cotcCode.trim();
    }

    public String getCotcSkuDescription() {
        return cotcSkuDescription;
    }

    public void setCotcSkuDescription(String cotcSkuDescription) {
        this.cotcSkuDescription = cotcSkuDescription == null ? null : cotcSkuDescription.trim();
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode == null ? null : categoryCode.trim();
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    public String getCategoryNameEn() {
        return categoryNameEn;
    }

    public void setCategoryNameEn(String categoryNameEn) {
        this.categoryNameEn = categoryNameEn == null ? null : categoryNameEn.trim();
    }

    public String getAvailability() {
        return availability;
    }

    public void setAvailability(String availability) {
        this.availability = availability == null ? null : availability.trim();
    }
}