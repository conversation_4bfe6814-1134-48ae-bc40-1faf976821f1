package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * Brand级别二陈数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pg_optimus_brand_level_display_count")
public class PgOptimusBrandLevelDisplayCount extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 执行时间
     */
    private Date execDate;

    /**
     * 客户名称
     */
    private String banner;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * group_id
     */
    private String responseGroupId;

    /**
     * rid
     */
    private String responseId;

    private String category;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 拜访日期时间
     */
    private LocalDateTime visitDatetime;

    /**
     * 拜访第n次/月
     */
    private Integer visitCycle;

    private Integer recordId;

    /**
     * 拜访年份
     */
    private String visitYear;

    /**
     * 拜访月份
     */
    private String visitMonth;

    /**
     * 拜访周数
     */
    private String visitWeek;

    /**
     * display类型
     */
    private String displayType;

    /**
     * dispaly堆叠的面位数
     */
    private Integer displayStackFacing;

    /**
     * display个数
     */
    private Integer displayCnt;

    /**
     * ByBrand所有堆总的个数
     */
    private Integer totalDisplayCountBrand;

    /**
     * display占比
     */
    private BigDecimal displayCountShare;

    /**
     * 数据是否被抽取过，抽取次数
     */
    private Integer extract;

    @ApiModelProperty("是否是宝洁，0非宝洁/1宝洁")
    private Boolean isPgProductFlag;

    /**
     * 最后一次抽取时间
     */
    private LocalDateTime lastExtractTime;

}
