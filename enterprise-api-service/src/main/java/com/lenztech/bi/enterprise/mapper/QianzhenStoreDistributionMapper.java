package com.lenztech.bi.enterprise.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.QianzhenStoreDistribution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 千镇门店含有的sku分销信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Mapper
public interface QianzhenStoreDistributionMapper extends BaseMapper<QianzhenStoreDistribution> {
    List<QianzhenStoreDistribution> getListDynamicTable(@Param("responseId") String responseId, @Param("prefix") String prefix);
}
