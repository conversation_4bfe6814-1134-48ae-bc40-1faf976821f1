package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.junlebao.RepeatBiResultResp;
import com.lenztech.bi.enterprise.dto.pec.PecBiTargetResp;

/**
 * 统一bi指标Service
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
public interface PecReportService {

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return JmlBiTargetResp
     */
    PecBiTargetResp getBiTargetList(String responseId);

    RepeatBiResultResp getPecRepeatResult(String responseId);
}
