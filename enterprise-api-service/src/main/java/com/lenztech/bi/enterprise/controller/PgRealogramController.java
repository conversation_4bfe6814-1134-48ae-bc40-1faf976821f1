package com.lenztech.bi.enterprise.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.CustomerResponseData;
import com.lenztech.bi.enterprise.dto.pg.PgRealogramDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgRealogramQueryParam;
import com.lenztech.bi.enterprise.dto.pg.PgRealogramShelfDTO;
import com.lenztech.bi.enterprise.service.PgRealogramService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * PG Realogram 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/biResult/realogram/")
public class PgRealogramController {

    public static final Logger logger = LoggerFactory.getLogger(PgRealogramController.class);
    
    @Autowired
    PgRealogramService pgRealogramService;

    /**
     * 分页查询 Realogram Display 数据
     * @param param 查询参数
     * @return Realogram Display数据列表（分页）
     */
    @RequestMapping(value = "getRealogramDisplayPageList", method = RequestMethod.POST)
    @ControllerAnnotation(use = "分页查询 Realogram Display 数据")
    public CustomerResponseData<IPage<PgRealogramDisplayDTO>> getRealogramDisplayPageList(
            @RequestBody PgRealogramQueryParam param) {
        try {
            // 使用优化后的查询逻辑，只传递必要的参数
            IPage<PgRealogramDisplayDTO> result = pgRealogramService.getRealogramDisplayPageList(
                    param.getSearchDate(), param.getResponseId(), param.getResponseGroupId(), null,
                    null, null, null, null,
                    null, null, null,
                    null, null, null,
                    param.getPageNum(), param.getPageSize());
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("分页查询 Realogram Display 数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 分页查询 Realogram Shelf 数据
     * @param param 查询参数
     * @return Realogram Shelf数据列表（分页）
     */
    @RequestMapping(value = "getRealogramShelfPageList", method = RequestMethod.POST)
    @ControllerAnnotation(use = "分页查询 Realogram Shelf 数据")
    public CustomerResponseData<IPage<PgRealogramShelfDTO>> getRealogramShelfPageList(
            @RequestBody PgRealogramQueryParam param) {
        try {
            // 使用优化后的查询逻辑，只传递必要的参数
            IPage<PgRealogramShelfDTO> result = pgRealogramService.getRealogramShelfPageList(
                    param.getSearchDate(), param.getResponseId(), param.getResponseGroupId(), null,
                    null, null, null, null,
                    null, null, null,
                    null, null, null,
                    param.getPageNum(), param.getPageSize());
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("分页查询 Realogram Shelf 数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据responseId获取Display数据
     * @param responseId 答卷ID
     * @return Realogram Display数据列表
     */
    @RequestMapping(value = "getRealogramDisplayByResponseId", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据responseId获取Display数据")
    public CustomerResponseData<List<PgRealogramDisplayDTO>> getRealogramDisplayByResponseId(
            @RequestParam("responseId") String responseId) {
        try {
            List<PgRealogramDisplayDTO> result = pgRealogramService.getRealogramDisplayByResponseId(responseId);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据responseId获取Display数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据responseId获取Shelf数据
     * @param responseId 答卷ID
     * @return Realogram Shelf数据列表
     */
    @RequestMapping(value = "getRealogramShelfByResponseId", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据responseId获取Shelf数据")
    public CustomerResponseData<List<PgRealogramShelfDTO>> getRealogramShelfByResponseId(
            @RequestParam("responseId") String responseId) {
        try {
            List<PgRealogramShelfDTO> result = pgRealogramService.getRealogramShelfByResponseId(responseId);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据responseId获取Shelf数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据门店编码查询Display数据
     * @param storeCode 门店编码
     * @return Display数据列表
     */
    @RequestMapping(value = "getRealogramDisplayByStoreCode", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据门店编码查询Display数据")
    public CustomerResponseData<IPage<PgRealogramDisplayDTO>> getRealogramDisplayByStoreCode(
            @RequestParam("storeCode") String storeCode,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRealogramDisplayDTO> result = pgRealogramService.getRealogramDisplayPageList(
                    null, null,null, storeCode, null, null, null, null, null, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据门店编码查询Display数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据门店编码查询Shelf数据
     * @param storeCode 门店编码
     * @return Shelf数据列表
     */
    @RequestMapping(value = "getRealogramShelfByStoreCode", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据门店编码查询Shelf数据")
    public CustomerResponseData<IPage<PgRealogramShelfDTO>> getRealogramShelfByStoreCode(
            @RequestParam("storeCode") String storeCode,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRealogramShelfDTO> result = pgRealogramService.getRealogramShelfPageList(
                    null, null, null, storeCode, null, null, null, null, null, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据门店编码查询Shelf数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据品类查询Display数据
     * @param category 品类名称
     * @return Display数据列表
     */
    @RequestMapping(value = "getRealogramDisplayByCategory", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据品类查询Display数据")
    public CustomerResponseData<IPage<PgRealogramDisplayDTO>> getRealogramDisplayByCategory(
            @RequestParam("category") String category,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRealogramDisplayDTO> result = pgRealogramService.getRealogramDisplayPageList(
                    null, null,null, null, category, null, null, null, null, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据品类查询Display数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据品类查询Shelf数据
     * @param category 品类名称
     * @return Shelf数据列表
     */
    @RequestMapping(value = "getRealogramShelfByCategory", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据品类查询Shelf数据")
    public CustomerResponseData<IPage<PgRealogramShelfDTO>> getRealogramShelfByCategory(
            @RequestParam("category") String category,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRealogramShelfDTO> result = pgRealogramService.getRealogramShelfPageList(
                    null, null,null, null, category, null, null, null, null, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据品类查询Shelf数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据品牌查询Display数据
     * @param brand 品牌名称
     * @return Display数据列表
     */
    @RequestMapping(value = "getRealogramDisplayByBrand", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据品牌查询Display数据")
    public CustomerResponseData<IPage<PgRealogramDisplayDTO>> getRealogramDisplayByBrand(
            @RequestParam("brand") String brand,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRealogramDisplayDTO> result = pgRealogramService.getRealogramDisplayPageList(
                    null, null,null, null, null, brand, null, null, null, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据品牌查询Display数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据品牌查询Shelf数据
     * @param brand 品牌名称
     * @return Shelf数据列表
     */
    @RequestMapping(value = "getRealogramShelfByBrand", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据品牌查询Shelf数据")
    public CustomerResponseData<IPage<PgRealogramShelfDTO>> getRealogramShelfByBrand(
            @RequestParam("brand") String brand,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            IPage<PgRealogramShelfDTO> result = pgRealogramService.getRealogramShelfPageList(
                    null, null,null, null, null, brand, null, null, null, null, null, null, null, null, pageNum, pageSize);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据品牌查询Shelf数据异常", e);
            return CustomerResponseData.failure();
        }
    }
}
