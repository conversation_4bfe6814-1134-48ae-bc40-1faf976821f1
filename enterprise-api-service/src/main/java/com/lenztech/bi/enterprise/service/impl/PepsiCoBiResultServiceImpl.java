package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lenztech.bi.enterprise.comon.AppealStatusEnums;
import com.lenztech.bi.enterprise.dto.bi.PepsiCoBiResultResp;
import com.lenztech.bi.enterprise.dto.bi.ProductDistribDetail;
import com.lenztech.bi.enterprise.dto.bi.ProductImageDetail;
import com.lenztech.bi.enterprise.entity.BaishiQianzhenType;
import com.lenztech.bi.enterprise.entity.QianzhenSkuDistribution;
import com.lenztech.bi.enterprise.entity.QianzhenStoreDistribution;
import com.lenztech.bi.enterprise.entity.QianzhenStoreResult;
import com.lenztech.bi.enterprise.mapper.QianzhenSkuDistributionMapper;
import com.lenztech.bi.enterprise.mapper.QianzhenStoreDistributionMapper;
import com.lenztech.bi.enterprise.mapper.QianzhenStoreResultMapper;
import com.lenztech.bi.enterprise.service.PepsiCoBiResultService;
import com.lenztech.bi.enterprise.utils.CglibCopyBeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <p>
 * 百事bi报表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PepsiCoBiResultServiceImpl implements PepsiCoBiResultService {


    @Autowired
    BaishiQianzhenTypeServiceImpl baishiQianzhenTypeServiceImpl;


    @Autowired
    private QianzhenSkuDistributionMapper qianzhenSkuDistributionMapper;

    @Autowired
    private QianzhenStoreDistributionMapper qianzhenStoreDistributionMapper;

    @Autowired
    private QianzhenStoreResultMapper qianzhenStoreResultMapper;

    @Override
    public PepsiCoBiResultResp getStoreInfo(String responseId, String taskId) {

        QueryWrapper<BaishiQianzhenType> queryTypeWrapper = new QueryWrapper<>();
        queryTypeWrapper.eq("task_id", taskId);
        BaishiQianzhenType one = baishiQianzhenTypeServiceImpl.getOne(queryTypeWrapper);
        if (null == one || StringUtils.isBlank(one.getTablePrefix())) {
            return new PepsiCoBiResultResp();
        }
        return getStoreInfoDynamicTable(responseId, one.getTablePrefix());
    }

    public PepsiCoBiResultResp getStoreInfoDynamicTable(String responseId, String prefix) {

        List<QianzhenStoreDistribution> storeDistribu = qianzhenStoreDistributionMapper.getListDynamicTable(responseId, prefix);
        if (CollectionUtils.isEmpty(storeDistribu)) {
            return new PepsiCoBiResultResp();
        }

        List<QianzhenSkuDistribution> imageSku = qianzhenSkuDistributionMapper.getListDynamicTable(responseId, prefix);

        List<QianzhenStoreResult> qianzhen = qianzhenStoreResultMapper.getListDynamicTable(responseId, prefix);
        if (CollectionUtils.isEmpty(qianzhen)) {
            return new PepsiCoBiResultResp();
        }

        QianzhenStoreResult result = qianzhen.get(0);

        PepsiCoBiResultResp pepsiCoBiResultResp = new PepsiCoBiResultResp();
        CglibCopyBeanUtil.basicCopyBean(result, pepsiCoBiResultResp);
        QianzhenStoreDistribution qianzhenStoreDistribution = storeDistribu.get(0);
        pepsiCoBiResultResp.setStoreName(qianzhenStoreDistribution.getStoreName());
        pepsiCoBiResultResp.setStoreNum(qianzhenStoreDistribution.getStoreNum());
        pepsiCoBiResultResp.setStoreAddress(qianzhenStoreDistribution.getStoreAddress());
        pepsiCoBiResultResp.setStoreType(qianzhenStoreDistribution.getStoreType());
        pepsiCoBiResultResp.setVisitTime(qianzhenStoreDistribution.getVisitTime());
        pepsiCoBiResultResp.setFacingCount(result.getFacingCount());
        pepsiCoBiResultResp.setSos((BigDecimal.valueOf(result.getPercentage()).multiply(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP) + "%" );
        pepsiCoBiResultResp.setTargetRate(result.getTargetRate());
        pepsiCoBiResultResp.setMfacTarget(result.getMfacTarget());
        if (AppealStatusEnums.START.getValue().equals(qianzhenStoreDistribution.getAppealStatus())) {
            pepsiCoBiResultResp.setAppealStatus(AppealStatusEnums.START.getCode());
        } else if (AppealStatusEnums.ING.getValue().equals(qianzhenStoreDistribution.getAppealStatus())) {
            pepsiCoBiResultResp.setAppealStatus(AppealStatusEnums.ING.getCode());

        } else if (AppealStatusEnums.END.getValue().equals(qianzhenStoreDistribution.getAppealStatus())) {
            pepsiCoBiResultResp.setAppealStatus(AppealStatusEnums.END.getCode());
        }

        pepsiCoBiResultResp.setProductDistribList(CglibCopyBeanUtil.doBatchClone(storeDistribu, ProductDistribDetail.class));

        pepsiCoBiResultResp.setProductImageList(CglibCopyBeanUtil.doBatchClone(imageSku, ProductImageDetail.class));

        return pepsiCoBiResultResp;
    }


}
