package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (HipacPatchResult)实体类
 *
 * <AUTHOR>
 * @since 2021-05-24 22:04:25
 */
@Data
public class HipacPatchResult implements Serializable {
    private static final long serialVersionUID = 246345926507165197L;

    private Integer id;

    private String responseId;

    private String imgId;

    private String skuCode;

    private String skuName;

    private String coordinate;

    private String mosaicCoords;

    private Integer facingCount;

    private Date updateTime;

}