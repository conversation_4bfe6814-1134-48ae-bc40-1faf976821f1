package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.dto.jzv2.JzDisplay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-10-11 16:53
 */
@DS("lenzbi")
@Mapper
public interface JzV2DisplayMapper {

    /**
     * 查询图片集合
     *
     * @param responseId
     * @return
     */
    List<JzDisplay> getImageList(@Param("responseId") String responseId);

}
