//package com.lenztech.bi.enterprise.service.impl;
//
//import com.google.common.collect.ImmutableMap;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.pg.PgPocBrandFacingListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocRuleListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocSkuExistListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocSkuNotExistListDTO;
//import com.lenztech.bi.enterprise.entity.*;
//import com.lenztech.bi.enterprise.mapper.bienterprise.*;
//import com.lenztech.bi.enterprise.service.PgEnterpriseService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @version V1.0
// * @date 2019-11-07 15:19
// * @since JDK 1.8
// */
//@Service
//@Slf4j
//public class PgEnterpriseServiceImpl implements PgEnterpriseService {
//
//    private PgPocBrandFacingMapper pgPocBrandFacingMapper;
//    private PgPocKpiMapper pgPocKpiMapper;
//    private PgPocRuleMapper pgPocRuleMapper;
//    private PgPocSkuExistMapper pgPocSkuExistMapper;
//    private PgPocSkuNotExistMapper pgPocSkuNotExistMapper;
//
//    @Autowired
//    public PgEnterpriseServiceImpl(PgPocBrandFacingMapper pgPocBrandFacingMapper,
//                                   PgPocKpiMapper pgPocKpiMapper,
//                                   PgPocRuleMapper pgPocRuleMapper,
//                                   PgPocSkuExistMapper pgPocSkuExistMapper,
//                                   PgPocSkuNotExistMapper pgPocSkuNotExistMapper) {
//        this.pgPocBrandFacingMapper = pgPocBrandFacingMapper;
//        this.pgPocKpiMapper = pgPocKpiMapper;
//        this.pgPocRuleMapper = pgPocRuleMapper;
//        this.pgPocSkuExistMapper = pgPocSkuExistMapper;
//        this.pgPocSkuNotExistMapper = pgPocSkuNotExistMapper;
//    }
//
//    @Override
//    public ResponseData<PgPocBrandFacingListDTO> listPgPocBrandFacingEntity(String responseId) {
//        List<PgPocBrandFacingEntity> pgPocBrandFacingEntities = pgPocBrandFacingMapper.listPgPocBrandFacingEntity(ImmutableMap.<String, Object>builder()
//                .put("responseId", responseId)
//                .build());
//        return ResponseData.success(new PgPocBrandFacingListDTO(pgPocBrandFacingEntities));
//    }
//
//    @Override
//    public ResponseData<PgPocKpiEntity> selectPgPocKpiEntityByRid(String responseId) {
//        PgPocKpiEntity pgPocKpiEntity = pgPocKpiMapper.selectPgPocKpiEntityByRid(ImmutableMap.<String, Object>builder()
//                .put("responseId", responseId)
//                .build());
//        return ResponseData.success(pgPocKpiEntity);
//    }
//
//    @Override
//    public ResponseData<PgPocRuleListDTO> listPgPocRuleEntity(String responseId) {
//        List<PgPocRuleEntity> pgPocRuleEntities = pgPocRuleMapper.listPgPocRuleEntity(ImmutableMap.<String, Object>builder()
//                .put("responseId", responseId)
//                .build());
//        return ResponseData.success(new PgPocRuleListDTO(pgPocRuleEntities));
//    }
//
//    @Override
//    public ResponseData<PgPocSkuExistListDTO> listPgPocSkuExistEntity(String responseId) {
//        List<PgPocSkuExistEntity> pgPocSkuExistEntities = pgPocSkuExistMapper.listPgPocSkuExistEntity(ImmutableMap.<String, Object>builder()
//                .put("responseId", responseId)
//                .build());
//        return ResponseData.success(new PgPocSkuExistListDTO(pgPocSkuExistEntities));
//    }
//
//    @Override
//    public ResponseData<PgPocSkuNotExistListDTO> listPgPocSkuNotExistEntity(String responseId) {
//        List<PgPocSkuNotExistEntity> pgPocSkuNotExistEntities = pgPocSkuNotExistMapper.listPgPocSkuNotExistEntity(ImmutableMap.<String, Object>builder()
//                .put("responseId", responseId)
//                .build());
//        return ResponseData.success(new PgPocSkuNotExistListDTO(pgPocSkuNotExistEntities));
//    }
//
//}
