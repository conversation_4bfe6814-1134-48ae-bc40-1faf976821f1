package com.lenztech.bi.enterprise.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019-11-11 15:24
 * @since JDK 1.8
 */
@Data
public class PgPocRuleEntity {

    /**
     * 主键,和t_image_product表id相同
     */
    private Long id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 答卷id
     */
    private String rid;
    /**
     * 图片id
     */
    private Integer image;
    /**
     * 层数
     */
    private Integer layer;
    /**
     * 产品id
     */
    private Integer productId;
    /**
     * 每个sku的合格状态，Y是合格，N是不合格
     */
    private String status;
    /**
     * 坐标
     */
    private String coordinate;
    /**
     * 门店的规则合格的判断，1是合格，0是不合格
     */
    private Integer storeStatus;

}
