package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.lenztech.bi.enterprise.dto.gsk.*;
import com.lenztech.bi.enterprise.entity.*;
import com.lenztech.bi.enterprise.mapper.lenzbi.BiProjectsConfV1Mapper;
import com.lenztech.bi.enterprise.mapper.task.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lv
 */

@Slf4j
@Service
public class GskDdtAndDdrService {


    @Autowired
    private TTasklaunchMapper tTasklaunchMapper;

    @Autowired
    private TResponseMapper tResponseMapper;

    @Autowired
    private TQuestionTempMapper tQuestionTempMapper;

    @Autowired
    private TQuestionMapper tQuestionMapper;

    @Autowired
    private TAnswerMapper tAnswerMapper;

    @Autowired
    private BiProjectsConfV1Mapper biProjectsConfV1Mapper;


    /**
     * Posm页面1
     * 动态问卷接口
     *
     * @param gskPosmReq
     */
    public List<GskPosmDto> getPosmList(GskPosmReq gskPosmReq) {
        List<BiProjectsConfV1> biProjectsConfV1List = this.getPosmTaskId();
        List<String> posmList = biProjectsConfV1List.stream().map(BiProjectsConfV1::getTaskidArgs).collect(Collectors.toList());
        // 动态问卷返回的数据
        List<GskPosmDto> gskPosmDtoList = Lists.newArrayList();
        // t_response表 ownerId 是taskId
        LambdaQueryWrapper<TResponse> tResponseLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(gskPosmReq.getUpTime())) {
            LocalDate localDate = LocalDate.parse(gskPosmReq.getUpTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDateTime startTime = LocalDateTime.of(localDate, LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.MAX);
            tResponseLambdaQueryWrapper.between(TResponse::getUpTime, startTime, endTime);
        }

        tResponseLambdaQueryWrapper.in(TResponse::getTaskidOwner, posmList);
        tResponseLambdaQueryWrapper.eq(TResponse::getPhone, gskPosmReq.getPhone());
        List<TResponse> tResponseList = tResponseMapper.selectList(tResponseLambdaQueryWrapper);

        for (TResponse tResponse : tResponseList) {
            GskPosmDto gskPosmDto = new GskPosmDto();
            // t_response表的taskId 关联 t_taskLaunch表的taskId
            TTasklaunch tTasklaunch = tTasklaunchMapper.selectOne(new LambdaQueryWrapper<TTasklaunch>().eq(TTasklaunch::getTaskid, tResponse.getTaskid()));
            gskPosmDto.setStoreName(tTasklaunch.getReallyAddress());
            gskPosmDto.setStoreType(tTasklaunch.getStoreTypeNumber());
            gskPosmDto.setUploadTime(tResponse.getUpTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            gskPosmDto.setTaskId(tResponse.getTaskid());
            gskPosmDto.setResponseId(tResponse.getId());
            gskPosmDto.setTaskIdOwner(tResponse.getTaskidOwner());

            // 根据t_response的一个rid 和一个 taskId 的taskId 去查question_temp表
            LambdaQueryWrapper<TQuestionTemp> tQuestionTempLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tQuestionTempLambdaQueryWrapper.eq(TQuestionTemp::getTaskid, tResponse.getTaskid());
            tQuestionTempLambdaQueryWrapper.eq(TQuestionTemp::getResponseId, tResponse.getId());
            // 类型为1 单选题
            tQuestionTempLambdaQueryWrapper.eq(TQuestionTemp::getType, 1);
            List<TQuestionTemp> tQuestionTempList = tQuestionTempMapper.selectList(tQuestionTempLambdaQueryWrapper);
            List<String> qidList = tQuestionTempList.stream().map(TQuestionTemp::getId).distinct().collect(Collectors.toList());

            LambdaQueryWrapper<TAnswer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TAnswer::getResponseId, tResponse.getId());
            queryWrapper.in(TAnswer::getQuestionTempId, qidList);
            List<TAnswer> tAnswerList = tAnswerMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tAnswerList)) {
                log.info("当前rid ,{} 不进行展示", tResponse.getId());
                continue;
            }
            LambdaQueryWrapper<TAnswer> tAnswerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tAnswerLambdaQueryWrapper.eq(TAnswer::getResponseId, tResponse.getId());
            tAnswerLambdaQueryWrapper.in(TAnswer::getQuestionTempId, qidList);
            tAnswerLambdaQueryWrapper.eq(TAnswer::getAnswer, 1);
            int count = tAnswerMapper.selectCount(tAnswerLambdaQueryWrapper);
            gskPosmDto.setUpToStandard(new BigDecimal(count).multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(qidList.size()), 2, BigDecimal.ROUND_HALF_UP).toString() + "%");
            gskPosmDtoList.add(gskPosmDto);
        }
        return gskPosmDtoList;

    }

    /**
     * Posm 动态问卷 详情界面
     *
     * @param gskPosmDetailReq
     */
    public List<GskPosmDetailDto> getPosmDetail(GskPosmDetailReq gskPosmDetailReq) {
        // 定义返回值
        List<GskPosmDetailDto> gskPosmDetailDtoList = Lists.newArrayList();
        // 查出全部的answer值
        LambdaQueryWrapper<TAnswer> tAnswerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tAnswerLambdaQueryWrapper.eq(TAnswer::getResponseId, gskPosmDetailReq.getResponseId());
        List<TAnswer> tAnswerList = tAnswerMapper.selectList(tAnswerLambdaQueryWrapper);

        LambdaQueryWrapper<TQuestionTemp> tQuestionTempLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tQuestionTempLambdaQueryWrapper.eq(TQuestionTemp::getTaskid, gskPosmDetailReq.getTaskId());
        tQuestionTempLambdaQueryWrapper.eq(TQuestionTemp::getResponseId, gskPosmDetailReq.getResponseId());
        // 类型为1 单选题
        tQuestionTempLambdaQueryWrapper.eq(TQuestionTemp::getType, 1);
        List<TQuestionTemp> tQuestionTempList = tQuestionTempMapper.selectList(tQuestionTempLambdaQueryWrapper);
        // 按照大题名进行分组

        Map<String, List<TQuestionTemp>> tQuestionTempMap = tQuestionTempList.stream().collect(Collectors.groupingBy(TQuestionTemp::getLoopsku));

        for (Map.Entry<String, List<TQuestionTemp>> loopSku : tQuestionTempMap.entrySet()) {
            GskPosmDetailDto gskPosmDetailDto = new GskPosmDetailDto();
            gskPosmDetailDto.setLoopSku(loopSku.getKey());
            List<GskPosmAnswerDetailDto> gskPosmAnswerDetailDtoList = Lists.newArrayList();
            for (TQuestionTemp tQuestionTemp : loopSku.getValue()) {
                GskPosmAnswerDetailDto gskPosmAnswerDetailDto = new GskPosmAnswerDetailDto();
                gskPosmAnswerDetailDto.setQuestionDesc(tQuestionTemp.getTitle());
                TAnswer tAnswer = tAnswerList.stream().filter(t -> t.getQuestionTempId().equals(tQuestionTemp.getId())).findFirst().orElse(null);
                gskPosmAnswerDetailDto.setAnswerDesc(String.valueOf(Integer.parseInt(tAnswer.getAnswer()) == 1 ? 1 : 0));
                if (tQuestionTemp.getTitle().contains("陈列的POSM是否在好位置")) {
                    gskPosmAnswerDetailDto.setAnswerDesc(Integer.parseInt(tAnswer.getAnswer()) == 1 ? "好位置" : "非好位置");
                }

                gskPosmAnswerDetailDtoList.add(gskPosmAnswerDetailDto);
            }
            gskPosmDetailDto.setGskPosmAnswerDetailDtoList(gskPosmAnswerDetailDtoList);
            gskPosmDetailDtoList.add(gskPosmDetailDto);
        }

        return gskPosmDetailDtoList;
    }


    /**
     * 产品可见页面1
     *
     * @param gskPosmReq
     */
    public List<GskPosmDto> getProductList(GskPosmReq gskPosmReq) {
        List<BiProjectsConfV1> biProjectsConfV1List = this.getProductTaskId();
        List<String> productList = biProjectsConfV1List.stream().map(BiProjectsConfV1::getTaskidArgs).collect(Collectors.toList());
        // 动态问卷返回的数据
        List<GskPosmDto> gskPosmDtoList = Lists.newArrayList();

        // t_response表 ownerId 是taskId
        LambdaQueryWrapper<TResponse> tResponseLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(gskPosmReq.getUpTime())) {
            LocalDate localDate = LocalDate.parse(gskPosmReq.getUpTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDateTime startTime = LocalDateTime.of(localDate, LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.MAX);
            tResponseLambdaQueryWrapper.between(TResponse::getUpTime, startTime, endTime);
        }
        tResponseLambdaQueryWrapper.eq(TResponse::getPhone, gskPosmReq.getPhone());
        tResponseLambdaQueryWrapper.in(TResponse::getTaskidOwner, productList);
        List<TResponse> tResponseList = tResponseMapper.selectList(tResponseLambdaQueryWrapper);

        for (TResponse tResponse : tResponseList) {
            GskPosmDto gskPosmDto = new GskPosmDto();
            // t_response表的taskId 关联 t_taskLaunch表的taskId
            TTasklaunch tTasklaunch = tTasklaunchMapper.selectOne(new LambdaQueryWrapper<TTasklaunch>().eq(TTasklaunch::getTaskid, tResponse.getTaskid()));
            gskPosmDto.setStoreName(tTasklaunch.getReallyAddress());
            gskPosmDto.setStoreType(tTasklaunch.getStoreTypeNumber());
            gskPosmDto.setUploadTime(tResponse.getUpTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            gskPosmDto.setTaskId(tResponse.getTaskid());
            gskPosmDto.setTaskIdOwner(tResponse.getTaskidOwner());
            gskPosmDto.setResponseId(tResponse.getId());

            // 根据t_response的一个rid 和一个 taskId 的taskId 去查question_temp表
            LambdaQueryWrapper<TQuestion> tQuestionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tQuestionLambdaQueryWrapper.eq(TQuestion::getTaskid, tResponse.getTaskidOwner());
            tQuestionLambdaQueryWrapper.eq(TQuestion::getType, 1);
            List<TQuestion> tQuestionTempList = tQuestionMapper.selectList(tQuestionLambdaQueryWrapper);

            List<String> qidList = tQuestionTempList.stream().map(TQuestion::getId).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<TAnswer> tAnswerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tAnswerLambdaQueryWrapper.eq(TAnswer::getResponseId, tResponse.getId());
            tAnswerLambdaQueryWrapper.in(TAnswer::getQid, qidList);
            List<TAnswer> tAnswerList = tAnswerMapper.selectList(tAnswerLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(tAnswerList)) {
                log.info("当前rid ,{} 不进行展示", tResponse.getId());
                continue;
            }
            int c7 = 0;
            int c11 = 0;
            int c12 = 0;
            int c16 = 0;
            // 遍历问题表
            for (TQuestion tQuestion : tQuestionTempList) {
                TAnswer tAnswer = tAnswerList.stream().filter(t -> t.getQid().equals(tQuestion.getId())).findFirst().orElse(null);
                if (tQuestion.getTitle().contains("该医院有无分销舒适达专业修复100g")
                        || tQuestion.getTitle().contains("该医院有无分销舒适达专业修复100g口腔专业渠道限定装")
                        || tQuestion.getTitle().contains("该医院有无分销舒适达专业修复美白100g")) {
                    if (Integer.parseInt(tAnswer.getAnswer()) == 1) {
                        c7 += 1;
                    }
                } else if (tQuestion.getTitle().contains("该医院有无分销保丽净假牙稳固剂")) {
                    if (Integer.parseInt(tAnswer.getAnswer()) == 1) {
                        c11 += 1;
                    }
                } else if (tQuestion.getTitle().contains("该医院有无分销保丽净全/半口假牙清洁片24片装")
                        || tQuestion.getTitle().contains("该医院有无分销保丽净全/半口假牙清洁片60片装")
                        || tQuestion.getTitle().contains("该医院有无分销保丽净局部假牙清洁片24片装")
                        || tQuestion.getTitle().contains("该医院有无分销保丽净全/半口假牙清洁片60片+假牙盒促销装")) {
                    if (Integer.parseInt(tAnswer.getAnswer()) == 1) {
                        c12 += 1;
                    }
                } else if (tQuestion.getTitle().contains("该医院有无分销保丽净牙套清洁片24片")
                        || tQuestion.getTitle().contains("该医院有无分销保丽净牙套清洁片24片*2+2件套装")) {
                    if (Integer.parseInt(tAnswer.getAnswer()) == 1) {
                        c16 += 1;
                    }
                }
            }
            if (c7 != 0 && c11 != 0 && c12 != 0 && c16 != 0) {
                gskPosmDto.setUpToStandard(String.valueOf(1));
            } else {
                gskPosmDto.setUpToStandard(String.valueOf(0));
            }
            gskPosmDtoList.add(gskPosmDto);
        }
        return gskPosmDtoList;
    }


    /**
     * 产品可见页面2
     *
     * @param gskPosmDetailReq
     */
    public List<GskPosmDetailDto> getProductDetail(GskPosmDetailReq gskPosmDetailReq) {
        // 定义返回值
        List<GskPosmDetailDto> gskPosmDetailDtoList = Lists.newArrayList();
        // 查出全部的answer值
        LambdaQueryWrapper<TAnswer> tAnswerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tAnswerLambdaQueryWrapper.eq(TAnswer::getResponseId, gskPosmDetailReq.getResponseId());
        List<TAnswer> tAnswerList = tAnswerMapper.selectList(tAnswerLambdaQueryWrapper);

        LambdaQueryWrapper<TQuestion> tQuestionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tQuestionLambdaQueryWrapper.eq(TQuestion::getTaskid, gskPosmDetailReq.getTaskIdOwner());
        // 类型为1 单选题
        tQuestionLambdaQueryWrapper.eq(TQuestion::getType, 1);
        tQuestionLambdaQueryWrapper.orderByAsc(TQuestion::getQindex);
        List<TQuestion> tQuestionList = tQuestionMapper.selectList(tQuestionLambdaQueryWrapper);
        Map<String, List<TQuestion>> tQuestionMap = tQuestionList.stream().collect(Collectors.groupingBy(TQuestion::getTaskid));

        for (Map.Entry<String, List<TQuestion>> index : tQuestionMap.entrySet()) {
            GskPosmDetailDto gskPosmDetailDto = new GskPosmDetailDto();
            gskPosmDetailDto.setLoopSku("产品可见达标");
            List<GskPosmAnswerDetailDto> gskPosmAnswerDetailDtoList = Lists.newArrayList();
            for (TQuestion tQuestion : index.getValue()) {
                GskPosmAnswerDetailDto gskPosmAnswerDetailDto = new GskPosmAnswerDetailDto();
                gskPosmAnswerDetailDto.setQuestionDesc(tQuestion.getTitle());
                TAnswer tAnswer = tAnswerList.stream().filter(t -> t.getQid().equals(tQuestion.getId())).findFirst().orElse(null);
                gskPosmAnswerDetailDto.setAnswerDesc(String.valueOf(Integer.parseInt(tAnswer.getAnswer()) == 1 ? 1 : 0));
                gskPosmAnswerDetailDtoList.add(gskPosmAnswerDetailDto);
            }
            gskPosmDetailDto.setGskPosmAnswerDetailDtoList(gskPosmAnswerDetailDtoList);
            gskPosmDetailDtoList.add(gskPosmDetailDto);
        }

        return gskPosmDetailDtoList;
    }


    /**
     * 时间下拉列表
     * Posm 动态下拉列表
     *
     * @param gskPosmReq
     * @return
     */
    public List<String> getUpTimePosmList(GskPosmReq gskPosmReq) {
        List<BiProjectsConfV1> biProjectsConfV1List = this.getPosmTaskId();
        List<String> idList = biProjectsConfV1List.stream().map(BiProjectsConfV1::getTaskidArgs).collect(Collectors.toList());
        LambdaQueryWrapper<TResponse> tResponseLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tResponseLambdaQueryWrapper.in(TResponse::getTaskidOwner, idList);
        tResponseLambdaQueryWrapper.eq(TResponse::getPhone, gskPosmReq.getPhone());
        tResponseLambdaQueryWrapper.orderByDesc(TResponse::getUpTime);
        List<TResponse> tResponseList = tResponseMapper.selectList(tResponseLambdaQueryWrapper);
        List<String> dateList = Lists.newArrayList();
        List<LocalDateTime> timeList = tResponseList.stream().distinct().map(TResponse::getUpTime).collect(Collectors.toList());
        for (LocalDateTime localDateTime : timeList) {
            String date = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dateList.add(date);
        }
        dateList = dateList.stream().distinct().collect(Collectors.toList());
        return dateList;
    }

    /**
     * 时间下拉列表
     * Product 动态下拉列表
     *
     * @param gskPosmReq
     * @return
     */
    public List<String> getUpTimeProductList(GskPosmReq gskPosmReq) {
        List<BiProjectsConfV1> biProjectsConfV1List = this.getProductTaskId();
        List<String> idList = biProjectsConfV1List.stream().map(BiProjectsConfV1::getTaskidArgs).collect(Collectors.toList());
        LambdaQueryWrapper<TResponse> tResponseLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tResponseLambdaQueryWrapper.in(TResponse::getTaskidOwner, idList);
        tResponseLambdaQueryWrapper.eq(TResponse::getPhone, gskPosmReq.getPhone());
        tResponseLambdaQueryWrapper.orderByDesc(TResponse::getUpTime);
        List<TResponse> tResponseList = tResponseMapper.selectList(tResponseLambdaQueryWrapper);
        List<String> dateList = Lists.newArrayList();
        List<LocalDateTime> timeList = tResponseList.stream().distinct().map(TResponse::getUpTime).collect(Collectors.toList());
        for (LocalDateTime localDateTime : timeList) {
            String date = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dateList.add(date);
        }
        dateList = dateList.stream().distinct().collect(Collectors.toList());
        return dateList;
    }

    /**
     * posm
     */
    public List<BiProjectsConfV1> getPosmTaskId() {
        LambdaQueryWrapper<BiProjectsConfV1> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiProjectsConfV1::getOthersArgs, "posm");
        List<BiProjectsConfV1> biProjectsConfV1List = biProjectsConfV1Mapper.selectList(queryWrapper);
        return biProjectsConfV1List;
    }

    /**
     * product
     */
    public List<BiProjectsConfV1> getProductTaskId() {
        LambdaQueryWrapper<BiProjectsConfV1> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiProjectsConfV1::getOthersArgs, "product");
        List<BiProjectsConfV1> biProjectsConfV1List = biProjectsConfV1Mapper.selectList(queryWrapper);
        return biProjectsConfV1List;
    }
}
