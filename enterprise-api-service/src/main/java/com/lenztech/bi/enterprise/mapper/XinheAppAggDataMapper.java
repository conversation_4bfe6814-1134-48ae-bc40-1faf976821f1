/*
 * Copyright (c) 2022. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.dto.xinhe.DownloadExcelDTO;
import com.lenztech.bi.enterprise.entity.XinheAppAggData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 欣和图片识别结果
 */
@DS("lenzbi")
public interface XinheAppAggDataMapper extends BaseMapper<XinheAppAggData> {
	
	List<XinheAppAggData> getListByImportInfo(@Param("downloadExcelDTO") DownloadExcelDTO downloadExcelDTO);
	
}
