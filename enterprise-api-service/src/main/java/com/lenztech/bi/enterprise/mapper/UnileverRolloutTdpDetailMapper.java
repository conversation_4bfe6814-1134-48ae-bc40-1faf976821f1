package com.lenztech.bi.enterprise.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.UnileverRolloutTdpDetail;
import feign.Param;

import java.util.List;

@DS("lenzbi")
public interface UnileverRolloutTdpDetailMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(UnileverRolloutTdpDetail record);

    int insertSelective(UnileverRolloutTdpDetail record);

    UnileverRolloutTdpDetail selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(UnileverRolloutTdpDetail record);

    List<UnileverRolloutTdpDetail> getByResponseId(@Param("responseId") String responseId);
}