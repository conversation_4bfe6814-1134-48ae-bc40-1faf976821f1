package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lenztech.bi.enterprise.entity.TImageStoreProductExist;
import com.lenztech.bi.enterprise.mapper.task.TImageStoreProductExistMapper;
import com.lenztech.bi.enterprise.service.ITImageStoreProductExistService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 门店中某个商品是否存在 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Service
public class TImageStoreProductExistServiceImpl extends ServiceImpl<TImageStoreProductExistMapper, TImageStoreProductExist> implements ITImageStoreProductExistService {

}
