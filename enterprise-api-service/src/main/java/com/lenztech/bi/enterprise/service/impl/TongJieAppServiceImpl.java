package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.comon.RepeatStatusEnum;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiImageResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiPatchResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.CoordinateDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.PriceDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.StitchImageDTO;
import com.lenztech.bi.enterprise.entity.TongJieAppImageDetail;
import com.lenztech.bi.enterprise.entity.TongJieAppProductDetail;
import com.lenztech.bi.enterprise.entity.TongJieAppStitchImageDetail;
import com.lenztech.bi.enterprise.entity.TongjieAppPrice;
import com.lenztech.bi.enterprise.mapper.TongJieAppImageMapper;
import com.lenztech.bi.enterprise.mapper.TongJieAppProductMapper;
import com.lenztech.bi.enterprise.mapper.TongJieAppStitchImageDetailMapper;
import com.lenztech.bi.enterprise.mapper.TongjieAppPriceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 联合利华App根据答卷id查询BI识别指标结果
 *
 * <AUTHOR>
 * @date 2022-01-17 17:44:19
 */
@Service
@Slf4j
public class TongJieAppServiceImpl {


    @Autowired
    private TongJieAppImageMapper tongJieAppImageMapper;

    @Autowired
    private TongJieAppProductMapper tongJieAppProductMapper;

    @Autowired
    private TongJieAppStitchImageDetailMapper tongJieAppStitchImageDetailMapper;
    @Autowired
    private TongjieAppPriceMapper tongjieAppPriceMapper;

    /**
     * 根据答卷id查询BI结果
     *
     * @param responseId 答卷Id
     * @return ApiResultDTO
     */
    public ApiResultDTO getBiTargetList(String responseId) {



        ApiResultDTO apiResultDTO = new ApiResultDTO();
        apiResultDTO.setResponseId(responseId);


        LambdaQueryWrapper<TongJieAppStitchImageDetail>  stitchImageDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        stitchImageDetailLambdaQueryWrapper.eq(TongJieAppStitchImageDetail::getResponseId,responseId);
        List<TongJieAppStitchImageDetail> tongJieAppStitchImageDetails = tongJieAppStitchImageDetailMapper.selectList(stitchImageDetailLambdaQueryWrapper);

        List<StitchImageDTO> stitchImages  = new ArrayList<>();

        for (TongJieAppStitchImageDetail tongJieAppStitchImageDetail : tongJieAppStitchImageDetails) {
            StitchImageDTO stitchImageDTO = new StitchImageDTO();
            stitchImageDTO.setResponseId(tongJieAppStitchImageDetail.getResponseId());
            stitchImageDTO.setGroupNo(tongJieAppStitchImageDetail.getGroupNo());
            stitchImageDTO.setJointUrl(tongJieAppStitchImageDetail.getJointUrl());
            stitchImageDTO.setQuestionId(tongJieAppStitchImageDetail.getQuestionId());
            stitchImages.add(stitchImageDTO);
        }
        apiResultDTO.setStitchImages(stitchImages);

        // 查询所有图片信息
        LambdaQueryWrapper<TongJieAppImageDetail> imageWrapper = new LambdaQueryWrapper<>();
        imageWrapper.eq(TongJieAppImageDetail::getResponseId, responseId);
        List<TongJieAppImageDetail> imageDetailList = tongJieAppImageMapper.selectList(imageWrapper);

        if (CollectionUtils.isEmpty(imageDetailList)) {
            log.info("【查询BI识别结果集为空!】, responseId:{}", responseId);
            return apiResultDTO;
        }

        // 查询识别出所有SKU信息
        LambdaQueryWrapper<TongJieAppProductDetail> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(TongJieAppProductDetail::getResponseId, responseId);
        List<TongJieAppProductDetail> productDetailList = tongJieAppProductMapper.selectList(productWrapper);

        if (CollectionUtils.isEmpty(productDetailList)) {
            log.info("【查询BI识别明细表结果集为空!】, responseId:{}", responseId);
            return apiResultDTO;
        }

        // 根据ImageId分组
        Map<String, List<TongJieAppProductDetail>> productMap = productDetailList.stream().collect(Collectors.groupingBy(TongJieAppProductDetail::getImageId));

        // 构建识别明细
        List<ApiImageResultDTO> apiImageResultList = Lists.newArrayList();
        for (TongJieAppImageDetail imageDetail : imageDetailList) {
            ApiImageResultDTO imageResult = new ApiImageResultDTO();
            imageResult.setImageId(imageDetail.getImageId());
            imageResult.setRemake(imageDetail.getRemake());
            imageResult.setRemakeScore(imageDetail.getRemakeScore());
            imageResult.setRepeat(Objects.isNull(imageDetail.getRepeatGroup()) ? RepeatStatusEnum.NOT_REPEAT.getCode() : RepeatStatusEnum.REPEAT.getCode());
            imageResult.setRepeatGroup(imageDetail.getRepeatGroup());


            ArrayList<ApiPatchResultDTO> patchListList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(productMap.get(imageDetail.getImageId()))) {
                for (TongJieAppProductDetail productDetail : productMap.get(imageDetail.getImageId())) {
                    ApiPatchResultDTO sku = new ApiPatchResultDTO();
                    CoordinateDTO coordinateDTO = new CoordinateDTO();
                    sku.setId(productDetail.getProductId());
                    sku.setSkuCode(productDetail.getCustomerCode());
                    sku.setColumn(productDetail.getColumn());
                    sku.setLayer(productDetail.getLayer());
                    sku.setStitchDuplicateStatus(productDetail.getInarge());
                    coordinateDTO.setxMin(productDetail.getXmin());
                    coordinateDTO.setyMin(productDetail.getYmin());
                    coordinateDTO.setxMax(productDetail.getXmax());
                    coordinateDTO.setyMax(productDetail.getYmax());
                    sku.setCoordinate(coordinateDTO);

                    CoordinateDTO stitchCoordinate = new CoordinateDTO();
                    stitchCoordinate.setxMin(productDetail.getStitchXmin());
                    stitchCoordinate.setxMax(productDetail.getStitchXmax());
                    stitchCoordinate.setyMin(productDetail.getStitchYmin());
                    stitchCoordinate.setyMax(productDetail.getStitchYmax());
                    sku.setStitchCoordinate(stitchCoordinate);
                    sku.setScene(productDetail.getScene());
                    patchListList.add(sku);
                }
            }

            imageResult.setPatches(patchListList);
            apiImageResultList.add(imageResult);
        }

        LambdaQueryWrapper<TongjieAppPrice> priceWrapper = new LambdaQueryWrapper<>();
        priceWrapper.eq(TongjieAppPrice::getResponseId, responseId);
        List<TongjieAppPrice> priceList = tongjieAppPriceMapper.selectList(priceWrapper);
        List<PriceDTO>  priceDTOS = new ArrayList<>();
        for (TongjieAppPrice tongjieAppPrice : priceList) {
            PriceDTO priceDTO = new PriceDTO();
            priceDTO.setPrice(tongjieAppPrice.getPrice());
            priceDTO.setProductId(tongjieAppPrice.getProductId());
            priceDTO.setResponseId(tongjieAppPrice.getResponseId());
            priceDTOS.add(priceDTO);
        }

        apiResultDTO.setPrices(priceDTOS);
        apiResultDTO.setAiResult(apiImageResultList);


        return apiResultDTO;
    }

}