package com.lenztech.bi.enterprise.service.enterprise;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lenztech.bi.enterprise.dto.optimus.DynamicSubAnswerDTO;
import com.lenztech.bi.enterprise.entity.lenzbi.PgOptimusBackupsBrandLevelDisplayCount;
import com.lenztech.bi.enterprise.entity.lenzbi.PgOptimusBackupsBrandLevelDisplaySize;
import com.lenztech.bi.enterprise.entity.lenzbi.PgOptimusBackupsCategoryLevelDisplayCount;
import com.lenztech.bi.enterprise.entity.lenzbi.PgOptimusBackupsFlatAnswer;
import com.lenztech.bi.enterprise.entity.lenzbi.PgOptimusBackupsSetting;
import com.lenztech.bi.enterprise.entity.lenzbi.PgOptimusBackupsSettingArea;
import com.lenztech.bi.enterprise.entity.ppz.AnswerDO;
import com.lenztech.bi.enterprise.entity.ppz.QuestionDynamicCircularRelDO;
import com.lenztech.bi.enterprise.entity.ppz.QuestionDynamicCircularTitleDO;
import com.lenztech.bi.enterprise.entity.ppz.QuestionFillBlankDO;
import com.lenztech.bi.enterprise.entity.ppz.TaskAddressDO;
import com.lenztech.bi.enterprise.mapper.lenzbi.PgOptimusBackupsFlatAnswerMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.PgOptimusBackupsSettingAreaMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.PgOptimusBackupsSettingMapper;
import com.lenztech.bi.enterprise.mapper.ppz.AnswerMapper;
import com.lenztech.bi.enterprise.mapper.ppz.QuestionDynamicCircularRelMapper;
import com.lenztech.bi.enterprise.mapper.ppz.QuestionDynamicCircularTitleMapper;
import com.lenztech.bi.enterprise.mapper.ppz.QuestionFillBlankMapper;
import com.lenztech.bi.enterprise.mapper.ppz.TaskAddressMapper;
import com.lenztech.bi.enterprise.service.lenzbi.OptimusFlatAnswerService;
import com.lenztech.bi.enterprise.service.lenzbi.PgOptimusBackupsBrandLevelDisplayCountService;
import com.lenztech.bi.enterprise.service.lenzbi.PgOptimusBackupsBrandLevelDisplaySizeService;
import com.lenztech.bi.enterprise.service.lenzbi.PgOptimusBackupsCategoryLevelDisplayCountService;
import com.lenztech.bi.enterprise.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Date;
import java.util.HashSet;

@Service
@Slf4j
public class OptimusServiceImpl {

    @Resource
    private PgOptimusBackupsSettingMapper settingMapper;

    @Resource
    private AnswerMapper answerMapper;

    @Resource
    private QuestionDynamicCircularTitleMapper circularTitleMapper;

    @Resource
    private QuestionDynamicCircularRelMapper circularRelMapper;

    @Resource
    private QuestionFillBlankMapper questionFillBlankMapper;

    @Resource
    private PgOptimusBackupsFlatAnswerMapper flatAnswerMapper;

    @Resource
    private TaskAddressMapper businessTaskAddressMapper;

    @Resource
    private OptimusFlatAnswerService optimusFlatAnswerService;

    @Resource
    private PgOptimusBackupsSettingAreaMapper settingAreaMapper;

    @Resource
    private PgOptimusBackupsBrandLevelDisplayCountService pgOptimusBackupsBrandLevelDisplayCountService;

    @Resource
    private PgOptimusBackupsCategoryLevelDisplayCountService categoryLevelDisplayCountService;

    @Resource
    private PgOptimusBackupsBrandLevelDisplaySizeService pgOptimusBackupsBrandLevelDisplaySizeService;

    public void calc(List<Long> taskAddressIdList) {
        List<TaskAddressDO> execList;

        // 1. 查询执行记录
        if (CollectionUtils.isEmpty(taskAddressIdList)) {
            // 查询当天所有执行记录
            LocalDate today = LocalDate.now();
            LocalDateTime todayStart = today.atStartOfDay();
            LocalDateTime tomorrowStart = today.plusDays(1).atStartOfDay();
            execList = businessTaskAddressMapper.selectList(
                    new LambdaQueryWrapper<TaskAddressDO>()
                            .ge(TaskAddressDO::getCreateTime, todayStart)
                            .lt(TaskAddressDO::getCreateTime, tomorrowStart)
            );
        } else {
            // 查询指定执行记录
            execList = businessTaskAddressMapper.selectList(
                    new LambdaQueryWrapper<TaskAddressDO>()
                            .in(TaskAddressDO::getTaskAddressId, taskAddressIdList)
            );
        }

        // 2. 得到原始明细数据

        processAndSaveAllToDb(execList);

        // 3. 循环计算KPI
        Set<String> processedGroupIds = new HashSet<>();
        for (TaskAddressDO taskAddressDO : execList) {
            Long taskAddressId = taskAddressDO.getTaskAddressId();
            // 先删除该taskAddressId相关的所有结果表数据，支持重新计算

            // 删除brand count表
            pgOptimusBackupsBrandLevelDisplayCountService.remove(
                new LambdaQueryWrapper<PgOptimusBackupsBrandLevelDisplayCount>()
                    .eq(PgOptimusBackupsBrandLevelDisplayCount::getTaskAddressId, taskAddressId));
            // 删除brand size表
            pgOptimusBackupsBrandLevelDisplaySizeService.remove(
                new LambdaQueryWrapper<PgOptimusBackupsBrandLevelDisplaySize>()
                    .eq(PgOptimusBackupsBrandLevelDisplaySize::getTaskAddressId, taskAddressId));
            // 删除category count表
            categoryLevelDisplayCountService.remove(
                new LambdaQueryWrapper<PgOptimusBackupsCategoryLevelDisplayCount>()
                    .eq(PgOptimusBackupsCategoryLevelDisplayCount::getTaskAddressId, taskAddressId));

            // 按品牌处理陈列数量
            String groupId = calcAndSaveBrandLevelDisplayCount(taskAddressId);
            processedGroupIds.add(groupId);

            // 按品类处理陈列数量
            calcAndSaveCategoryLevelDisplayCount(taskAddressId);
        }

        // 按组处理陈列面积
        for (String responseGroupId : processedGroupIds) {
            calcAndSaveBrandLevelDisplaySize(responseGroupId);
        }
    }

    /**
     * 主处理方法：
     * 1. 查询当天所有 business_task_address 执行记录
     * 2. 对每条执行，查找唯一配置（pg_optimus_backups_setting），并查出所有普通题答案
     * 3. 对于每条执行，分别查找 displayTypeQid/brandQid 的动态循环题结构和答案（只查本配置相关）
     * 4. brandQid 的每个循环项-子题-答案平铺为一行，displayTypeQid 的所有答案冗余为json
     * 5. 所有答案查找都加 taskId 和 taskAddressId 条件，保证数据唯一性和准确性
     * 6. 最终批量写入 pg_optimus_backups_flat_answer
     */
    private void processAndSaveAllToDb(List<TaskAddressDO> execList) {
        // 3. 一次性查出所有配置，放入Map方便后续查找
        List<PgOptimusBackupsSetting> allSettings = settingMapper.selectList(null);
        Map<String, PgOptimusBackupsSetting> settingMap = allSettings.stream()
                .filter(s -> s.getTaskId() != null)
                .collect(Collectors.toMap(PgOptimusBackupsSetting::getTaskId, s -> s, (a, b) -> a));

        List<PgOptimusBackupsSettingArea> areaConfigList = settingAreaMapper.selectList(null);
        Map<String, java.math.BigDecimal> areaMap = areaConfigList.stream()
                .collect(Collectors.toMap(PgOptimusBackupsSettingArea::getDisplayType,
                        PgOptimusBackupsSettingArea::getStandardArea));

        for (TaskAddressDO exec : execList) {
            Long taskId = exec.getTaskId();
            Long taskAddressId = exec.getTaskAddressId();

            // 删除flat表
            flatAnswerMapper.delete(new LambdaQueryWrapper<PgOptimusBackupsFlatAnswer>()
                    .eq(PgOptimusBackupsFlatAnswer::getTaskAddressId, taskAddressId));

            if (taskId == null) {
                continue;
            }
            PgOptimusBackupsSetting setting = settingMap.get(String.valueOf(taskId));
            if (setting == null) {
                continue;
            }
            // 收集所有普通题qid
            List<String> normalQids = new ArrayList<>();
            normalQids.add(setting.getBannerQid());
            normalQids.add(setting.getStoreCodeQid());
            normalQids.add(setting.getStoreTypeQid());
            normalQids.add(setting.getSubmitTimeQid());
            normalQids.add(setting.getResponseGroupIdQid());
            normalQids.add(setting.getResponseIdQid());
            normalQids.add(setting.getDirectionCountQid());
            // 查询答案时加 in 条件
            List<AnswerDO> answerList = answerMapper.selectList(
                new LambdaQueryWrapper<AnswerDO>()
                    .eq(AnswerDO::getTaskId, taskId)
                    .eq(AnswerDO::getTaskAddressId, taskAddressId)
                    .in(AnswerDO::getQuestionId, normalQids)
            );
            Map<String, AnswerDO> answerMap = answerList.stream().collect(Collectors.toMap(a -> String.valueOf(a.getQuestionId()), a -> a, (a, b) -> a));
            String banner = getAnswerFromMap(answerMap, setting.getBannerQid());
            String storeCode = getAnswerFromMap(answerMap, setting.getStoreCodeQid());
            String storeType = getAnswerFromMap(answerMap, setting.getStoreTypeQid());
            String responseGroupId = getAnswerFromMap(answerMap, setting.getResponseGroupIdQid());
            String responseId = getAnswerFromMap(answerMap, setting.getResponseIdQid());
            String visitTime = getAnswerFromMap(answerMap, setting.getSubmitTimeQid());
            String directionCount = getAnswerFromMap(answerMap, setting.getDirectionCountQid());
            String categoryName = setting.getCategoryName();
            List<DynamicSubAnswerDTO> displayTypeList = getDynamicSubAnswersWithTaskId(setting.getDisplayTypeQid(), taskId, taskAddressId, true, 0);
            String displayTypeJson = displayTypeList != null ? JSON.toJSONString(displayTypeList) : null;
            // 新增：批量查出所有面积配置，计算总面积
            java.math.BigDecimal totalArea = java.math.BigDecimal.ZERO;
            if (displayTypeList != null && !displayTypeList.isEmpty()) {
                // 查询所有面积配置，组装为map
                for (DynamicSubAnswerDTO dto : displayTypeList) {
                    String displayType = dto.getCircularTitle();
                    String answerStr = dto.getAnswer();
                    int count = 0;
                    try {
                        count = Integer.parseInt(answerStr);
                    } catch (Exception e) {
                        continue;
                    }
                    java.math.BigDecimal standardArea = areaMap.get(displayType);
                    if (standardArea != null) {
                        totalArea = totalArea.add(standardArea.multiply(java.math.BigDecimal.valueOf(count)));
                    }
                }
            }
            List<DynamicSubAnswerDTO> brandList = getDynamicSubAnswersWithTaskId(setting.getBrandQid(), taskId, taskAddressId, false, Integer.parseInt(directionCount));
            List<PgOptimusBackupsFlatAnswer> flatList = new ArrayList<>();
            if (brandList != null) {
                for (DynamicSubAnswerDTO sub : brandList) {
                    PgOptimusBackupsFlatAnswer flat = new PgOptimusBackupsFlatAnswer();
                    flat.setTaskAddressId(taskAddressId);
                    flat.setBanner(banner);
                    flat.setStoreCode(storeCode);
                    flat.setStoreType(storeType);
                    flat.setResponseGroupId(responseGroupId);
                    flat.setResponseId(responseId);
                    if (StringUtils.isNotBlank(visitTime)) {
                        flat.setVisitTime(DateUtil.convert2Date(visitTime, DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
                    }
                    flat.setDirectionCount(directionCount);
                    flat.setCategoryName(categoryName);
                    flat.setDisplayType(displayTypeJson);
                    flat.setDisplayArea(totalArea);
                    flat.setBrand(sub.getSubQuestionTitle());
                    flat.setCircularTitle(sub.getCircularTitle());
                    flat.setAnswer(sub.getAnswer());
                    flat.setCreateTime(new java.util.Date());
                    flat.setTaskAddressId(exec.getTaskAddressId());
                    flatList.add(flat);
                }
            }
            // 每个执行单独批量插入
            if (!flatList.isEmpty()) {
                optimusFlatAnswerService.saveBatch(flatList);
            }
        }
    }

    /**
     * 从一次性查出的答案Map中获取指定题目的答案
     */
    private String getAnswerFromMap(Map<String, AnswerDO> answerMap, String qidStr) {
        if (qidStr == null) return null;
        AnswerDO answer = answerMap.get(qidStr);
        return answer != null ? answer.getResponseText() : null;
    }

    /**
     * 查找动态循环题（如brandQid、displayTypeQid）下所有循环项-子题-答案
     * 只查本配置相关结构和答案，所有答案都加taskId和taskAddressId过滤
     *
     * @param dynamicQidStr 配置表中的循环题id
     * @param taskId        当前执行的taskId
     * @param taskAddressId 当前执行的taskAddressId
     * @return List<DynamicSubAnswerDTO>，每个元素为一个循环项下的子题及答案
     */
    private List<DynamicSubAnswerDTO> getDynamicSubAnswersWithTaskId(String dynamicQidStr, Long taskId, Long taskAddressId, boolean isDisplayType, int maxCount) {
        if (dynamicQidStr == null || taskId == null || taskAddressId == null) {
            return null;
        }
        try {
            // 只查本次循环题相关title/rel
            List<QuestionDynamicCircularTitleDO> titles = circularTitleMapper.selectList(
                new LambdaQueryWrapper<QuestionDynamicCircularTitleDO>()
                    .eq(QuestionDynamicCircularTitleDO::getQuestionId, dynamicQidStr)
            );
            List<QuestionDynamicCircularRelDO> rels = circularRelMapper.selectList(
                new LambdaQueryWrapper<QuestionDynamicCircularRelDO>()
                    .eq(QuestionDynamicCircularRelDO::getQuestionId, dynamicQidStr)
            );
            Set<String> subQids = rels.stream().map(rel -> String.valueOf(rel.getRelQuestionId())).collect(Collectors.toSet());
            Map<Long, QuestionFillBlankDO> subQuestionMap = new HashMap<>();
            if (!subQids.isEmpty()) {
                LambdaQueryWrapper<QuestionFillBlankDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(QuestionFillBlankDO::getQuestionId, subQids);
                List<QuestionFillBlankDO> subQuestions = questionFillBlankMapper.selectList(queryWrapper);
                for (QuestionFillBlankDO q : subQuestions) {
                    subQuestionMap.put(q.getQuestionId(), q);
                }
            }
            if (titles == null) {
                return null;
            }

            // 一次性查出所有相关答案
            Set<Long> titleIds = titles.stream().map(QuestionDynamicCircularTitleDO::getId).collect(Collectors.toSet());
            Set<Long> relQuestionIds = rels.stream().map(QuestionDynamicCircularRelDO::getRelQuestionId).collect(Collectors.toSet());
            List<AnswerDO> answerList = answerMapper.selectList(
                    new LambdaQueryWrapper<AnswerDO>()
                            .eq(AnswerDO::getQuestionId, dynamicQidStr)
                            .eq(AnswerDO::getTaskAddressId, taskAddressId)
                            .in(AnswerDO::getDynamicCircularTitleId, titleIds)
                            .in(AnswerDO::getSubQuestionId, relQuestionIds)
            );
            // key: questionId+titleId_subquestionId
            Map<String, AnswerDO> answerMap = answerList.stream()
                    .collect(Collectors.toMap(
                            a -> a.getQuestionId() + "_" + a.getDynamicCircularTitleId() + "_" + a.getSubQuestionId(),
                            a -> a,
                            (a, b) -> a
                    ));


            List<DynamicSubAnswerDTO> list = new ArrayList<>();
            // 陈列类型循环题
            if (isDisplayType) {
                for (QuestionDynamicCircularTitleDO title : titles) {
                    for (QuestionDynamicCircularRelDO rel : rels) {
                        QuestionFillBlankDO subQ = subQuestionMap.get(rel.getRelQuestionId());
                        String subTitle = subQ != null ? subQ.getTitle() : null;
                        AnswerDO a = answerMap.get(title.getQuestionId() + "_" + title.getId() + "_" + rel.getRelQuestionId());
                        if (a == null || StringUtils.isEmpty(a.getResponseText())) {
                            continue;
                        }
                        DynamicSubAnswerDTO dto = new DynamicSubAnswerDTO();
                        dto.setCircularTitle(title.getTitle());
                        dto.setSubQuestionTitle(subTitle);
                        dto.setAnswer(a.getResponseText());
                        list.add(dto);
                    }
                }
            } else {
                for (QuestionDynamicCircularTitleDO title : titles) {
//                    try {
//                        int i = Integer.parseInt(title.getTitle().substring(0, 1));
//                        if (i > maxCount) {
//                            break;
//                        }
//                    } catch (Exception e) {
//                        // TODO
//                    }
                    for (QuestionDynamicCircularRelDO rel : rels) {
                        QuestionFillBlankDO subQ = subQuestionMap.get(rel.getRelQuestionId());
                        String subTitle = subQ != null ? subQ.getTitle() : null;
                        AnswerDO a = answerMap.get(title.getQuestionId() + "_" + title.getId() + "_" + rel.getRelQuestionId());
                        DynamicSubAnswerDTO dto = new DynamicSubAnswerDTO();
                        dto.setCircularTitle(title.getTitle());
                        dto.setSubQuestionTitle(subTitle);
                        dto.setAnswer(a == null ? null : a.getResponseText());
                        list.add(dto);
                    }
                }
            }
            return list;
        } catch (Exception e) {
            log.error("处理动态循环题答案异常！", e);
        }
        return new ArrayList<>();
    }

    /**
     * 计算并保存品牌级别的display占比数据到pg_optimus_backups_brand_level_display_count表。
     * 逻辑：
     * 1. 查询pg_optimus_backups_flat_answer表，筛选circular_title=DisplayCnt的数据。
     * 2. 取出唯一的response_group_id。
     * 3. 查询同组下brand=Total的数据，分母为所有Total的answer之和。
     * 4. 计算每个brand的占比，组装PgOptimusBackupsBrandLevelDisplayCount对象，批量写入新表。
     *
     * @param taskAddressId 指定的taskAddressId
     */
    public String calcAndSaveBrandLevelDisplayCount(Long taskAddressId) {

        // 查询circular_title=DisplayCnt的数据
        List<PgOptimusBackupsFlatAnswer> displayCntList = flatAnswerMapper.selectList(
                new LambdaQueryWrapper<PgOptimusBackupsFlatAnswer>()
                        .eq(PgOptimusBackupsFlatAnswer::getTaskAddressId, taskAddressId)
                        .eq(PgOptimusBackupsFlatAnswer::getCircularTitle, "DisplayCnt")
                        .ne(PgOptimusBackupsFlatAnswer::getBrand, "Total")
        );

        if (displayCntList.isEmpty()) {
            return null;
        }

        // 取出唯一的response_group_id
        String responseGroupId = displayCntList.get(0).getResponseGroupId();

        // 查询同组下brand=Total的数据
        List<PgOptimusBackupsFlatAnswer> totalList = flatAnswerMapper.selectList(
                new LambdaQueryWrapper<PgOptimusBackupsFlatAnswer>()
                        .eq(PgOptimusBackupsFlatAnswer::getResponseGroupId, responseGroupId)
                        .eq(PgOptimusBackupsFlatAnswer::getCircularTitle, "DisplayCnt")
                        .eq(PgOptimusBackupsFlatAnswer::getBrand, "Total")
        );

        // 分母：所有Total的answer之和
        java.math.BigDecimal denominator = totalList.stream()
                .map(a -> safeToBigDecimal(a.getAnswer()))
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);

        List<PgOptimusBackupsBrandLevelDisplayCount> resultList = new ArrayList<>();

        for (PgOptimusBackupsFlatAnswer item : displayCntList) {

            java.math.BigDecimal numerator = safeToBigDecimal(item.getAnswer());
            java.math.BigDecimal ratio;
            if (denominator.compareTo(java.math.BigDecimal.ZERO) == 0) {
                ratio = java.math.BigDecimal.ZERO;
            } else {
                ratio = numerator.divide(denominator, 6, java.math.RoundingMode.HALF_UP);
            }

            PgOptimusBackupsBrandLevelDisplayCount result = new PgOptimusBackupsBrandLevelDisplayCount();
            result.setBanner(item.getBanner());
            result.setStoreCode(item.getStoreCode());
            result.setStoreType(item.getStoreType());
            result.setResponseGroupId(item.getResponseGroupId());
            result.setResponseId(item.getResponseId());
            result.setVisitDatetime(item.getVisitTime());
            result.setBrand(item.getBrand());
            result.setDisplayType(formatDisplayType(item.getDisplayType()));
            result.setDisplayCnt(safeToInt(item.getAnswer()));
            result.setTotalDisplayCountCat(denominator.intValue());
            result.setDisplayCountShare(ratio);
            // 其他字段如visitDatetime/visitCycle等可按需补充
            result.setCreateTime(new Date());
            result.setTaskAddressId(item.getTaskAddressId());
            resultList.add(result);
        }
        if (!resultList.isEmpty()) {
            pgOptimusBackupsBrandLevelDisplayCountService.saveBatch(resultList);
            return resultList.get(0).getResponseGroupId();
        }
        return null;
    }

    /**
     * 计算并保存品类级别的display占比数据到pg_optimus_backups_category_level_display_count表。
     * 逻辑：
     * 1. 查询pg_optimus_backups_flat_answer表，筛选circular_title=DisplayCnt且brand!=Total的数据。
     * 2. 取出唯一的response_group_id。
     * 3. 如果集合中任意一条answer=1，则分子为1，否则为0。
     * 4. 查询同组下brand=Total的数据，分母为所有Total的answer之和。
     * 5. 组装PgOptimusBackupsCategoryLevelDisplayCount对象，插入新表。
     *
     * @param taskAddressId 指定的taskAddressId
     */
    public void calcAndSaveCategoryLevelDisplayCount(Long taskAddressId) {

        // 查询circular_title=DisplayCnt且brand!=Total的数据
        List<PgOptimusBackupsFlatAnswer> displayCntList = flatAnswerMapper.selectList(
                new LambdaQueryWrapper<PgOptimusBackupsFlatAnswer>()
                        .eq(PgOptimusBackupsFlatAnswer::getTaskAddressId, taskAddressId)
                        .eq(PgOptimusBackupsFlatAnswer::getCircularTitle, "DisplayCnt")
                        .ne(PgOptimusBackupsFlatAnswer::getBrand, "Total")
        );

        if (displayCntList.isEmpty()) {
            return;
        }

        // 取出唯一的response_group_id
        String responseGroupId = displayCntList.get(0).getResponseGroupId();

        // 判断集合中是否有answer=1
        boolean hasOne = displayCntList.stream().anyMatch(a -> safeToInt(a.getAnswer()) == 1);
        int numerator = hasOne ? 1 : 0;

        // 查询同组下brand=Total的数据
        List<PgOptimusBackupsFlatAnswer> totalList = flatAnswerMapper.selectList(
                new LambdaQueryWrapper<PgOptimusBackupsFlatAnswer>()
                        .eq(PgOptimusBackupsFlatAnswer::getResponseGroupId, responseGroupId)
                        .eq(PgOptimusBackupsFlatAnswer::getCircularTitle, "DisplayCnt")
                        .eq(PgOptimusBackupsFlatAnswer::getBrand, "Total")
        );

        // 分母：所有Total的answer之和
        int denominator = totalList.stream()
                .mapToInt(a -> safeToInt(a.getAnswer()))
                .sum();

        java.math.BigDecimal ratio;
        if (denominator == 0) {
            ratio = java.math.BigDecimal.ZERO;
        } else {
            ratio = new java.math.BigDecimal(numerator).divide(new java.math.BigDecimal(denominator), 6, java.math.RoundingMode.HALF_UP);
        }

        // 组装新表对象
        PgOptimusBackupsCategoryLevelDisplayCount result = new PgOptimusBackupsCategoryLevelDisplayCount();
        PgOptimusBackupsFlatAnswer first = displayCntList.get(0);
        result.setBanner(first.getBanner());
        result.setStoreCode(first.getStoreCode());
        result.setStoreType(first.getStoreType());
        result.setResponseGroupId(first.getResponseGroupId());
        result.setResponseId(first.getResponseId());
        result.setVisitDatetime(first.getVisitTime());
        // 这里category字段请根据实际业务补充赋值
        result.setCategory(first.getCategoryName());
        result.setDisplayType(formatDisplayType(first.getDisplayType()));
        result.setDisplayCnt(numerator);
        result.setTotalDisplayCountCat(denominator);
        result.setDisplayCountShare(ratio);
        result.setCreateTime(new java.util.Date());
        result.setTaskAddressId(first.getTaskAddressId());
        // 其他字段如visitDatetime/visitCycle等可按需补充

        // 插入新表
        categoryLevelDisplayCountService.save(result);
    }

    /**
     * 计算并保存品牌级别的陈列面积数据到pg_optimus_backups_brand_level_display_size表（全组处理）。
     * <p>
     * 处理流程说明：
     * 1. 以 responseGroupId 为单位，查询该组下所有明细（flat answer），每个组内可能有多个 taskAddressId。
     * 2. 按 responseId+brand+product_form 生成所有品牌面积数据（每个品牌、每个 product_form 一条）。
     *    - 面积计算公式：品牌总占比 / direction_count * display_area。
     *    - answer 字段为百分比字符串，需转为小数再累加。
     * 3. 按 product_form 分组，组内 brand=Total 的 displaySize 累加，赋值给该 product_form 下所有数据的 display_size_cat_form 字段。
     *    - 业务含义：同一个陈列形态（product_form）下所有 brand=Total 的面积之和，反映该形态下的总面积。
     * 4. 所有 product_form 的 display_size_cat_form 再加和，赋值给所有数据的 display_size_cat 字段。
     *    - 业务含义：本组（responseGroupId）下所有陈列形态的总面积。
     * 5. 最终批量写入所有数据，保证组内所有 taskAddressId、所有品牌、所有 product_form 的面积结果和汇总字段都正确。
     *
     * @param responseGroupId 指定的 response_group_id
     */
    public void calcAndSaveBrandLevelDisplaySize(String responseGroupId) {

        // 1. 查询全组明细（同 response_group_id 下所有数据，包含多个 taskAddressId）
        List<PgOptimusBackupsFlatAnswer> rawList = flatAnswerMapper.selectList(
            new LambdaQueryWrapper<PgOptimusBackupsFlatAnswer>()
                .eq(PgOptimusBackupsFlatAnswer::getResponseGroupId, responseGroupId)
                .ne(PgOptimusBackupsFlatAnswer::getCircularTitle, "DisplayCnt")
        );
        if (rawList.isEmpty()) {
            return;
        }

        // 2. 按 responseId+brand+去前缀后的circularTitle 分组
        Map<String, List<PgOptimusBackupsFlatAnswer>> groupMap = rawList.stream()
            .collect(Collectors.groupingBy(a -> a.getResponseId() + "_" + a.getBrand() + "_" + normalizeCircularTitle(a.getCircularTitle())));
        List<PgOptimusBackupsBrandLevelDisplaySize> resultList = new ArrayList<>();
        for (List<PgOptimusBackupsFlatAnswer> group : groupMap.values()) {
            PgOptimusBackupsFlatAnswer first = group.get(0);
            // direction_count：本组明细的分母
            java.math.BigDecimal directionCount = safeToBigDecimal(first.getDirectionCount());
            // display_area：本组明细的总面积
            java.math.BigDecimal displayArea = first.getDisplayArea();
            // 计算品牌总占比（answer 字段为百分比字符串，需转为小数再累加）
            java.math.BigDecimal totalShare = group.stream()
                .map(a -> safeToBigDecimal(a.getAnswer()).divide(new java.math.BigDecimal("100"), 6, java.math.RoundingMode.HALF_UP))
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
            // 计算面积
            java.math.BigDecimal area = java.math.BigDecimal.ZERO;
            if (directionCount != null && directionCount.compareTo(java.math.BigDecimal.ZERO) != 0 && displayArea != null) {
                area = totalShare.divide(directionCount, 6, java.math.RoundingMode.HALF_UP).multiply(displayArea);
            }
            // 组装实体
            PgOptimusBackupsBrandLevelDisplaySize result = new PgOptimusBackupsBrandLevelDisplaySize();
            result.setBanner(first.getBanner());
            result.setStoreCode(first.getStoreCode());
            result.setStoreType(first.getStoreType());
            result.setCategory(first.getCategoryName());
            result.setBrand(first.getBrand());
            result.setProductForm(normalizeCircularTitle(first.getCircularTitle()));
            result.setVisitDatetime(first.getVisitTime());
            result.setResponseGroupId(first.getResponseGroupId());
            result.setResponseId(first.getResponseId());
            result.setDisplayType(formatDisplayType(first.getDisplayType()));
            result.setDisplaySize(area);
            result.setCreateTime(new java.util.Date());
            result.setTaskAddressId(first.getTaskAddressId());
            resultList.add(result);
        }

        // 保存前排序：taskAddressId、productForm、brand
        resultList.sort((a, b) -> {
            int cmp1 = a.getTaskAddressId().compareTo(b.getTaskAddressId());
            if (cmp1 != 0) return cmp1;
            String pfA = a.getProductForm() == null ? "" : a.getProductForm();
            String pfB = b.getProductForm() == null ? "" : b.getProductForm();
            int cmp2 = pfA.compareTo(pfB);
            if (cmp2 != 0) return cmp2;
            String brandA = a.getBrand() == null ? "" : a.getBrand();
            String brandB = b.getBrand() == null ? "" : b.getBrand();
            return brandA.compareTo(brandB);
        });

        // 3. 按 product_form 分组，组内 brand=Total 的 displaySize 累加赋值给 display_size_cat_form
        //    - 业务含义：同一个陈列形态下所有 brand=Total 的面积之和，反映该形态下的总面积
        Map<String, List<PgOptimusBackupsBrandLevelDisplaySize>> formGroup = resultList.stream()
            .collect(Collectors.groupingBy(x -> x.getProductForm() == null ? "" : x.getProductForm()));
        Map<String, java.math.BigDecimal> catFormMap = new HashMap<>();
        for (Map.Entry<String, List<PgOptimusBackupsBrandLevelDisplaySize>> entry : formGroup.entrySet()) {
            String productForm = entry.getKey();
            List<PgOptimusBackupsBrandLevelDisplaySize> list = entry.getValue();
            // 只统计 brand=Total 的面积
            java.math.BigDecimal sum = list.stream()
                .filter(x -> "Total".equals(x.getBrand()))
                .map(x -> x.getDisplaySize() == null ? java.math.BigDecimal.ZERO : x.getDisplaySize())
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
            catFormMap.put(productForm, sum);
            // 给该 product_form 下所有数据赋值
            list.forEach(x -> x.setDisplaySizeCatForm(sum));
        }

        // 4. 所有 product_form 的 display_size_cat_form 累加赋值给 display_size_cat
        //    - 业务含义：本组下所有陈列形态的总面积
        java.math.BigDecimal catSum = catFormMap.values().stream().reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
        resultList.forEach(x -> x.setDisplaySizeCat(catSum));

        // 5. 批量写入
        pgOptimusBackupsBrandLevelDisplaySizeService.saveBatch(resultList);
    }

    /**
     * 去除circularTitle前的序号和“-”，如“1-xxx”或“2-xxx”变成“xxx”
     */
    public static String normalizeCircularTitle(String circularTitle) {
        if (circularTitle == null) return "";
        return circularTitle.replaceFirst("^\\d+-", "");
    }

    /**
     * 统一格式化 displayType 字段（假设一定是json数组，直接提取circularTitle）
     * @param displayTypeJson display_type 字段的原始json数组字符串
     * @return 格式化后的 displayType
     */
    private static String formatDisplayType(String displayTypeJson) {
        if (displayTypeJson == null || displayTypeJson.trim().isEmpty()) {
            return "";
        }
        List<String> types = new ArrayList<>();
        // 直接解析为对象数组，提取 circularTitle
        List<DynamicSubAnswerDTO> dtoList = com.alibaba.fastjson.JSON.parseArray(displayTypeJson, DynamicSubAnswerDTO.class);
        for (DynamicSubAnswerDTO dto : dtoList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getCircularTitle())) {
                types.add(dto.getCircularTitle());
            }
        }
        // 处理逻辑
        if (types.size() >= 2) {
            return "HHD(" + String.join("+", types) + ")";
        } else if (types.size() == 1) {
            String t = types.get(0);
            if (t.contains("方桌FZ") || t.contains("方框FK")) {
                return "HHD:方桌FZ/方框FK";
            } else if (t.contains("方堆FD")) {
                return "HHD:方堆FD";
            } else if (t.contains("异形堆YXD")) {
                return "HHD:异形堆YXD";
            } else {
                // 其他类型原样返回
                return t;
            }
        }
        return displayTypeJson;
    }

    /**
     * 字符串安全转BigDecimal，异常时返回0。
     *
     * @param val 字符串
     * @return BigDecimal
     */
    private static java.math.BigDecimal safeToBigDecimal(String val) {
        try {
            return new java.math.BigDecimal(val);
        } catch (Exception e) {
            return java.math.BigDecimal.ZERO;
        }
    }

    /**
     * 字符串安全转Integer，异常时返回0。
     *
     * @param val 字符串
     * @return Integer
     */
    private static Integer safeToInt(String val) {
        try {
            return Integer.parseInt(val);
        } catch (Exception e) {
            return 0;
        }
    }
} 