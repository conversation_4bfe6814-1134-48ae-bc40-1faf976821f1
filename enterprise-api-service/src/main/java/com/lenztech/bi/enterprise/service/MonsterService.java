package com.lenztech.bi.enterprise.service;



import com.lenztech.bi.enterprise.dto.monster.MonsterMec2ndDisplayDTO;
import com.lenztech.bi.enterprise.dto.monster.MonsterPosmAvailabilityDTO;
import com.lenztech.bi.enterprise.dto.monster.QueryConditionsDTO;
import com.lenztech.bi.enterprise.entity.*;

import java.util.List;

public interface MonsterService {

    /**
     * 获取品牌分销率 渠道对比1
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterDistributionByBrand> getDistributionByBrandList(String period, String bg, String city);

    /**
     * 获取偏好分销率 渠道对比2
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterDistributionByFlavor> getDistributionByFlavorList(String period, String bg, String city);

    /**
     * 获取UTC偏好分销率 渠道对比3
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterUtcDistributionByFlavor> getUtcDistributionByFlavorList(String period, String bg, String city);

    /**
     * 获取Store % by MEC flavor number 渠道对比4
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterStoreRatioByMecFlavorNumber> getStoreRatioByMecFlavorNumberList(String period, String bg, String city);

    /**
     * 获取MecPositioningInMainShelf 渠道对比5
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterMecPositioningInMainShelf> getMecPositioningInMainShelfList(String period, String bg, String city);

    /**
     * 获取MonsterCokeCoolerRatioWithMec 渠道对比6
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterCokeCoolerRatioWithMec> getCokeCoolerRatioWithMecList(String period, String bg, String city);

    /**
     * 获取MecPositioningInMainShelf 渠道对比7
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterMecPositioningInCustomerCooler> getMecPositioningInCustomerCooler(String period, String bg, String city);

    /**
     * SuctionRackAvailability 渠道对比8
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterSuctionRackAvailability> getSuctionRackAvailabilityList(String period, String bg, String city);

    /**
     * 获取PriceParityWithRbg 渠道对比9
     * @param period
     * @param bg
     * @param city
     * @return
     */
    List<MonsterPriceParityWithRbg> getPriceParityWithRbgList(String period, String bg, String city);


    /**
     * 获取PosmAvailability年度对比1
     * @param period
     * @param bg
     * @return
     */
    MonsterPosmAvailabilityDTO getPosmAvailabilityList(String period, String bg);

    /**
     * 获取Mec2ndDisplay 年度对比2
     * @param period
     * @param bg
     * @return
     */
    MonsterMec2ndDisplayDTO getMec2ndDisplayList(String period, String bg);

    /**
     * 获取Mec2ndDisplay 年度对比3 查询条件
     * @return
     */
    QueryConditionsDTO getQueryConditionsYearComparisonEachCityList();

    /**
     * 获取yearComparisonEachCityList
     * @param period
     * @param channel
     * @return
     */
    List<MonsterYearComparisonEachCity> getYearComparisonEachCityList(String period, String channel);

    /**
     * 获取DealerData1 经销商数据1
     * @param retailer
     * @param city
     * @return
     */
    List<MonsterDealerData1> getDealerData1List(String retailer, String city);

    /**
     * 获取DealerData2 经销商数据2
     * @param retailer
     * @param city
     * @return
     */
    List<MonsterDealerData2> getDealerData2List(String retailer, String city);

    /**
     * 获取DealerData3 经销商数据3
     * @param retailer
     * @param city
     * @return
     */
    List<MonsterDealerData3> getDealerData3List(String retailer, String city);

    /**
     * 获取DealerData4 经销商数据4
     * @param retailer
     * @param city
     * @return
     */
    List<MonsterDealerData4> getDealerData4List(String retailer, String city);

    /**
     * 获取DealerData5 经销商数据5
     * @param retailer
     * @param city
     * @return
     */
    List<MonsterDealerData5> getDealerData5List(String retailer, String city);

    /**
     * 获取渠道对比查询条件
     * @return
     */
    QueryConditionsDTO getQueryConditionsChannelCompareList();

    /**
     * 获取年度对比查询条件
     * @return
     */
    QueryConditionsDTO getQueryConditionsAnnualComparisonList();

    /**
     * 获取经销商数据报表查询条件
     * @return
     */
    QueryConditionsDTO getQueryConditionsDealerDataList();

    /**
     * 获取经销商数据报表查询条件
     * @return
     */
    QueryConditionsDTO getQueryConditionsDealerDataListByRetail(String retail);
}
