package com.lenztech.bi.enterprise.entity;

public class InspectResult {
    private String checkCode;
    private String checkValue;

    // 构造函数
    public InspectResult() {}

    public InspectResult(String checkCode, String checkValue) {
        this.checkCode = checkCode;
        this.checkValue = checkValue;
    }

    // getter 和 setter 方法
    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getCheckValue() {
        return checkValue;
    }

    public void setCheckValue(String checkValue) {
        this.checkValue = checkValue;
    }
} 