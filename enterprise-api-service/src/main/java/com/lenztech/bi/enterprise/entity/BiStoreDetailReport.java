package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-18
 */
public class BiStoreDetailReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 答卷id
     */
    private String responseId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品品类
     */
    private String category;

    /**
     * 产品品牌
     */
    private String brand;

    /**
     * 产品考核类型（0未划分/1非必备本品/2竞品/3必备本品）
     */
    private Integer productCheckType;

    /**
     * 分销
     */
    private Integer isExist;

    /**
     * 面位
     */
    private BigDecimal facing;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public Integer getProductCheckType() {
        return productCheckType;
    }

    public void setProductCheckType(Integer productCheckType) {
        this.productCheckType = productCheckType;
    }
    public Integer getIsExist() {
        return isExist;
    }

    public void setIsExist(Integer isExist) {
        this.isExist = isExist;
    }
    public BigDecimal getFacing() {
        return facing;
    }

    public void setFacing(BigDecimal facing) {
        this.facing = facing;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BiStoreDetailReport{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", productId=" + productId +
        ", productName=" + productName +
        ", category=" + category +
        ", brand=" + brand +
        ", productCheckType=" + productCheckType +
        ", isExist=" + isExist +
        ", facing=" + facing +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
