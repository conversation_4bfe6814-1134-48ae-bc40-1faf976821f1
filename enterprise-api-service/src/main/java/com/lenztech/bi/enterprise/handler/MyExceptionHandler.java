package com.lenztech.bi.enterprise.handler;

import com.lenztech.bi.enterprise.dto.IStatusMessage;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.RetStatus;
import com.lenztech.bi.enterprise.exception.ServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Path;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@RestControllerAdvice
public class MyExceptionHandler {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @ExceptionHandler(Exception.class)
    public Object handleException(Exception e, HttpServletRequest request, HttpServletResponse response){
        logger.error("请求：{}发生异常：{}", request.getRequestURI(), e);
        ResponseData<Map> result = new ResponseData();
        if(e instanceof ServerException){
            result.setRetStatus(RetStatus.builder().retCode(IStatusMessage.SystemStatus.SERVER_ERROR.getCode()).errMsg(IStatusMessage.SystemStatus.SERVER_ERROR.getMessage()+e.getMessage()));

        }else{
            result.setRetStatus(RetStatus.builder().retCode(IStatusMessage.SystemStatus.PARAM_ERROR.getCode()).errMsg(IStatusMessage.SystemStatus.PARAM_ERROR.getMessage()+e.getMessage()));
        }
        return result;
    }

    /**
     * 自定义注解异常拦截
     */
    @ExceptionHandler({BindException.class, ConstraintViolationException.class, MethodArgumentNotValidException.class})
    public Object handleMethodArgumentNotValidException(Exception e, HttpServletRequest request) {
        logger.error("请求：{}发生异常：{}", request.getRequestURI(), e);
        // 错误信息
        StringBuilder sb = new StringBuilder("");
        // 错误信息map
        Map<String, String> error = new HashMap<>();
        String msg = "";
        if (!(e instanceof BindException) && !(e instanceof MethodArgumentNotValidException)) {
            for (ConstraintViolation cv: ((ConstraintViolationException)e).getConstraintViolations()) {
                msg = cv.getMessage();
                sb.append(msg).append("；");

                Iterator<Path.Node> it = cv.getPropertyPath().iterator();
                Path.Node last = null;
                while (it.hasNext()) {
                    last = (Path.Node)it.next();
                }
                /*for(last = null; it.hasNext(); last = (Path.Node)it.next()) {
                }*/
                error.put(last != null ? last.getName() : "", msg);
            }
        } else {
            List<ObjectError> allErrors = null;
            if (e instanceof BindException) {
                allErrors = ((BindException)e).getAllErrors();
            } else {
                allErrors = ((MethodArgumentNotValidException)e).getBindingResult().getAllErrors();
            }
            // 拼接错误信息
            for (ObjectError oe : allErrors) {
                msg = oe.getDefaultMessage();
                sb.append(msg).append("；");
                if (oe instanceof FieldError) {
                    error.put(((FieldError)oe).getField(), msg);
                } else {
                    error.put(oe.getObjectName(), msg);
                }
            }
        }

        ResponseData<Map> result = new ResponseData();
        result.setRetStatus(RetStatus.builder().retCode(IStatusMessage.SystemStatus.PARAM_ERROR.getCode()).errMsg(IStatusMessage.SystemStatus.PARAM_ERROR.getMessage()+sb.toString()));
        return result;
    }
}