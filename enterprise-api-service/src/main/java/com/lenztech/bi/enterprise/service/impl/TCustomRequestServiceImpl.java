//package com.lenztech.bi.enterprise.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.Wrapper;
//import com.lenztech.bi.enterprise.entity.TCustomRequest;
//import com.lenztech.bi.enterprise.mapper.task.TCustomRequestMapper;
//import com.lenztech.bi.enterprise.service.ITCustomRequestService;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.springframework.stereotype.Service;
//
///**
// * <p>
// * 接入客户特有数据存储表 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-08-28
// */
//@Service
//public class TCustomRequestServiceImpl extends ServiceImpl<TCustomRequestMapper, TCustomRequest> implements ITCustomRequestService {
//
//    @Override
//    public TCustomRequest getOne(Wrapper<TCustomRequest> queryWrapper, boolean throwEx) {
//        return super.getOne(queryWrapper, throwEx);
//    }
//}
