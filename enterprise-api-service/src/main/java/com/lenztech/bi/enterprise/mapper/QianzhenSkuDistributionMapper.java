package com.lenztech.bi.enterprise.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.entity.QianzhenSkuDistribution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 千镇图片sku Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Mapper
public interface QianzhenSkuDistributionMapper extends BaseMapper<QianzhenSkuDistribution> {

    List<QianzhenSkuDistribution> getListDynamicTable(@Param("responseId") String responseId, @Param("prefix") String prefix);
}
