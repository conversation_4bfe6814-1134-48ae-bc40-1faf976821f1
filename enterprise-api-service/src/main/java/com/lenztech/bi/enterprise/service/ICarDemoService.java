package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.car.AllPostCodeRateDTO;
import com.lenztech.bi.enterprise.dto.car.CarDetailDTO;
import com.lenztech.bi.enterprise.dto.car.CarPatternDTO;
import com.lenztech.bi.enterprise.dto.car.CarSellRateDTO;
import com.lenztech.bi.enterprise.dto.car.CarStatisticsDataDTO;
import com.lenztech.bi.enterprise.dto.car.CarStatisticsDataReq;
import com.lenztech.bi.enterprise.dto.car.LineChartStatisticsDTO;
import com.lenztech.bi.enterprise.dto.car.PeopleExecuteStatisticsDTO;
import com.lenztech.bi.enterprise.dto.car.PostCodeDTO;
import com.lenztech.bi.enterprise.dto.car.ProvincePostCodeDTO;


/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/1 13:50
 **/
public interface ICarDemoService {

    AllPostCodeRateDTO getAllPostCodeRate();

    ProvincePostCodeDTO getProvincePostCodeRate(String province);

    CarSellRateDTO getCarSellRate();

    CarStatisticsDataDTO getStatisticDataByBrand(CarStatisticsDataReq req);

    CarDetailDTO getDataByCarPattern(String pattern, Integer pageSize, Integer pageNo);

    LineChartStatisticsDTO getPeoplePerformAndStoreCover(String carType);

    PeopleExecuteStatisticsDTO getDataByPeoplePerform(String carType, String area);

    PostCodeDTO getAddressPostCodeRate(String address);
}
