package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 魔爪mec_positioning_in_main_shelf
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
public class MonsterMecPositioningInMainShelf extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 编号
     */
    private Integer noId;

    /**
     * 时间段
     */
    private String period;

    /**
     * BG
     */
    private String bg;

    /**
     * 城市
     */
    private String city;

    private String channel;

    private String fact;

    private String mecPositioningInMainShelf;

    /**
     * VS YA
     */
    private String vsYa;

    /**
     * VS PP
     */
    private String vsPp;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getNoId() {
        return noId;
    }

    public void setNoId(Integer noId) {
        this.noId = noId;
    }
    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }
    public String getBg() {
        return bg;
    }

    public void setBg(String bg) {
        this.bg = bg;
    }
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
    public String getFact() {
        return fact;
    }

    public void setFact(String fact) {
        this.fact = fact;
    }
    public String getMecPositioningInMainShelf() {
        return mecPositioningInMainShelf;
    }

    public void setMecPositioningInMainShelf(String mecPositioningInMainShelf) {
        this.mecPositioningInMainShelf = mecPositioningInMainShelf;
    }

    public String getVsYa() {
        return vsYa;
    }

    public void setVsYa(String vsYa) {
        this.vsYa = vsYa;
    }

    public String getVsPp() {
        return vsPp;
    }

    public void setVsPp(String vsPp) {
        this.vsPp = vsPp;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "MonsterMecPositioningInMainShelf{" +
        "id=" + id +
        ", noId=" + noId +
        ", period=" + period +
        ", bg=" + bg +
        ", city=" + city +
        ", channel=" + channel +
        ", fact=" + fact +
        ", mecPositioningInMainShelf=" + mecPositioningInMainShelf +

        ", vsPp=" + vsPp +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
