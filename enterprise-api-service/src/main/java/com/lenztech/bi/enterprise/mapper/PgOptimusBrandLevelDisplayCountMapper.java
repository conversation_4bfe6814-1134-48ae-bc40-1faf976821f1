package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.PgOptimusBrandLevelDisplayCount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * Brand级别二陈数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@DS("lenzbi-pg")
public interface PgOptimusBrandLevelDisplayCountMapper extends BaseMapper<PgOptimusBrandLevelDisplayCount> {

}
