package com.lenztech.bi.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lenztech.bi.enterprise.entity.PgOptimusBrandLevelDisplayCount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * Brand级别二陈数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@DS("lenzbi-pg")
public interface PgOptimusBrandLevelDisplayCountMapper extends BaseMapper<PgOptimusBrandLevelDisplayCount> {

    /**
     * 分页查询Brand级别二陈数据，通过子查询关联序列表
     * @param page 分页参数
     * @param searchDate 查询日期
     * @param responseGroupId 响应组ID，可为空
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT * FROM pg_optimus_brand_level_display_count " +
            "WHERE record_id IN (" +
            "  SELECT id FROM pg_optimus_realogram_sequence " +
            "  WHERE exec_date = #{searchDate} " +
            "  AND is_remained_scc = 1 " +
            "  AND process_status = 1 " +
            "  <if test='responseGroupId != null and responseGroupId != \"\"'>" +
            "    AND response_group_id = #{responseGroupId} " +
            "  </if>" +
            ") " +
            "ORDER BY visit_datetime DESC" +
            "</script>")
    IPage<PgOptimusBrandLevelDisplayCount> selectBrandLevelDisplayWithPagination(
            Page<PgOptimusBrandLevelDisplayCount> page,
            @Param("searchDate") String searchDate,
            @Param("responseGroupId") String responseGroupId);

}
