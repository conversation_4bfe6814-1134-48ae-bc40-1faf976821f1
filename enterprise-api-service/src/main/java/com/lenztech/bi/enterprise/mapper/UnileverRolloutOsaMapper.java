package com.lenztech.bi.enterprise.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.UnileverRolloutOsa;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@DS("lenzbi")
public interface UnileverRolloutOsaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(UnileverRolloutOsa record);

    int insertSelective(UnileverRolloutOsa record);

    UnileverRolloutOsa selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(UnileverRolloutOsa record);

    int updateByPrimaryKey(UnileverRolloutOsa record);

    List<UnileverRolloutOsa> getByResponseId(@Param("responseId") String responseId);
}