package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * <p>
 * SKU级别货架数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public class PgOptimusSkuLevelShelfData extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 执行时间
     */
    private LocalDate execDate;

    /**
     * 客户名称
     */
    private String banner;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * 制造商
     */
    private String manufacture;

    /**
     * 品类名称
     */
    private String category;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 产品形态
     */
    private String productForm;

    private String productFormCode;

    /**
     * 产品系列
     */
    private String lineUp;

    /**
     * 产品形式
     */
    private String productVariant;

    /**
     *  EAN产品编码 69码
     */
    private String eanCode;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 是否是宝洁，0非宝洁/1宝洁
     */
    private Boolean isPgProductFlag;

    /**
     * 拜访日期时间
     */
    private LocalDateTime visitDatetime;

    /**
     * 拜访第n次/月
     */
    private Integer visitCycle;

    /**
     * 拜访年份
     */
    private String visitYear;

    /**
     * 拜访月份
     */
    private String visitMonth;

    /**
     * 拜访周数
     */
    private String visitWeek;

    /**
     * group_id
     */
    private String responseGroupId;

    /**
     * rid
     */
    private String responseId;

    /**
     * 面位数据
     */
    private BigDecimal skuFacing;

    /**
     * 堆叠的面位数
     */
    private BigDecimal skuStackFacing;

    /**
     * Bay# 组数
     */
    private BigDecimal skuBay;

    /**
     * SKU 长度
     */
    private BigDecimal skuLength;

    /**
     * category total的面位
     */
    private BigDecimal totalFacingCat;

    /**
     * category total的堆叠面位
     */
    private BigDecimal totalStackFacingCat;

    /**
     * category total的bay
     */
    private BigDecimal totalBayCat;

    /**
     * category total的长度
     */
    private BigDecimal totalLengthCat;

    /**
     * category/form total的面位
     */
    private BigDecimal totalFacingForm;

    /**
     * category/form total的堆叠面位
     */
    private BigDecimal totalStackFacingForm;

    /**
     * category/form total的bay
     */
    private BigDecimal totalBayForm;

    /**
     * category/form total的长度
     */
    private BigDecimal totalLengthForm;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 数据是否被抽取过，抽取次数
     */
    private Integer extract;

    private Integer recordId;

    /**
     * 最后一次抽取时间
     */
    private LocalDateTime lastExtractTime;

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public LocalDate getExecDate() {
        return execDate;
    }

    public void setExecDate(LocalDate execDate) {
        this.execDate = execDate;
    }
    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }
    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }
    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }
    public String getManufacture() {
        return manufacture;
    }

    public void setManufacture(String manufacture) {
        this.manufacture = manufacture;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public String getProductForm() {
        return productForm;
    }

    public void setProductForm(String productForm) {
        this.productForm = productForm;
    }
    public String getLineUp() {
        return lineUp;
    }

    public void setLineUp(String lineUp) {
        this.lineUp = lineUp;
    }
    public String getProductVariant() {
        return productVariant;
    }

    public void setProductVariant(String productVariant) {
        this.productVariant = productVariant;
    }
    public String getEanCode() {
        return eanCode;
    }

    public void setEanCode(String eanCode) {
        this.eanCode = eanCode;
    }
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
    public Boolean getIsPgProductFlag() {
        return isPgProductFlag;
    }

    public void setIsPgProductFlag(Boolean isPgProductFlag) {
        this.isPgProductFlag = isPgProductFlag;
    }
    public LocalDateTime getVisitDatetime() {
        return visitDatetime;
    }

    public void setVisitDatetime(LocalDateTime visitDatetime) {
        this.visitDatetime = visitDatetime;
    }

    public Boolean getPgProductFlag() {
        return isPgProductFlag;
    }

    public void setPgProductFlag(Boolean pgProductFlag) {
        isPgProductFlag = pgProductFlag;
    }

    public String getProductFormCode() {
        return productFormCode;
    }

    public void setProductFormCode(String productFormCode) {
        this.productFormCode = productFormCode;
    }

    public Integer getVisitCycle() {
        return visitCycle;
    }

    public void setVisitCycle(Integer visitCycle) {
        this.visitCycle = visitCycle;
    }

    public String getVisitYear() {
        return visitYear;
    }

    public void setVisitYear(String visitYear) {
        this.visitYear = visitYear;
    }
    public String getVisitMonth() {
        return visitMonth;
    }

    public void setVisitMonth(String visitMonth) {
        this.visitMonth = visitMonth;
    }
    public String getVisitWeek() {
        return visitWeek;
    }

    public void setVisitWeek(String visitWeek) {
        this.visitWeek = visitWeek;
    }
    public String getResponseGroupId() {
        return responseGroupId;
    }

    public void setResponseGroupId(String responseGroupId) {
        this.responseGroupId = responseGroupId;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public BigDecimal getSkuFacing() {
        return skuFacing;
    }

    public void setSkuFacing(BigDecimal skuFacing) {
        this.skuFacing = skuFacing;
    }
    public BigDecimal getSkuStackFacing() {
        return skuStackFacing;
    }

    public void setSkuStackFacing(BigDecimal skuStackFacing) {
        this.skuStackFacing = skuStackFacing;
    }
    public BigDecimal getSkuBay() {
        return skuBay;
    }

    public void setSkuBay(BigDecimal skuBay) {
        this.skuBay = skuBay;
    }
    public BigDecimal getSkuLength() {
        return skuLength;
    }

    public void setSkuLength(BigDecimal skuLength) {
        this.skuLength = skuLength;
    }
    public BigDecimal getTotalFacingCat() {
        return totalFacingCat;
    }

    public void setTotalFacingCat(BigDecimal totalFacingCat) {
        this.totalFacingCat = totalFacingCat;
    }
    public BigDecimal getTotalStackFacingCat() {
        return totalStackFacingCat;
    }

    public void setTotalStackFacingCat(BigDecimal totalStackFacingCat) {
        this.totalStackFacingCat = totalStackFacingCat;
    }
    public BigDecimal getTotalBayCat() {
        return totalBayCat;
    }

    public void setTotalBayCat(BigDecimal totalBayCat) {
        this.totalBayCat = totalBayCat;
    }
    public BigDecimal getTotalLengthCat() {
        return totalLengthCat;
    }

    public void setTotalLengthCat(BigDecimal totalLengthCat) {
        this.totalLengthCat = totalLengthCat;
    }
    public BigDecimal getTotalFacingForm() {
        return totalFacingForm;
    }

    public void setTotalFacingForm(BigDecimal totalFacingForm) {
        this.totalFacingForm = totalFacingForm;
    }
    public BigDecimal getTotalStackFacingForm() {
        return totalStackFacingForm;
    }

    public void setTotalStackFacingForm(BigDecimal totalStackFacingForm) {
        this.totalStackFacingForm = totalStackFacingForm;
    }
    public BigDecimal getTotalBayForm() {
        return totalBayForm;
    }

    public void setTotalBayForm(BigDecimal totalBayForm) {
        this.totalBayForm = totalBayForm;
    }
    public BigDecimal getTotalLengthForm() {
        return totalLengthForm;
    }

    public void setTotalLengthForm(BigDecimal totalLengthForm) {
        this.totalLengthForm = totalLengthForm;
    }
    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    public Integer getExtract() {
        return extract;
    }

    public void setExtract(Integer extract) {
        this.extract = extract;
    }
    public LocalDateTime getLastExtractTime() {
        return lastExtractTime;
    }

    public void setLastExtractTime(LocalDateTime lastExtractTime) {
        this.lastExtractTime = lastExtractTime;
    }

    @Override
    public String toString() {
        return "PgOptimusSkuLevelShelfData{" +
            "id=" + id +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", execDate=" + execDate +
            ", banner=" + banner +
            ", storeCode=" + storeCode +
            ", storeType=" + storeType +
            ", manufacture=" + manufacture +
            ", category=" + category +
            ", brand=" + brand +
            ", productForm=" + productForm +
            ", lineUp=" + lineUp +
            ", productVariant=" + productVariant +
            ", eanCode=" + eanCode +
            ", skuName=" + skuName +
            ", isPgProductFlag=" + isPgProductFlag +
            ", visitDatetime=" + visitDatetime +
            ", visitCycle=" + visitCycle +
            ", visitYear=" + visitYear +
            ", visitMonth=" + visitMonth +
            ", visitWeek=" + visitWeek +
            ", responseGroupId=" + responseGroupId +
            ", responseId=" + responseId +
            ", skuFacing=" + skuFacing +
            ", skuStackFacing=" + skuStackFacing +
            ", skuBay=" + skuBay +
            ", skuLength=" + skuLength +
            ", totalFacingCat=" + totalFacingCat +
            ", totalStackFacingCat=" + totalStackFacingCat +
            ", totalBayCat=" + totalBayCat +
            ", totalLengthCat=" + totalLengthCat +
            ", totalFacingForm=" + totalFacingForm +
            ", totalStackFacingForm=" + totalStackFacingForm +
            ", totalBayForm=" + totalBayForm +
            ", totalLengthForm=" + totalLengthForm +
            ", productCode=" + productCode +
            ", extract=" + extract +
            ", lastExtractTime=" + lastExtractTime +
        "}";
    }
}
