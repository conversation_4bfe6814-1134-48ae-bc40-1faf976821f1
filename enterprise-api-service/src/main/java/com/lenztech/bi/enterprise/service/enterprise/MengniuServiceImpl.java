package com.lenztech.bi.enterprise.service.enterprise;

import com.lenztech.bi.enterprise.mapper.enterprise.MengniuMapper;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultImage;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultSku;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiTargetResp;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuH5BiTargetResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 蒙牛bi指标ServiceImpl
 *
 * <AUTHOR>
 * @date 2022-06-15 11:14:22
 */
@Slf4j
@Service
public class MengniuServiceImpl {

    private MengniuMapper mengniuMapper;

    public MengniuServiceImpl(MengniuMapper mengniuMapper) {
        this.mengniuMapper = mengniuMapper;
    }

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return ManonBiTargetResp
     */
    public MengniuBiTargetResp getBiTarget(String responseId) {
        log.info("蒙牛查询识别结果！responseId:" + responseId);
        MengniuBiTargetResp mengniuBiTargetResp = new MengniuBiTargetResp();
        try {
            List<MengniuBiResultImage> imageList = mengniuMapper.getImageList(responseId);
            List<MengniuBiResultSku> skuList = mengniuMapper.getSkuList(responseId);
            mengniuBiTargetResp.setResponseId(responseId);
            mengniuBiTargetResp.setImageList(imageList);
            mengniuBiTargetResp.setSkuList(skuList);
        } catch (Exception e) {
            log.error("查询蒙牛识别结果异常！responseId=", responseId, e);
        }
        return mengniuBiTargetResp;
    }

    /**
     * 查询指标集合
     *
     * @param responseId
     * @return ManonBiTargetResp
     */
    public MengniuH5BiTargetResp getH5BiTarget(String responseId) {
        log.info("蒙牛查询H5识别结果！responseId:" + responseId);
        MengniuH5BiTargetResp mengniuBiTargetResp = new MengniuH5BiTargetResp();
        try {
            mengniuBiTargetResp.setResponseId(responseId);
            mengniuBiTargetResp.setImageList(mengniuMapper.getImageList(responseId));
            mengniuBiTargetResp.setSummarySkuList(mengniuMapper.getSummarySkuList(responseId));
            mengniuBiTargetResp.setSummaryResult(mengniuMapper.getSummaryResult(responseId));
        } catch (Exception e) {
            log.error("蒙牛查询H5识别结果异常！responseId=", responseId, e);
        }
        return mengniuBiTargetResp;
    }

}
