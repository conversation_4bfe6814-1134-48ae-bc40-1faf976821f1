package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class TQuestion {

    /**
     * 操作id
     */
    @TableId("Id")
    private String Id;

    private String taskid;

    private String type;

    private String title;

    @TableField("optionValue")
    private String optionValue;

    private Integer uploadNum;

    private String indexOwner;

    private String qindex;


}
