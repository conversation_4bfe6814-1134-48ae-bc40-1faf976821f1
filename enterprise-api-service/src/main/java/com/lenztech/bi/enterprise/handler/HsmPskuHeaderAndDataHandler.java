package com.lenztech.bi.enterprise.handler;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.util.ListUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HsmPsku<PERSON>eaderAndDataHandler extends AnalysisEventListener<Object> {

    private Map<Integer, List<String>> headerLists = new HashMap<>(); // 用于存储每个sheet的表头
    private Map<Integer, List<Object>> dataLists = new HashMap<>(); // 用于存储每个sheet的数据
    private boolean isFirstRow = true;
    private int currentSheetIndex = -1; // 当前正在处理的sheet索引

    @Override
    public void invoke(Object object, AnalysisContext context) {
        if (isFirstRow) {
            isFirstRow = false;
            // object 是一个映射，键是列索引，值是单元格数据
            Map<Integer, String> rowCells = (Map<Integer, String>) object;
            for (Map.Entry<Integer, String> entry : rowCells.entrySet()) {
                headerLists.get(currentSheetIndex).add(entry.getValue());
            }
        } else {
            // 添加数据到对应sheet的数据列表
            dataLists.get(currentSheetIndex).add(object);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 所有数据解析完成后的操作
    }

    public Map<Integer, List<Object>> getAllDataLists() {
        return dataLists;
    }

    public Map<Integer, List<String>> getAllHeaders() {
        return headerLists;
    }


    // 提供一个方法来设置当前sheet的索引
    public void setCurrentSheetIndex(int index) {
        this.currentSheetIndex = index;
        this.headerLists.put(index, new ArrayList<>());
        this.dataLists.put(index, new ArrayList<>());
        this.isFirstRow = true;
    }
}
