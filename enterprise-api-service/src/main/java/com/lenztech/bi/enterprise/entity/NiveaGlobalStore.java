package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.util.Date;

/**
 * description
 *
 * @author: y<PERSON><PERSON>
 * @date：2024-04-12
 */
@Data
public class NiveaGlobalStore {

    private Long id;

    private String storeNumber;

    private String storeName;

    private String country;

    private String region;

    private String storeType;

    private String retailer;

    private String city;

    private String street;

    private String active;

    private String district;

    private String branch;

    private String state;

    private String attribute1;

    private String attribute2;

    private String attribute3;

    private String attribute4;

    private String attribute5;

    private String attribute6;

    private String attribute7;

    private String attribute8;

    private String attribute9;

    private String attribute10;

    private String phone;

    private String routeId;

    private Date createTime;

}
