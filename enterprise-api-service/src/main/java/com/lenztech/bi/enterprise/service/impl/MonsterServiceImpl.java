package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.monster.MonsterMec2ndDisplayDTO;
import com.lenztech.bi.enterprise.dto.monster.MonsterPosmAvailabilityDTO;
import com.lenztech.bi.enterprise.dto.monster.QueryConditionsDTO;
import com.lenztech.bi.enterprise.entity.*;
import com.lenztech.bi.enterprise.mapper.MonsterYearComparisonEachCityMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.*;
import com.lenztech.bi.enterprise.service.MonsterService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/** Created with IntelliJ IDEA. User: sunqingyuan Date: 2021/5/18 Time: 11:11 类功能: */
@Service
public class MonsterServiceImpl implements MonsterService {

  @Autowired private MonsterCokeCoolerRatioWithMecMapper monsterCokeCoolerRatioWithMecMapper;

  @Autowired private MonsterDistributionByBrandMapper monsterDistributionByBrandMapper;

  @Autowired private MonsterDistributionByFlavorMapper monsterDistributionByFlavorMapper;

  @Autowired private MonsterSuctionRackAvailabilityMapper monsterSuctionRackAvailabilityMapper;

  @Autowired private MonsterPriceParityWithRbgMapper monsterPriceParityWithRbgMapper;

  @Autowired private MonsterUtcDistributionByFlavorMapper monsterUtcDistributionByFlavorMapper;

  @Autowired
  private MonsterMecPositioningInCustomerCoolerMapper monsterMecPositioningInCustomerCoolerMapper;

  @Autowired private MonsterMecPositioningInMainShelfMapper monsterMecPositioningInMainShelfMapper;

  @Autowired
  private MonsterStoreRatioByMecFlavorNumberMapper monsterStoreRatioByMecFlavorNumberMapper;

  @Autowired private MonsterPosmAvailabilityMapper monsterPosmAvailabilityMapper;

  @Autowired private MonsterMec2ndDisplayMapper monsterMec2ndDisplayMapper;

  @Autowired private MonsterDealerData1Mapper monsterDealerData1Mapper;

  @Autowired private MonsterDealerData2Mapper monsterDealerData2Mapper;

  @Autowired private MonsterDealerData3Mapper monsterDealerData3Mapper;

  @Autowired private MonsterDealerData4Mapper monsterDealerData4Mapper;

  @Autowired private MonsterDealerData5Mapper monsterDealerData5Mapper;

  @Autowired private MonsterResultsMapper monsterResultsMapper;

  @Autowired private MonsterYearComparisonEachCityMapper monsterYearComparisonEachCityMapper;

  private static final String CHANNEL_HS = "H/S";

  private static final String CHANNEL_CVS = "CVS";

  private static final String CHANNEL_GT = "GT";

  private static final String CHANNEL_PETRO = "Petro";

  private static final String FLAVOR_UTC = "UTC";

  private static final String FLAVOR_UTC_GREEN = "UTC Green";

  private static final String FLAVOR_UTC_ULTRA = "UTC Ultra";

  private static final String FLAVOR_UTC_OTHERS = "UTC Others";

  @Override
  public List<MonsterDistributionByBrand> getDistributionByBrandList(
      String period, String bg, String city) {

    List<MonsterDistributionByBrand> monsterDistributionByBrandList =
        monsterResultsMapper.getMonsterDistributionByBrandAvgList(period, bg, city);

    List<MonsterDistributionByBrand> monsterDistributionByBrandListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(monsterDistributionByBrandList)) {
      Map<String, List<MonsterDistributionByBrand>> collect =
          monsterDistributionByBrandList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        monsterDistributionByBrandListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        monsterDistributionByBrandListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        monsterDistributionByBrandListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        monsterDistributionByBrandListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    return monsterDistributionByBrandListAfterSort;
  }

  @Override
  public List<MonsterDistributionByFlavor> getDistributionByFlavorList(
      String period, String bg, String city) {

    List<MonsterDistributionByFlavor> monsterDistributionByFlavorList =
        monsterResultsMapper.getMonsterDistributionByFlavorAvgList(period, bg, city);

    List<MonsterDistributionByFlavor> monsterDistributionByFlavorListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(monsterDistributionByFlavorList)) {
      Map<String, List<MonsterDistributionByFlavor>> collect =
          monsterDistributionByFlavorList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        monsterDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        monsterDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        monsterDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        monsterDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }
    return monsterDistributionByFlavorListAfterSort;
  }

  @Override
  public List<MonsterUtcDistributionByFlavor> getUtcDistributionByFlavorList(
      String period, String bg, String city) {

    List<MonsterUtcDistributionByFlavor> utcDistributionByFlavorList =
        monsterResultsMapper.getMonsterUtcDistributionByFlavorAvgList(period, bg, city);

    // 以产品给定的flavor排序
    utcDistributionByFlavorList = givenOrderByFlavor(utcDistributionByFlavorList);

    List<MonsterUtcDistributionByFlavor> utcDistributionByFlavorListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(utcDistributionByFlavorList)) {
      Map<String, List<MonsterUtcDistributionByFlavor>> collect =
          utcDistributionByFlavorList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    return utcDistributionByFlavorListAfterSort;
  }

  /**
   * 以产品给定的flavor排序
   *
   * @param utcDistributionByFlavorList
   * @return
   */
  public List<MonsterUtcDistributionByFlavor> givenOrderByFlavor(
      List<MonsterUtcDistributionByFlavor> utcDistributionByFlavorList) {

    List<MonsterUtcDistributionByFlavor> utcDistributionByFlavorListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(utcDistributionByFlavorList)) {
      Map<String, List<MonsterUtcDistributionByFlavor>> collect =
          utcDistributionByFlavorList.stream()
              .collect(Collectors.groupingBy(p -> p.getFlavor(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(FLAVOR_UTC))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(FLAVOR_UTC));
      }
      if (CollectionUtils.isNotEmpty(collect.get(FLAVOR_UTC_GREEN))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(FLAVOR_UTC_GREEN));
      }
      if (CollectionUtils.isNotEmpty(collect.get(FLAVOR_UTC_ULTRA))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(FLAVOR_UTC_ULTRA));
      }
      if (CollectionUtils.isNotEmpty(collect.get(FLAVOR_UTC_OTHERS))) {
        utcDistributionByFlavorListAfterSort.addAll(collect.get(FLAVOR_UTC_OTHERS));
      }
    }
    return utcDistributionByFlavorListAfterSort;
  }

  @Override
  public List<MonsterStoreRatioByMecFlavorNumber> getStoreRatioByMecFlavorNumberList(
      String period, String bg, String city) {

    List<MonsterStoreRatioByMecFlavorNumber> monsterStoreRatioByMecFlavorNumbers =
        monsterResultsMapper.getMonsterStoreRatioByMecFlavorNumberAvgList(period, bg, city);

    List<MonsterStoreRatioByMecFlavorNumber> monsterStoreRatioByMecFlavorNumbersAfterSort =
        new ArrayList<>();
    if (CollectionUtils.isNotEmpty(monsterStoreRatioByMecFlavorNumbers)) {
      Map<String, List<MonsterStoreRatioByMecFlavorNumber>> collect =
          monsterStoreRatioByMecFlavorNumbers.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        monsterStoreRatioByMecFlavorNumbersAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        monsterStoreRatioByMecFlavorNumbersAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        monsterStoreRatioByMecFlavorNumbersAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        monsterStoreRatioByMecFlavorNumbersAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    return monsterStoreRatioByMecFlavorNumbersAfterSort;
  }

  @Override
  public List<MonsterMecPositioningInMainShelf> getMecPositioningInMainShelfList(
      String period, String bg, String city) {

    List<MonsterMecPositioningInMainShelf> monsterMecPositioningInMainShelfList =
        monsterResultsMapper.getMonsterMecPositioningInMainShelfAvgList(period, bg, city);

    List<MonsterMecPositioningInMainShelf> monsterMecPositioningInMainShelfListAfterSort =
        new ArrayList<>();
    if (CollectionUtils.isNotEmpty(monsterMecPositioningInMainShelfList)) {
      Map<String, List<MonsterMecPositioningInMainShelf>> collect =
          monsterMecPositioningInMainShelfList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        monsterMecPositioningInMainShelfListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        monsterMecPositioningInMainShelfListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        monsterMecPositioningInMainShelfListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        monsterMecPositioningInMainShelfListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    return monsterMecPositioningInMainShelfListAfterSort;
  }

  @Override
  public List<MonsterCokeCoolerRatioWithMec> getCokeCoolerRatioWithMecList(
      String period, String bg, String city) {

    LambdaQueryWrapper<MonsterCokeCoolerRatioWithMec> cokeCoolerRatioWithMecLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    cokeCoolerRatioWithMecLambdaQueryWrapper.eq(MonsterCokeCoolerRatioWithMec::getPeriod, period);
    if (StringUtils.isNotBlank(bg)) {
      cokeCoolerRatioWithMecLambdaQueryWrapper.eq(MonsterCokeCoolerRatioWithMec::getBg, bg);
    }
    if (StringUtils.isNotBlank(city)) {
      cokeCoolerRatioWithMecLambdaQueryWrapper.eq(MonsterCokeCoolerRatioWithMec::getCity, city);
    }
    cokeCoolerRatioWithMecLambdaQueryWrapper.orderByAsc(MonsterCokeCoolerRatioWithMec::getChannel);
    List<MonsterCokeCoolerRatioWithMec> cokeCoolerRatioWithMecList =
        monsterCokeCoolerRatioWithMecMapper.selectList(cokeCoolerRatioWithMecLambdaQueryWrapper);

    List<MonsterCokeCoolerRatioWithMec> cokeCoolerRatioWithMecListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(cokeCoolerRatioWithMecList)) {
      Map<String, List<MonsterCokeCoolerRatioWithMec>> collect =
          cokeCoolerRatioWithMecList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        cokeCoolerRatioWithMecListAfterSort.add(
            getAverageMonsterCokeCoolerRatioWithMec(period, CHANNEL_HS, collect));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        cokeCoolerRatioWithMecListAfterSort.add(
            getAverageMonsterCokeCoolerRatioWithMec(period, CHANNEL_CVS, collect));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        cokeCoolerRatioWithMecListAfterSort.add(
            getAverageMonsterCokeCoolerRatioWithMec(period, CHANNEL_GT, collect));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        cokeCoolerRatioWithMecListAfterSort.add(
            getAverageMonsterCokeCoolerRatioWithMec(period, CHANNEL_PETRO, collect));
      }
    }

    return cokeCoolerRatioWithMecListAfterSort;
  }

  /**
   * 针对每种channel聚合 MonsterCokeCoolerRatioWithMec
   *
   * @param period
   * @param channelType
   * @param collect
   * @return
   */
  public MonsterCokeCoolerRatioWithMec getAverageMonsterCokeCoolerRatioWithMec(
      String period, String channelType, Map<String, List<MonsterCokeCoolerRatioWithMec>> collect) {

    MonsterCokeCoolerRatioWithMec monsterCokeCoolerRatioWithMec =
        new MonsterCokeCoolerRatioWithMec();
    monsterCokeCoolerRatioWithMec.setPeriod(period);
    monsterCokeCoolerRatioWithMec.setChannel(channelType);
    monsterCokeCoolerRatioWithMec.setCokeCoolerRatioWithMec(
        averageValueMonsterCokeCoolerRatioWithMec(collect.get(channelType), 0) + "");
    monsterCokeCoolerRatioWithMec.setVsYa(
        averageValueMonsterCokeCoolerRatioWithMec(collect.get(channelType), 1) + "");
    monsterCokeCoolerRatioWithMec.setVsPp(
        averageValueMonsterCokeCoolerRatioWithMec(collect.get(channelType), 2) + "");

    return monsterCokeCoolerRatioWithMec;
  }

  /**
   * 对某属性求平均值 MonsterCokeCoolerRatioWithMec
   *
   * @param monsterCokeCoolerRatioWithMecs
   * @param attributesType 0 coke_cooler_ratio_with_mec; 1 vs_ya; 2 vs_pp
   * @return
   */
  public double averageValueMonsterCokeCoolerRatioWithMec(
      List<MonsterCokeCoolerRatioWithMec> monsterCokeCoolerRatioWithMecs, int attributesType) {

    double averageValue = 0;
    double totalValue = 0;
    for (MonsterCokeCoolerRatioWithMec monsterCokeCoolerRatioWithMec :
        monsterCokeCoolerRatioWithMecs) {
      String attributesValue = "";
      if (attributesType == 0) {
        attributesValue = monsterCokeCoolerRatioWithMec.getCokeCoolerRatioWithMec();
      } else if (attributesType == 1) {
        attributesValue = monsterCokeCoolerRatioWithMec.getVsYa();
      } else if (attributesType == 2) {
        attributesValue = monsterCokeCoolerRatioWithMec.getVsPp();
      }
      if ("-".equals(attributesValue)) {
        attributesValue = "0";
      }
      totalValue = totalValue + Double.parseDouble(attributesValue);
    }
    averageValue = totalValue / monsterCokeCoolerRatioWithMecs.size();
    averageValue = clearDistortion(averageValue);
    return averageValue;
  }

  /**
   * 去除double失真
   *
   * @return
   */
  public double clearDistortion(double sourceData) {
    BigDecimal b = new BigDecimal(sourceData);
    sourceData = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    return sourceData;
  }

  @Override
  public List<MonsterMecPositioningInCustomerCooler> getMecPositioningInCustomerCooler(
      String period, String bg, String city) {

    List<MonsterMecPositioningInCustomerCooler> mecPositioningInCustomerCoolerList =
        monsterResultsMapper.getMonsterMecPositioningInCustomerCoolerAvgList(period, bg, city);

    List<MonsterMecPositioningInCustomerCooler> mecPositioningInCustomerCoolerListAfterSort =
        new ArrayList<>();
    if (CollectionUtils.isNotEmpty(mecPositioningInCustomerCoolerList)) {
      Map<String, List<MonsterMecPositioningInCustomerCooler>> collect =
          mecPositioningInCustomerCoolerList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        mecPositioningInCustomerCoolerListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        mecPositioningInCustomerCoolerListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        mecPositioningInCustomerCoolerListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        mecPositioningInCustomerCoolerListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    return mecPositioningInCustomerCoolerListAfterSort;
  }

  @Override
  public List<MonsterSuctionRackAvailability> getSuctionRackAvailabilityList(
      String period, String bg, String city) {

    List<MonsterSuctionRackAvailability> suctionRackAvailabilityList =
        monsterResultsMapper.getMonsterSuctionRackAvailabilityAvgList(period, bg, city);

    List<MonsterSuctionRackAvailability> suctionRackAvailabilityListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(suctionRackAvailabilityList)) {
      Map<String, List<MonsterSuctionRackAvailability>> collect =
          suctionRackAvailabilityList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        suctionRackAvailabilityListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        suctionRackAvailabilityListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        suctionRackAvailabilityListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        suctionRackAvailabilityListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    return suctionRackAvailabilityListAfterSort;
  }

  @Override
  public List<MonsterPriceParityWithRbg> getPriceParityWithRbgList(
      String period, String bg, String city) {

    LambdaQueryWrapper<MonsterPriceParityWithRbg> priceParityWithRbgLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    priceParityWithRbgLambdaQueryWrapper.eq(MonsterPriceParityWithRbg::getPeriod, period);
    if (StringUtils.isNotBlank(bg)) {
      priceParityWithRbgLambdaQueryWrapper.eq(MonsterPriceParityWithRbg::getBg, bg);
    }
    if (StringUtils.isNotBlank(city)) {
      priceParityWithRbgLambdaQueryWrapper.eq(MonsterPriceParityWithRbg::getCity, city);
    }

    priceParityWithRbgLambdaQueryWrapper.orderByAsc(MonsterPriceParityWithRbg::getChannel);
    List<MonsterPriceParityWithRbg> priceParityWithRbgList =
        monsterPriceParityWithRbgMapper.selectList(priceParityWithRbgLambdaQueryWrapper);

    List<MonsterPriceParityWithRbg> priceParityWithRbgListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(priceParityWithRbgList)) {
      Map<String, List<MonsterPriceParityWithRbg>> collect =
          priceParityWithRbgList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        priceParityWithRbgListAfterSort.add(
            getAverageMonsterPriceParityWithRbg(period, CHANNEL_HS, collect));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        priceParityWithRbgListAfterSort.add(
            getAverageMonsterPriceParityWithRbg(period, CHANNEL_CVS, collect));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        priceParityWithRbgListAfterSort.add(
            getAverageMonsterPriceParityWithRbg(period, CHANNEL_GT, collect));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        priceParityWithRbgListAfterSort.add(
            getAverageMonsterPriceParityWithRbg(period, CHANNEL_PETRO, collect));
      }
    }

    return priceParityWithRbgListAfterSort;
  }

  /**
   * 针对每种channel聚合 MonsterPriceParityWithRbg
   *
   * @param period
   * @param channelType
   * @param collect
   * @return
   */
  public MonsterPriceParityWithRbg getAverageMonsterPriceParityWithRbg(
      String period, String channelType, Map<String, List<MonsterPriceParityWithRbg>> collect) {

    MonsterPriceParityWithRbg monsterCokeCoolerRatioWithMec = new MonsterPriceParityWithRbg();
    monsterCokeCoolerRatioWithMec.setPeriod(period);
    monsterCokeCoolerRatioWithMec.setChannel(channelType);
    monsterCokeCoolerRatioWithMec.setPriceParityWithRbg(
        averageValueMonsterPriceParityWithRbg(collect.get(channelType), 0) + "");
    monsterCokeCoolerRatioWithMec.setVsYa(
        averageValueMonsterPriceParityWithRbg(collect.get(channelType), 1) + "");
    monsterCokeCoolerRatioWithMec.setVsPp(
        averageValueMonsterPriceParityWithRbg(collect.get(channelType), 2) + "");

    return monsterCokeCoolerRatioWithMec;
  }

  /**
   * 对某属性求平均值 MonsterPriceParityWithRbg
   *
   * @param monsterPriceParityWithRbgs
   * @param attributesType 0 priceParityWithRbg; 1 vs_ya; 2 vs_pp
   * @return
   */
  public double averageValueMonsterPriceParityWithRbg(
      List<MonsterPriceParityWithRbg> monsterPriceParityWithRbgs, int attributesType) {

    double averageValue = 0;
    double totalValue = 0;
    for (MonsterPriceParityWithRbg monsterSuctionRackAvailability : monsterPriceParityWithRbgs) {
      String attributesValue = "";
      if (attributesType == 0) {
        attributesValue = monsterSuctionRackAvailability.getPriceParityWithRbg();
      } else if (attributesType == 1) {
        attributesValue = monsterSuctionRackAvailability.getVsYa();
      } else if (attributesType == 2) {
        attributesValue = monsterSuctionRackAvailability.getVsPp();
      }
      if ("-".equals(attributesValue)) {
        attributesValue = "0";
      }
      totalValue = totalValue + Double.parseDouble(attributesValue);
    }
    averageValue = totalValue / monsterPriceParityWithRbgs.size();
    averageValue = clearDistortion(averageValue);
    return averageValue;
  }

  @Override
  public MonsterPosmAvailabilityDTO getPosmAvailabilityList(String period, String bg) {

    MonsterPosmAvailabilityDTO monsterPosmAvailabilityDTO = new MonsterPosmAvailabilityDTO();
    LambdaQueryWrapper<MonsterPosmAvailability> posmAvailabilityLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    posmAvailabilityLambdaQueryWrapper.eq(MonsterPosmAvailability::getPeriod, period);
    posmAvailabilityLambdaQueryWrapper.eq(MonsterPosmAvailability::getBg, bg);
    posmAvailabilityLambdaQueryWrapper.orderByAsc(MonsterPosmAvailability::getChannel);
    List<MonsterPosmAvailability> posmAvailabilityList =
        monsterPosmAvailabilityMapper.selectList(posmAvailabilityLambdaQueryWrapper);

    List<MonsterPosmAvailability> posmAvailabilityListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(posmAvailabilityList)) {
      Map<String, List<MonsterPosmAvailability>> collect =
          posmAvailabilityList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        posmAvailabilityListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        posmAvailabilityListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        posmAvailabilityListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        posmAvailabilityListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    monsterPosmAvailabilityDTO.setMonsterPosmAvailabilityList(posmAvailabilityListAfterSort);

    List<MonsterPosmAvailability> monsterPosmAvailabilityAvgList =
        monsterResultsMapper.getMonsterPosmAvailabilityAvgList(period, bg);
    monsterPosmAvailabilityDTO.setMonsterPosmAvailabilityAvgList(monsterPosmAvailabilityAvgList);

    return monsterPosmAvailabilityDTO;
  }

  @Override
  public MonsterMec2ndDisplayDTO getMec2ndDisplayList(String period, String bg) {

    MonsterMec2ndDisplayDTO monsterMec2ndDisplayDTO = new MonsterMec2ndDisplayDTO();
    LambdaQueryWrapper<MonsterMec2ndDisplay> monsterMec2ndDisplayLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    monsterMec2ndDisplayLambdaQueryWrapper.eq(MonsterMec2ndDisplay::getPeriod, period);
    monsterMec2ndDisplayLambdaQueryWrapper.eq(MonsterMec2ndDisplay::getBg, bg);
    monsterMec2ndDisplayLambdaQueryWrapper.orderByAsc(MonsterMec2ndDisplay::getChannel);
    List<MonsterMec2ndDisplay> monsterMec2ndDisplayList =
        monsterMec2ndDisplayMapper.selectList(monsterMec2ndDisplayLambdaQueryWrapper);

    List<MonsterMec2ndDisplay> monsterMec2ndDisplayListAfterSort = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(monsterMec2ndDisplayList)) {
      Map<String, List<MonsterMec2ndDisplay>> collect =
          monsterMec2ndDisplayList.stream()
              .collect(Collectors.groupingBy(p -> p.getChannel(), Collectors.toList()));
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_HS))) {
        monsterMec2ndDisplayListAfterSort.addAll(collect.get(CHANNEL_HS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_CVS))) {
        monsterMec2ndDisplayListAfterSort.addAll(collect.get(CHANNEL_CVS));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_GT))) {
        monsterMec2ndDisplayListAfterSort.addAll(collect.get(CHANNEL_GT));
      }
      if (CollectionUtils.isNotEmpty(collect.get(CHANNEL_PETRO))) {
        monsterMec2ndDisplayListAfterSort.addAll(collect.get(CHANNEL_PETRO));
      }
    }

    monsterMec2ndDisplayDTO.setMonsterMec2ndDisplayList(monsterMec2ndDisplayListAfterSort);

    List<MonsterMec2ndDisplay> monsterMec2ndDisplayAvgList =
        monsterResultsMapper.getMonsterMec2ndDisplayAvgList(period, bg);
    monsterMec2ndDisplayDTO.setMonsterMec2ndDisplayAvgList(monsterMec2ndDisplayAvgList);

    return monsterMec2ndDisplayDTO;
  }

  @Override
  public QueryConditionsDTO getQueryConditionsYearComparisonEachCityList() {

    QueryConditionsDTO queryConditionsDTO = new QueryConditionsDTO();
    queryConditionsDTO.setChannelList(new ArrayList<>());
    queryConditionsDTO.setPeriodList(new ArrayList<>());

    LambdaQueryWrapper<MonsterYearComparisonEachCity> yearComparisonEachCityLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    yearComparisonEachCityLambdaQueryWrapper.groupBy(MonsterYearComparisonEachCity::getPeriod);

    List<MonsterYearComparisonEachCity> yearComparisonEachCityList =
        monsterYearComparisonEachCityMapper.selectList(yearComparisonEachCityLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(yearComparisonEachCityList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              yearComparisonEachCityList.stream()
                  .map(MonsterYearComparisonEachCity::getPeriod)
                  .collect(Collectors.toList()));
    }

    yearComparisonEachCityLambdaQueryWrapper = new LambdaQueryWrapper<>();
    yearComparisonEachCityLambdaQueryWrapper.groupBy(MonsterYearComparisonEachCity::getChannel);

    List<MonsterYearComparisonEachCity> yearComparisonEachCityByChannelList =
        monsterYearComparisonEachCityMapper.selectList(yearComparisonEachCityLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(yearComparisonEachCityByChannelList)) {
      queryConditionsDTO
          .getChannelList()
          .addAll(
              yearComparisonEachCityByChannelList.stream()
                  .map(MonsterYearComparisonEachCity::getChannel)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  @Override
  public List<MonsterYearComparisonEachCity> getYearComparisonEachCityList(
      String period, String channel) {

    LambdaQueryWrapper<MonsterYearComparisonEachCity> yearComparisonEachCityLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    if (StringUtils.isNotBlank(period)) {
      yearComparisonEachCityLambdaQueryWrapper.eq(MonsterYearComparisonEachCity::getPeriod, period);
    }
    if (StringUtils.isNotBlank(channel)) {
      yearComparisonEachCityLambdaQueryWrapper.eq(
          MonsterYearComparisonEachCity::getChannel, channel);
    }

    List<MonsterYearComparisonEachCity> yearComparisonEachCityList =
        monsterYearComparisonEachCityMapper.selectList(yearComparisonEachCityLambdaQueryWrapper);

    return yearComparisonEachCityList;
  }

  @Override
  public List<MonsterDealerData1> getDealerData1List(String retailer, String city) {

    LambdaQueryWrapper<MonsterDealerData1> dealerData1LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData1LambdaQueryWrapper.eq(MonsterDealerData1::getRetailer, retailer);
    dealerData1LambdaQueryWrapper.eq(MonsterDealerData1::getCity, city);
    List<MonsterDealerData1> dealerData1List =
        monsterDealerData1Mapper.selectList(dealerData1LambdaQueryWrapper);

    return dealerData1List;
  }

  @Override
  public List<MonsterDealerData2> getDealerData2List(String retailer, String city) {

    LambdaQueryWrapper<MonsterDealerData2> dealerData2LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData2LambdaQueryWrapper.eq(MonsterDealerData2::getRetailer, retailer);
    dealerData2LambdaQueryWrapper.eq(MonsterDealerData2::getCity, city);
    List<MonsterDealerData2> dealerData2List =
        monsterDealerData2Mapper.selectList(dealerData2LambdaQueryWrapper);

    return dealerData2List;
  }

  @Override
  public List<MonsterDealerData3> getDealerData3List(String retailer, String city) {

    LambdaQueryWrapper<MonsterDealerData3> dealerData3LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData3LambdaQueryWrapper.eq(MonsterDealerData3::getRetailer, retailer);
    dealerData3LambdaQueryWrapper.eq(MonsterDealerData3::getCity, city);
    List<MonsterDealerData3> dealerData3List =
        monsterDealerData3Mapper.selectList(dealerData3LambdaQueryWrapper);

    return dealerData3List;
  }

  @Override
  public List<MonsterDealerData4> getDealerData4List(String retailer, String city) {

    LambdaQueryWrapper<MonsterDealerData4> dealerData4LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData4LambdaQueryWrapper.eq(MonsterDealerData4::getRetailer, retailer);
    dealerData4LambdaQueryWrapper.eq(MonsterDealerData4::getCity, city);
    List<MonsterDealerData4> dealerData4List =
        monsterDealerData4Mapper.selectList(dealerData4LambdaQueryWrapper);

    return dealerData4List;
  }

  @Override
  public List<MonsterDealerData5> getDealerData5List(String retailer, String city) {

    LambdaQueryWrapper<MonsterDealerData5> dealerData5LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData5LambdaQueryWrapper.eq(MonsterDealerData5::getRetailer, retailer);
    dealerData5LambdaQueryWrapper.eq(MonsterDealerData5::getCity, city);
    List<MonsterDealerData5> dealerData5List =
        monsterDealerData5Mapper.selectList(dealerData5LambdaQueryWrapper);

    return dealerData5List;
  }

  @Override
  public QueryConditionsDTO getQueryConditionsChannelCompareList() {

    QueryConditionsDTO queryConditionsDTO = new QueryConditionsDTO();
    queryConditionsDTO.setCityList(new ArrayList<>());
    queryConditionsDTO.setBgList(new ArrayList<>());
    queryConditionsDTO.setPeriodList(new ArrayList<>());
    // 封装渠道对比数据
    queryConditionsDTO = getQueryConditionsMonsterDistributionByBrand(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterDistributionByFlavor(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterUtcDistributionByFlavor(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterStoreRatioByMecFlavorNumber(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterMecPositioningInMainShelf(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterCokeCoolerRatioWithMec(queryConditionsDTO);
    queryConditionsDTO =
        getQueryConditionsMonsterMecPositioningInCustomerCooler(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterSuctionRackAvailability(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterPriceParityWithRbg(queryConditionsDTO);

    queryConditionsDTO.setCityList(
        queryConditionsDTO.getCityList().stream().distinct().collect(Collectors.toList()));
    queryConditionsDTO.setCityList(
        queryConditionsDTO.getCityList().stream()
            .filter(string -> !string.isEmpty())
            .collect(Collectors.toList()));

    queryConditionsDTO.setPeriodList(
        queryConditionsDTO.getPeriodList().stream().distinct().collect(Collectors.toList()));
    queryConditionsDTO.setPeriodList(
        queryConditionsDTO.getPeriodList().stream()
            .filter(string -> !string.isEmpty())
            .collect(Collectors.toList()));

    queryConditionsDTO.setBgList(
        queryConditionsDTO.getBgList().stream().distinct().collect(Collectors.toList()));
    queryConditionsDTO.setBgList(
        queryConditionsDTO.getBgList().stream()
            .filter(string -> !string.isEmpty())
            .collect(Collectors.toList()));

    List<String> bgList = new ArrayList<>();
    bgList.add("Total");
    for (String bg : queryConditionsDTO.getBgList()) {
      if (!"Total".equals(bg)) {
        bgList.add(bg);
      }
    }
    queryConditionsDTO.setBgList(bgList);
    return queryConditionsDTO;
  }

  @Override
  public QueryConditionsDTO getQueryConditionsAnnualComparisonList() {

    QueryConditionsDTO queryConditionsDTO = new QueryConditionsDTO();
    queryConditionsDTO.setBgList(new ArrayList<>());
    queryConditionsDTO.setPeriodList(new ArrayList<>());
    // 封装年度对比
    queryConditionsDTO = getQueryConditionsMonsterPosmAvailability(queryConditionsDTO);
    queryConditionsDTO = getQueryConditionsMonsterMec2ndDisplay(queryConditionsDTO);

    queryConditionsDTO.setPeriodList(
        queryConditionsDTO.getPeriodList().stream().distinct().collect(Collectors.toList()));
    queryConditionsDTO.setBgList(
        queryConditionsDTO.getBgList().stream().distinct().collect(Collectors.toList()));

    return queryConditionsDTO;
  }

  @Override
  public QueryConditionsDTO getQueryConditionsDealerDataList() {

    QueryConditionsDTO queryConditionsDTO = new QueryConditionsDTO();
    queryConditionsDTO.setRetailerList(new ArrayList<>());
    queryConditionsDTO.setCityList(new ArrayList<>());
    // 封装经销商数据对比
    queryConditionsDTO = getQueryConditionsMonsterDealerData1(queryConditionsDTO, "");
    queryConditionsDTO = getQueryConditionsMonsterDealerData2(queryConditionsDTO, "");
    queryConditionsDTO = getQueryConditionsMonsterDealerData3(queryConditionsDTO, "");
    queryConditionsDTO = getQueryConditionsMonsterDealerData4(queryConditionsDTO, "");
    queryConditionsDTO = getQueryConditionsMonsterDealerData5(queryConditionsDTO, "");

    queryConditionsDTO.setRetailerList(
        queryConditionsDTO.getRetailerList().stream().distinct().collect(Collectors.toList()));
    queryConditionsDTO.setCityList(
        queryConditionsDTO.getCityList().stream().distinct().collect(Collectors.toList()));

    return queryConditionsDTO;
  }

  @Override
  public QueryConditionsDTO getQueryConditionsDealerDataListByRetail(String retail) {

    QueryConditionsDTO queryConditionsDTO = new QueryConditionsDTO();
    queryConditionsDTO.setRetailerList(new ArrayList<>());
    queryConditionsDTO.setCityList(new ArrayList<>());
    // 封装经销商数据对比
    queryConditionsDTO = getQueryConditionsMonsterDealerData1(queryConditionsDTO, retail);
    queryConditionsDTO = getQueryConditionsMonsterDealerData2(queryConditionsDTO, retail);
    queryConditionsDTO = getQueryConditionsMonsterDealerData3(queryConditionsDTO, retail);
    queryConditionsDTO = getQueryConditionsMonsterDealerData4(queryConditionsDTO, retail);
    queryConditionsDTO = getQueryConditionsMonsterDealerData5(queryConditionsDTO, retail);

    List<String> retailerList = new ArrayList<>();
    retailerList.add(retail);
    queryConditionsDTO.setRetailerList(retailerList);
    queryConditionsDTO.setCityList(
        queryConditionsDTO.getCityList().stream().distinct().collect(Collectors.toList()));

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterDistributionByBrand条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterDistributionByBrand(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterDistributionByBrand> distributionByBrandList = new ArrayList<>();
    LambdaQueryWrapper<MonsterDistributionByBrand> distributionByBrandLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    distributionByBrandLambdaQueryWrapper.groupBy(MonsterDistributionByBrand::getPeriod);
    distributionByBrandList =
        monsterDistributionByBrandMapper.selectList(distributionByBrandLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(distributionByBrandList)) {
      List<String> aa =
          distributionByBrandList.stream()
              .map(MonsterDistributionByBrand::getPeriod)
              .collect(Collectors.toList());
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              distributionByBrandList.stream()
                  .map(MonsterDistributionByBrand::getPeriod)
                  .collect(Collectors.toList()));
    }

    distributionByBrandLambdaQueryWrapper = new LambdaQueryWrapper<>();
    distributionByBrandLambdaQueryWrapper.groupBy(MonsterDistributionByBrand::getBg);
    distributionByBrandList.clear();
    distributionByBrandList =
        monsterDistributionByBrandMapper.selectList(distributionByBrandLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(distributionByBrandList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              distributionByBrandList.stream()
                  .map(MonsterDistributionByBrand::getBg)
                  .collect(Collectors.toList()));
    }

    distributionByBrandLambdaQueryWrapper = new LambdaQueryWrapper<>();
    distributionByBrandLambdaQueryWrapper.groupBy(MonsterDistributionByBrand::getCity);
    distributionByBrandList.clear();
    distributionByBrandList =
        monsterDistributionByBrandMapper.selectList(distributionByBrandLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(distributionByBrandList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              distributionByBrandList.stream()
                  .map(MonsterDistributionByBrand::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterDistributionByFlavor条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterDistributionByFlavor(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterDistributionByFlavor> distributionByFlavorList = new ArrayList<>();
    LambdaQueryWrapper<MonsterDistributionByFlavor> distributionByFlavorLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    distributionByFlavorLambdaQueryWrapper.groupBy(MonsterDistributionByFlavor::getPeriod);
    distributionByFlavorList =
        monsterDistributionByFlavorMapper.selectList(distributionByFlavorLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(distributionByFlavorList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              distributionByFlavorList.stream()
                  .map(MonsterDistributionByFlavor::getPeriod)
                  .collect(Collectors.toList()));
    }

    distributionByFlavorLambdaQueryWrapper = new LambdaQueryWrapper<>();
    distributionByFlavorLambdaQueryWrapper.groupBy(MonsterDistributionByFlavor::getBg);
    distributionByFlavorList.clear();
    distributionByFlavorList =
        monsterDistributionByFlavorMapper.selectList(distributionByFlavorLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(distributionByFlavorList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              distributionByFlavorList.stream()
                  .map(MonsterDistributionByFlavor::getBg)
                  .collect(Collectors.toList()));
    }

    distributionByFlavorLambdaQueryWrapper = new LambdaQueryWrapper<>();
    distributionByFlavorLambdaQueryWrapper.groupBy(MonsterDistributionByFlavor::getCity);
    distributionByFlavorList.clear();
    distributionByFlavorList =
        monsterDistributionByFlavorMapper.selectList(distributionByFlavorLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(distributionByFlavorList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              distributionByFlavorList.stream()
                  .map(MonsterDistributionByFlavor::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterUtcDistributionByFlavor条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterUtcDistributionByFlavor(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterUtcDistributionByFlavor> utcDistributionByFlavorList = new ArrayList<>();
    LambdaQueryWrapper<MonsterUtcDistributionByFlavor> distributionByFlavorLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    distributionByFlavorLambdaQueryWrapper.groupBy(MonsterUtcDistributionByFlavor::getPeriod);
    utcDistributionByFlavorList =
        monsterUtcDistributionByFlavorMapper.selectList(distributionByFlavorLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(utcDistributionByFlavorList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              utcDistributionByFlavorList.stream()
                  .map(MonsterUtcDistributionByFlavor::getPeriod)
                  .collect(Collectors.toList()));
    }

    distributionByFlavorLambdaQueryWrapper = new LambdaQueryWrapper<>();
    distributionByFlavorLambdaQueryWrapper.groupBy(MonsterUtcDistributionByFlavor::getBg);
    utcDistributionByFlavorList.clear();
    utcDistributionByFlavorList =
        monsterUtcDistributionByFlavorMapper.selectList(distributionByFlavorLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(utcDistributionByFlavorList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              utcDistributionByFlavorList.stream()
                  .map(MonsterUtcDistributionByFlavor::getBg)
                  .collect(Collectors.toList()));
    }

    distributionByFlavorLambdaQueryWrapper = new LambdaQueryWrapper<>();
    distributionByFlavorLambdaQueryWrapper.groupBy(MonsterUtcDistributionByFlavor::getCity);
    utcDistributionByFlavorList.clear();
    utcDistributionByFlavorList =
        monsterUtcDistributionByFlavorMapper.selectList(distributionByFlavorLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(utcDistributionByFlavorList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              utcDistributionByFlavorList.stream()
                  .map(MonsterUtcDistributionByFlavor::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterStoreRatioByMecFlavorNumber条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterStoreRatioByMecFlavorNumber(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterStoreRatioByMecFlavorNumber> storeRatioByMecFlavorNumberList = new ArrayList<>();
    LambdaQueryWrapper<MonsterStoreRatioByMecFlavorNumber>
        storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterStoreRatioByMecFlavorNumber::getPeriod);
    storeRatioByMecFlavorNumberList =
        monsterStoreRatioByMecFlavorNumberMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(storeRatioByMecFlavorNumberList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              storeRatioByMecFlavorNumberList.stream()
                  .map(MonsterStoreRatioByMecFlavorNumber::getPeriod)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterStoreRatioByMecFlavorNumber::getBg);
    storeRatioByMecFlavorNumberList.clear();
    storeRatioByMecFlavorNumberList =
        monsterStoreRatioByMecFlavorNumberMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(storeRatioByMecFlavorNumberList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              storeRatioByMecFlavorNumberList.stream()
                  .map(MonsterStoreRatioByMecFlavorNumber::getBg)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterStoreRatioByMecFlavorNumber::getCity);
    storeRatioByMecFlavorNumberList.clear();
    storeRatioByMecFlavorNumberList =
        monsterStoreRatioByMecFlavorNumberMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(storeRatioByMecFlavorNumberList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              storeRatioByMecFlavorNumberList.stream()
                  .map(MonsterStoreRatioByMecFlavorNumber::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterMecPositioningInMainShelf条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterMecPositioningInMainShelf(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterMecPositioningInMainShelf> mecPositioningInMainShelfList = new ArrayList<>();
    LambdaQueryWrapper<MonsterMecPositioningInMainShelf>
        storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterMecPositioningInMainShelf::getPeriod);
    mecPositioningInMainShelfList =
        monsterMecPositioningInMainShelfMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mecPositioningInMainShelfList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              mecPositioningInMainShelfList.stream()
                  .map(MonsterMecPositioningInMainShelf::getPeriod)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(MonsterMecPositioningInMainShelf::getBg);
    mecPositioningInMainShelfList.clear();
    mecPositioningInMainShelfList =
        monsterMecPositioningInMainShelfMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mecPositioningInMainShelfList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              mecPositioningInMainShelfList.stream()
                  .map(MonsterMecPositioningInMainShelf::getBg)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterMecPositioningInMainShelf::getCity);
    mecPositioningInMainShelfList.clear();
    mecPositioningInMainShelfList =
        monsterMecPositioningInMainShelfMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mecPositioningInMainShelfList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              mecPositioningInMainShelfList.stream()
                  .map(MonsterMecPositioningInMainShelf::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterCokeCoolerRatioWithMec条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterCokeCoolerRatioWithMec(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterCokeCoolerRatioWithMec> cokeCoolerRatioWithMecList = new ArrayList<>();
    LambdaQueryWrapper<MonsterCokeCoolerRatioWithMec>
        storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(MonsterCokeCoolerRatioWithMec::getPeriod);
    cokeCoolerRatioWithMecList =
        monsterCokeCoolerRatioWithMecMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(cokeCoolerRatioWithMecList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              cokeCoolerRatioWithMecList.stream()
                  .map(MonsterCokeCoolerRatioWithMec::getPeriod)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(MonsterCokeCoolerRatioWithMec::getBg);
    cokeCoolerRatioWithMecList.clear();
    cokeCoolerRatioWithMecList =
        monsterCokeCoolerRatioWithMecMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(cokeCoolerRatioWithMecList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              cokeCoolerRatioWithMecList.stream()
                  .map(MonsterCokeCoolerRatioWithMec::getBg)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(MonsterCokeCoolerRatioWithMec::getCity);
    cokeCoolerRatioWithMecList.clear();
    cokeCoolerRatioWithMecList =
        monsterCokeCoolerRatioWithMecMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(cokeCoolerRatioWithMecList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              cokeCoolerRatioWithMecList.stream()
                  .map(MonsterCokeCoolerRatioWithMec::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterMecPositioningInCustomerCooler条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterMecPositioningInCustomerCooler(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterMecPositioningInCustomerCooler> mecPositioningInCustomerCoolerList =
        new ArrayList<>();
    LambdaQueryWrapper<MonsterMecPositioningInCustomerCooler>
        storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterMecPositioningInCustomerCooler::getPeriod);
    mecPositioningInCustomerCoolerList =
        monsterMecPositioningInCustomerCoolerMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mecPositioningInCustomerCoolerList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              mecPositioningInCustomerCoolerList.stream()
                  .map(MonsterMecPositioningInCustomerCooler::getPeriod)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterMecPositioningInCustomerCooler::getBg);
    mecPositioningInCustomerCoolerList.clear();
    mecPositioningInCustomerCoolerList =
        monsterMecPositioningInCustomerCoolerMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mecPositioningInCustomerCoolerList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              mecPositioningInCustomerCoolerList.stream()
                  .map(MonsterMecPositioningInCustomerCooler::getBg)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterMecPositioningInCustomerCooler::getCity);
    mecPositioningInCustomerCoolerList.clear();
    mecPositioningInCustomerCoolerList =
        monsterMecPositioningInCustomerCoolerMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mecPositioningInCustomerCoolerList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              mecPositioningInCustomerCoolerList.stream()
                  .map(MonsterMecPositioningInCustomerCooler::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterMecPositioningInCustomerCooler条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterSuctionRackAvailability(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterSuctionRackAvailability> suctionRackAvailabilityList = new ArrayList<>();
    LambdaQueryWrapper<MonsterSuctionRackAvailability>
        storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(
        MonsterSuctionRackAvailability::getPeriod);
    suctionRackAvailabilityList =
        monsterSuctionRackAvailabilityMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(suctionRackAvailabilityList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              suctionRackAvailabilityList.stream()
                  .map(MonsterSuctionRackAvailability::getPeriod)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(MonsterSuctionRackAvailability::getBg);
    suctionRackAvailabilityList.clear();
    suctionRackAvailabilityList =
        monsterSuctionRackAvailabilityMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(suctionRackAvailabilityList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              suctionRackAvailabilityList.stream()
                  .map(MonsterSuctionRackAvailability::getBg)
                  .collect(Collectors.toList()));
    }

    storeRatioByMecFlavorNumberLambdaQueryWrapper = new LambdaQueryWrapper<>();
    storeRatioByMecFlavorNumberLambdaQueryWrapper.groupBy(MonsterSuctionRackAvailability::getCity);
    suctionRackAvailabilityList.clear();
    suctionRackAvailabilityList =
        monsterSuctionRackAvailabilityMapper.selectList(
            storeRatioByMecFlavorNumberLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(suctionRackAvailabilityList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              suctionRackAvailabilityList.stream()
                  .map(MonsterSuctionRackAvailability::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterPriceParityWithRbg条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterPriceParityWithRbg(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterPriceParityWithRbg> priceParityWithRbgList = new ArrayList<>();
    LambdaQueryWrapper<MonsterPriceParityWithRbg> priceParityWithRbgLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    priceParityWithRbgLambdaQueryWrapper.groupBy(MonsterPriceParityWithRbg::getPeriod);
    priceParityWithRbgList =
        monsterPriceParityWithRbgMapper.selectList(priceParityWithRbgLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(priceParityWithRbgList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              priceParityWithRbgList.stream()
                  .map(MonsterPriceParityWithRbg::getPeriod)
                  .collect(Collectors.toList()));
    }

    priceParityWithRbgLambdaQueryWrapper = new LambdaQueryWrapper<>();
    priceParityWithRbgLambdaQueryWrapper.groupBy(MonsterPriceParityWithRbg::getBg);
    priceParityWithRbgList.clear();
    priceParityWithRbgList =
        monsterPriceParityWithRbgMapper.selectList(priceParityWithRbgLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(priceParityWithRbgList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              priceParityWithRbgList.stream()
                  .map(MonsterPriceParityWithRbg::getBg)
                  .collect(Collectors.toList()));
    }

    priceParityWithRbgLambdaQueryWrapper = new LambdaQueryWrapper<>();
    priceParityWithRbgLambdaQueryWrapper.groupBy(MonsterPriceParityWithRbg::getCity);
    priceParityWithRbgList.clear();
    priceParityWithRbgList =
        monsterPriceParityWithRbgMapper.selectList(priceParityWithRbgLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(priceParityWithRbgList)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              priceParityWithRbgList.stream()
                  .map(MonsterPriceParityWithRbg::getCity)
                  .collect(Collectors.toList()));
    }

    return queryConditionsDTO;
  }

  /**
   * 获取MonsterPosmAvailability条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterPosmAvailability(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterPosmAvailability> posmAvailabilityList = new ArrayList<>();
    LambdaQueryWrapper<MonsterPosmAvailability> posmAvailabilityLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    posmAvailabilityLambdaQueryWrapper.groupBy(MonsterPosmAvailability::getPeriod);
    posmAvailabilityList =
        monsterPosmAvailabilityMapper.selectList(posmAvailabilityLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(posmAvailabilityList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              posmAvailabilityList.stream()
                  .map(MonsterPosmAvailability::getPeriod)
                  .collect(Collectors.toList()));
    }

    posmAvailabilityLambdaQueryWrapper = new LambdaQueryWrapper<>();
    posmAvailabilityLambdaQueryWrapper.groupBy(MonsterPosmAvailability::getBg);
    posmAvailabilityList.clear();
    posmAvailabilityList =
        monsterPosmAvailabilityMapper.selectList(posmAvailabilityLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(posmAvailabilityList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              posmAvailabilityList.stream()
                  .map(MonsterPosmAvailability::getBg)
                  .collect(Collectors.toList()));
    }
    return queryConditionsDTO;
  }

  /**
   * 获取Mec2ndDisplay条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterMec2ndDisplay(
      QueryConditionsDTO queryConditionsDTO) {

    List<MonsterMec2ndDisplay> mec2ndDisplayList = new ArrayList<>();
    LambdaQueryWrapper<MonsterMec2ndDisplay> mec2ndDisplayLambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    mec2ndDisplayLambdaQueryWrapper.groupBy(MonsterMec2ndDisplay::getPeriod);
    mec2ndDisplayList = monsterMec2ndDisplayMapper.selectList(mec2ndDisplayLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mec2ndDisplayList)) {
      queryConditionsDTO
          .getPeriodList()
          .addAll(
              mec2ndDisplayList.stream()
                  .map(MonsterMec2ndDisplay::getPeriod)
                  .collect(Collectors.toList()));
    }

    mec2ndDisplayLambdaQueryWrapper = new LambdaQueryWrapper<>();
    mec2ndDisplayLambdaQueryWrapper.groupBy(MonsterMec2ndDisplay::getBg);
    mec2ndDisplayList.clear();
    mec2ndDisplayList = monsterMec2ndDisplayMapper.selectList(mec2ndDisplayLambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(mec2ndDisplayList)) {
      queryConditionsDTO
          .getBgList()
          .addAll(
              mec2ndDisplayList.stream()
                  .map(MonsterMec2ndDisplay::getBg)
                  .collect(Collectors.toList()));
    }
    return queryConditionsDTO;
  }

  /**
   * 获取DealerData1条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterDealerData1(
      QueryConditionsDTO queryConditionsDTO, String retailer) {

    List<MonsterDealerData1> dealerData1List = new ArrayList<>();
    LambdaQueryWrapper<MonsterDealerData1> dealerData1LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData1LambdaQueryWrapper.groupBy(MonsterDealerData1::getRetailer);
    dealerData1List = monsterDealerData1Mapper.selectList(dealerData1LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData1List)) {
      queryConditionsDTO
          .getRetailerList()
          .addAll(
              dealerData1List.stream()
                  .map(MonsterDealerData1::getRetailer)
                  .collect(Collectors.toList()));
    }

    dealerData1LambdaQueryWrapper = new LambdaQueryWrapper<>();
    if (StringUtils.isNotBlank(retailer)) {
      dealerData1LambdaQueryWrapper.eq(MonsterDealerData1::getRetailer, retailer);
    }
    dealerData1LambdaQueryWrapper.groupBy(MonsterDealerData1::getCity);
    dealerData1List.clear();
    dealerData1List = monsterDealerData1Mapper.selectList(dealerData1LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData1List)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              dealerData1List.stream()
                  .map(MonsterDealerData1::getCity)
                  .collect(Collectors.toList()));
    }
    return queryConditionsDTO;
  }

  /**
   * 获取DealerData2条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterDealerData2(
      QueryConditionsDTO queryConditionsDTO, String retailer) {

    List<MonsterDealerData2> dealerData2List = new ArrayList<>();
    LambdaQueryWrapper<MonsterDealerData2> dealerData2LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData2LambdaQueryWrapper.groupBy(MonsterDealerData2::getRetailer);
    dealerData2List = monsterDealerData2Mapper.selectList(dealerData2LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData2List)) {
      queryConditionsDTO
          .getRetailerList()
          .addAll(
              dealerData2List.stream()
                  .map(MonsterDealerData2::getRetailer)
                  .collect(Collectors.toList()));
    }

    dealerData2LambdaQueryWrapper = new LambdaQueryWrapper<>();
    if (StringUtils.isNotBlank(retailer)) {
      dealerData2LambdaQueryWrapper.eq(MonsterDealerData2::getRetailer, retailer);
    }
    dealerData2LambdaQueryWrapper.groupBy(MonsterDealerData2::getCity);
    dealerData2List.clear();
    dealerData2List = monsterDealerData2Mapper.selectList(dealerData2LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData2List)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              dealerData2List.stream()
                  .map(MonsterDealerData2::getCity)
                  .collect(Collectors.toList()));
    }
    return queryConditionsDTO;
  }

  /**
   * 获取DealerData3条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterDealerData3(
      QueryConditionsDTO queryConditionsDTO, String retailer) {

    List<MonsterDealerData3> dealerData3List = new ArrayList<>();
    LambdaQueryWrapper<MonsterDealerData3> dealerData3LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData3LambdaQueryWrapper.groupBy(MonsterDealerData3::getRetailer);
    dealerData3List = monsterDealerData3Mapper.selectList(dealerData3LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData3List)) {
      queryConditionsDTO
          .getRetailerList()
          .addAll(
              dealerData3List.stream()
                  .map(MonsterDealerData3::getRetailer)
                  .collect(Collectors.toList()));
    }

    dealerData3LambdaQueryWrapper = new LambdaQueryWrapper<>();
    if (StringUtils.isNotBlank(retailer)) {
      dealerData3LambdaQueryWrapper.eq(MonsterDealerData3::getRetailer, retailer);
    }
    dealerData3LambdaQueryWrapper.groupBy(MonsterDealerData3::getCity);
    dealerData3List.clear();
    dealerData3List = monsterDealerData3Mapper.selectList(dealerData3LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData3List)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              dealerData3List.stream()
                  .map(MonsterDealerData3::getCity)
                  .collect(Collectors.toList()));
    }
    return queryConditionsDTO;
  }

  /**
   * 获取DealerData4条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterDealerData4(
      QueryConditionsDTO queryConditionsDTO, String retailer) {

    List<MonsterDealerData4> dealerData4List = new ArrayList<>();
    LambdaQueryWrapper<MonsterDealerData4> dealerData4LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData4LambdaQueryWrapper.groupBy(MonsterDealerData4::getRetailer);
    dealerData4List = monsterDealerData4Mapper.selectList(dealerData4LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData4List)) {
      queryConditionsDTO
          .getRetailerList()
          .addAll(
              dealerData4List.stream()
                  .map(MonsterDealerData4::getRetailer)
                  .collect(Collectors.toList()));
    }

    dealerData4LambdaQueryWrapper = new LambdaQueryWrapper<>();
    if (StringUtils.isNotBlank(retailer)) {
      dealerData4LambdaQueryWrapper.eq(MonsterDealerData4::getRetailer, retailer);
    }
    dealerData4LambdaQueryWrapper.groupBy(MonsterDealerData4::getCity);
    dealerData4List.clear();
    dealerData4List = monsterDealerData4Mapper.selectList(dealerData4LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData4List)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              dealerData4List.stream()
                  .map(MonsterDealerData4::getCity)
                  .collect(Collectors.toList()));
    }
    return queryConditionsDTO;
  }

  /**
   * 获取DealerData5条件
   *
   * @param queryConditionsDTO
   * @return
   */
  public QueryConditionsDTO getQueryConditionsMonsterDealerData5(
      QueryConditionsDTO queryConditionsDTO, String retailer) {

    List<MonsterDealerData5> dealerData5List = new ArrayList<>();
    LambdaQueryWrapper<MonsterDealerData5> dealerData5LambdaQueryWrapper =
        new LambdaQueryWrapper<>();
    dealerData5LambdaQueryWrapper.groupBy(MonsterDealerData5::getRetailer);
    dealerData5List = monsterDealerData5Mapper.selectList(dealerData5LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData5List)) {
      queryConditionsDTO
          .getRetailerList()
          .addAll(
              dealerData5List.stream()
                  .map(MonsterDealerData5::getRetailer)
                  .collect(Collectors.toList()));
    }

    dealerData5LambdaQueryWrapper = new LambdaQueryWrapper<>();
    if (StringUtils.isNotBlank(retailer)) {
      dealerData5LambdaQueryWrapper.eq(MonsterDealerData5::getRetailer, retailer);
    }
    dealerData5LambdaQueryWrapper.groupBy(MonsterDealerData5::getCity);
    dealerData5List.clear();
    dealerData5List = monsterDealerData5Mapper.selectList(dealerData5LambdaQueryWrapper);
    if (CollectionUtils.isNotEmpty(dealerData5List)) {
      queryConditionsDTO
          .getCityList()
          .addAll(
              dealerData5List.stream()
                  .map(MonsterDealerData5::getCity)
                  .collect(Collectors.toList()));
    }
    return queryConditionsDTO;
  }
}
