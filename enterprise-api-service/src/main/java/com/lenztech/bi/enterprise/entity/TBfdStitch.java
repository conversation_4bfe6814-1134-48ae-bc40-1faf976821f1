package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TBfdStitch implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String responseId;

    /**
     * 图片组ID
     */
    private String groupId;

    /**
     * 拼接原始图片id集合
     */
    private String sourceImageIdList;

    /**
     * 拼接结果效果图url
     */
    private String stitchImageUrl;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
