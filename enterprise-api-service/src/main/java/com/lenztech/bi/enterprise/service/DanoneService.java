package com.lenztech.bi.enterprise.service;

import com.alibaba.cloudapi.sdk.constant.HttpConstant;
import com.alibaba.cloudapi.sdk.constant.SdkConstant;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.lenztech.bi.enterprise.dto.pg.FeiShuRequest;
import com.lenztech.bi.enterprise.entity.*;
import com.lenztech.bi.enterprise.mapper.DanengPushLogMapper;
import com.lenztech.bi.enterprise.service.impl.DanengFreezerResultServiceImpl;
import com.lenztech.bi.enterprise.service.impl.DanengInspectResultServiceImpl;
import com.lenztech.bi.enterprise.service.impl.DanengStep2ServiceImpl;

import com.lenztech.bi.enterprise.utils.*;
import com.trax.lenz.common.annotation.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Service
public class DanoneService {
//    dev
//    private static final String API_URL = "https://danone-compass-nacos-test.winsfa.com/api/interfaces/mdmSfaApi/saveInspectData";
//    private static final String APP_SECRET_COMPASS = "2GvedYdtTL3C^LsYTjyo";
//    uat
//    private static final String API_URL = "https://ali-api-dev.danonewaters.com.cn/compass/mdmsfaapi/saveinspectdata";
//    private static final String APP_SECRET_COMPASS = "2GvedYdtTL3C^LsYTjyo";
//    private static final String APP_KEY = "204841512";
//    private static final String APP_SECRET = "rzXSdjjvl3movcTFaKWURtWJRa9JbJ1b";
//    private static final String APP_KEY = "204096046";
//    private static final String APP_SECRET = "gUzfxqpRD1JYUvpsY58bJHMoZlkR3h5V";
//    prod
    private static final String API_URL = "https://ali-api-prd.danonewaters.com.cn/compass/mdmsfaapi/saveinspectdata";
    private static final String APP_SECRET_COMPASS = "QjkcwlGtYQGclnX^HtlM";
    private static final String APP_KEY = "204841511";
    private static final String APP_SECRET = "NSHcKgETnl2Wb7u5tpjcGaryDmWPrtcG";
    private static final int HTTP_TIMEOUT = 30000; // 30秒超时
    private static final int BATCH_SIZE = 500; // 批量保存大小

    @Autowired
    private DanengStep2ServiceImpl danengStep2Service;

    @Autowired
    private DanengInspectResultServiceImpl danengInspectResultService;

    @Autowired
    private DanengFreezerResultServiceImpl danengFreezerResultService;
    
    @Autowired
    private DanengPushLogMapper danengPushLogMapper;

    //达能推送告警
    public static final String FEISHU_NOTICE_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/381882c1-43ce-451c-90ac-69fd6c79e72c";

    /**
     * 处理数据并调用第三方接口
     */
    public void processAndSendData(String searchDate, String addressIDnum) {
        try {
            // 构建查询条件并查询数据
            List<DanengStep2> step2List = queryDanengStep2Data(searchDate, addressIDnum);
            if (step2List.isEmpty()) {
                log.info("未查询到符合条件的数据");
                return;
            }

            //HTTPS Client init
            HttpClientBuilderParams httpsParam = new HttpClientBuilderParams();
            httpsParam.setAppKey(APP_KEY);
            httpsParam.setAppSecret(APP_SECRET);

            /**
             * HTTPS request use DO_NOT_VERIFY mode only for demo
             * Suggest verify for security
             */
            //httpsParam.setRegistry(getNoVerifyRegistry());

            HttpsApiClientenv_uat.getInstance().init(httpsParam);

            // 处理查询到的数据并发送请求
            List<DanengPushLog> pushLogList = processDataAndSendRequests(step2List);

            // 保存推送日志
            savePushLogs(pushLogList);

        } catch (Exception e) {
            log.error("处理数据时发生错误", e);
            throw new RuntimeException("处理数据失败", e);
        }
    }

    /**
     * 查询丹能Step2数据
     */
    private List<DanengStep2> queryDanengStep2Data(String searchDate, String addressIDnum) {
        // 设置时间范围（前一天晚21:00到当前天的晚21:00）
        DateTime now = new DateTime();
        DateTime yesterday = now.minusDays(1);

        String startTime = yesterday.toString("yyyy-MM-dd") + " 21:00:00";
        String endTime = now.toString("yyyy-MM-dd") + " 21:00:00";

        log.info("查询时间范围: {} 至 {}", startTime, endTime);

        // 构建查询条件
        LambdaQueryWrapper<DanengStep2> queryWrapper = buildQueryWrapper(searchDate, addressIDnum, startTime, endTime);

        // 查询数据
        List<DanengStep2> step2List = danengStep2Service.list(queryWrapper);
        log.info("查询到符合条件的数据: {} 条", step2List.size());

        return step2List;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<DanengStep2> buildQueryWrapper(String searchDate, String addressIDnum, String startTime, String endTime) {
        LambdaQueryWrapper<DanengStep2> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtil.isNotBlank(searchDate)){
            queryWrapper.eq(DanengStep2::getExecDate, searchDate);
        }

        if (StringUtil.isNotBlank(addressIDnum)){
            queryWrapper.eq(DanengStep2::getStoreCode, addressIDnum);
        }

        if (StringUtils.isBlank(searchDate) && StringUtils.isBlank(addressIDnum)){
            queryWrapper.ge(DanengStep2::getCreateTime, startTime);
            queryWrapper.lt(DanengStep2::getCreateTime, endTime);
        }

        return queryWrapper;
    }

    /**
     * 处理数据并发送请求
     */
    private List<DanengPushLog> processDataAndSendRequests(List<DanengStep2> step2List) {
        List<DanengPushLog> pushLogList = new ArrayList<>();

        for (DanengStep2 step2 : step2List) {
            try {
                // 处理单条数据并发送请求
                DanengPushLog pushLog = processSingleDataAndSendRequest(step2);
                pushLogList.add(pushLog);

                // 批量保存日志
                if (pushLogList.size() >= BATCH_SIZE) {
                    savePushLogs(pushLogList);
                    pushLogList = new ArrayList<>();
                }
            } catch (Exception e) {
                log.error("处理单条数据失败 - ID: {}", step2.getId(), e);
                // 继续处理下一条数据
            }
        }

        return pushLogList;
    }

    /**
     * 处理单条数据并发送请求
     */
    private DanengPushLog processSingleDataAndSendRequest(DanengStep2 step2) {
        // 1. 获取关联数据
        List<DanengInspectResult> inspectResults = getInspectResults(step2);
        List<DanengFreezerResult> freezerResults = getFreezerResults(step2);

        // 2. 构建请求数据
        String requestBody = buildRequestBody(step2, inspectResults, freezerResults);

        // 3. 准备认证信息
        String nonce = generateNonce();
        String curTime = String.valueOf(new DateTime().getMillis() / 1000);
        String checkSum = CheckDigestTool.getCheckSum(APP_SECRET_COMPASS, nonce, curTime);

        // 4. 创建日志对象
        DanengPushLog pushLog = createPushLog(step2, requestBody, nonce);

        // 5. 发送请求
        sendRequestAndUpdateLog(pushLog, requestBody, nonce, curTime, checkSum, step2.getId());

        // 6.推送失败告警
        if (Objects.nonNull(pushLog) && Objects.equals(pushLog.getStatus(), 0)){
            String errorMessage = "rid = " + pushLog.getResponseId() + ", 报错: " + pushLog.getErrorMessage();
            sendFeiShuNotice(errorMessage);
        }

        return pushLog;
    }

    /**
     * 获取检查结果数据
     */
    private List<DanengInspectResult> getInspectResults(DanengStep2 step2) {
        return danengInspectResultService.list(
            new LambdaQueryWrapper<DanengInspectResult>()
                .eq(DanengInspectResult::getResponseId, step2.getResponseId()).eq(DanengInspectResult::getExecDate, step2.getExecDate())
        );
    }

    /**
     * 获取冰柜检查数据
     */
    private List<DanengFreezerResult> getFreezerResults(DanengStep2 step2) {
        return danengFreezerResultService.list(
            new LambdaQueryWrapper<DanengFreezerResult>()
                .eq(DanengFreezerResult::getResponseId, step2.getResponseId()).eq(DanengFreezerResult::getExecDate, step2.getExecDate())
        );
    }

    /**
     * 创建推送日志对象
     */
    private DanengPushLog createPushLog(DanengStep2 step2, String requestBody, String nonce) {
        DanengPushLog pushLog = new DanengPushLog();
        pushLog.setStoreCode(step2.getStoreCode());
        pushLog.setResponseId(step2.getResponseId());
        pushLog.setRequestBody(requestBody);
        pushLog.setNonce(nonce);
        pushLog.setCustomerType(step2.getCustomerType());
        pushLog.setMonth(step2.getMonth());
        if (step2.getInspectDate() != null) {
            pushLog.setInspectDate(formatDate(step2.getInspectDate()));
        }
        pushLog.setPushTime(new Date());
        pushLog.setCreateTime(new Date());
        return pushLog;
    }

    /**
     * 发送请求并更新日志
     */
    private void sendRequestAndUpdateLog(DanengPushLog pushLog, String requestBody, String nonce, String curTime, String checkSum, Long stepId) {
        Map<String, List<String>> queryParams = new HashMap<String, List<String>>();
        Map<String, List<String>> headerParams = new HashMap<String, List<String>>();

        headerParams.put("Nonce", Lists.newArrayList(nonce));
        headerParams.put("CurTime", Lists.newArrayList(curTime));
        headerParams.put("CheckSum", Lists.newArrayList(checkSum));

        long startTime = System.currentTimeMillis();
        String response = null;
        int statusCode = 0;
        log.info("请求体: {}", requestBody);
        log.info("请求头: {}", headerParams);
        try {
            // 使用SDK方案发送请求获取响应
            ApiResponse apiResponse = HttpsApiClientenv_uat.getInstance().compass_saveinspectdataSyncMode(
                queryParams,
                headerParams,
                    requestBody.getBytes(SdkConstant.CLOUDAPI_ENCODING)
            );

            // 获取HTTP状态码
            statusCode = apiResponse.getCode();

            // 获取响应内容
            response = new String(apiResponse.getBody(), SdkConstant.CLOUDAPI_ENCODING);

            // 记录日志
            log.info("请求URL: {}", API_URL);



            try {
                log.info("响应状态码: {}", statusCode);
                log.info("响应体: {}", response);
                //log.info("HttpsApiClientenv_uat.getInstance().compass_saveinspectdataSyncMode response =: {}", CheckDigestTool.getResultString(apiResponse));
            } catch (Exception ex) {
                log.error("解析SDK响应日志失败", ex);
            }

            // 设置日志属性
            pushLog.setResponseBody(response);
            pushLog.setStatusCode(statusCode);

            // 判断是否成功：HTTP状态码为200并且返回报文中code为"0"才算成功
            boolean isSuccess = false;
            if (statusCode == HttpStatus.SC_OK && StringUtils.isNotBlank(response)) {
                try {
                    // 解析JSON响应
                    Gson gson = new Gson();
                    Map<String, Object> responseMap = gson.fromJson(response, Map.class);
                    // 检查code字段是否为"0"
                    if (responseMap != null && "0".equals(responseMap.get("code"))) {
                        isSuccess = true;
                    }
                } catch (Exception e) {
                    log.error("解析响应JSON失败", e);
                }
            }

            pushLog.setStatus(isSuccess ? 1 : 0);

        } catch (Exception e) {
            // 设置错误信息
            pushLog.setStatus(0);
            pushLog.setErrorMessage(e.getMessage());
            log.error("发送请求失败", e);
        } finally {
            // 计算响应时间
            long endTime = System.currentTimeMillis();
            pushLog.setResponseTime(endTime - startTime);
        }

        log.info("数据发送结果 - ID: {}, Response: {}, 是否成功: {}", stepId, response, pushLog.getStatus() == 1 ? "是" : "否");

//        long startTime = System.currentTimeMillis();
//        String response = null;
//        int statusCode = 0;
//
//        try {
//            ResponseResult result = sendRequest(requestBody, nonce, curTime, checkSum);
//            response = result.getResponse();
//            statusCode = result.getStatusCode();
//
//            // 设置日志属性
//            pushLog.setResponseBody(response);
//            pushLog.setStatusCode(statusCode);
//
//            // 判断是否成功：HTTP状态码为200并且返回报文中code为"0"才算成功
//            boolean isSuccess = false;
//            if (statusCode == HttpStatus.SC_OK && StringUtils.isNotBlank(response)) {
//                try {
//                    // 解析JSON响应
//                    Gson gson = new Gson();
//                    Map<String, Object> responseMap = gson.fromJson(response, Map.class);
//                    // 检查code字段是否为"0"
//                    if (responseMap != null && "0".equals(responseMap.get("code"))) {
//                        isSuccess = true;
//                    }
//                } catch (Exception e) {
//                    log.error("解析响应JSON失败", e);
//                }
//            }
//
//            pushLog.setStatus(isSuccess ? 1 : 0);
//
//        } catch (Exception e) {
//            // 设置错误信息
//            pushLog.setStatus(0);
//            pushLog.setErrorMessage(e.getMessage());
//            throw e;
//        } finally {
//            // 计算响应时间
//            long endTime = System.currentTimeMillis();
//            pushLog.setResponseTime(endTime - startTime);
//        }
//
//        log.info("数据发送结果 - ID: {}, Response: {}, 是否成功: {}", stepId, response, pushLog.getStatus() == 1 ? "是" : "否");
    }

    /**
     * 批量保存推送日志（参考PgDpbReportService的实现）
     */
    private void savePushLogs(List<DanengPushLog> logList) {
        if (logList == null || logList.isEmpty()) {
            return;
        }
        
        try {
            // 使用PgDpbReportService相同的分批处理方式
            int batchSize = BATCH_SIZE;
            for (int i = 0; i < logList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, logList.size());
                List<DanengPushLog> subList = logList.subList(i, end);
                danengPushLogMapper.insertBatch(subList);
            }
            log.info("批量保存日志成功，数量: {}", logList.size());
        } catch (Exception e) {
            log.error("批量插入日志失败", e);
            // 打印具体错误信息，便于调试
            log.error("错误详情: ", e);
        }
    }

    /**
     * 构建请求体
     */
    private String buildRequestBody(DanengStep2 step2, List<DanengInspectResult> inspectResults, List<DanengFreezerResult> freezerResults) {
        // 构建检查结果列表
        List<InspectResult> inspectResultList = new ArrayList<>();
        for (DanengInspectResult result : inspectResults) {
            inspectResultList.add(new InspectResult(result.getCheckCode(), result.getCheckValue()));
        }

        // 构建冰柜检查数据列表
        List<FreezerInspect> freezerInspectList = new ArrayList<>();
        for (DanengFreezerResult result : freezerResults) {
            freezerInspectList.add(new FreezerInspect(result.getAssetCode(), result.getLayers(), result.getRemarks()));
        }

        // 格式化日期和时间
        String formattedDate = formatDate(step2.getInspectDate());
        String formattedTime = formatTime(step2.getInspectTime());

        // 构建主请求对象
        InspectRequest request = new InspectRequest();
        request.setRequestId(CheckDigestTool.getUUID());
        request.setType(step2.getType() != null ? step2.getType() : 0);
        request.setInspectAddress(safeString(step2.getInspectAddress()));
        request.setInspectDate(formattedDate+ " " + formattedTime);
        request.setInspectPhone(safeString(step2.getInspectPhone()));
        request.setCustomerType(safeString(step2.getCustomerType()));
        request.setFreezerNumber(safeString(step2.getFreezerNumber()));
        request.setInspectResultList(inspectResultList);
        request.setFreezerInspectList(freezerInspectList);
        request.setInspectStoreName(safeString(step2.getInspectStoreName()));
        request.setInspectStoreResult(safeString(step2.getInspectStoreResult()));
        request.setInspectTime(formattedTime);
        request.setLink(safeString(step2.getLink()));
        request.setRemarks(safeString(step2.getRemarks()));
        request.setStoreCode(safeString(step2.getStoreCode()));
        request.setWordOrderId(safeString(step2.getResponseId()));
        request.setYearMonths(safeString(step2.getMonth()));

        // 转换为JSON
        Gson gson = new Gson();
        return gson.toJson(Arrays.asList(request));
    }

    /**
     * 安全获取字符串，如果输入为null则返回空字符串
     */
    private String safeString(String str) {
        return str != null ? str : "";
    }

    /**
     * 格式化日期为 yyyy-MM-dd 格式
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return new DateTime(date).toString("yyyy-MM-dd");
    }

    /**
     * 格式化时间为 HH:mm:ss 格式
     */
    private String formatTime(Date time) {
        if (time == null) {
            return "";
        }
        return new DateTime(time).toString("HH:mm:ss");
    }

    /**
     * 生成随机数
     */
    private String generateNonce() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            sb.append(Integer.toHexString(random.nextInt(16)));
        }
        return sb.toString();
    }

    /**
     * HTTP响应结果类
     */
    private static class ResponseResult {
        private int statusCode;
        private String response;

        public int getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(int statusCode) {
            this.statusCode = statusCode;
        }

        public String getResponse() {
            return response;
        }

        public void setResponse(String response) {
            this.response = response;
        }
    }

    public void sendFeiShuNotice(String content){
        FeiShuRequest feiShuRequest = new FeiShuRequest();
        feiShuRequest.setMsg_type("text");
        FeiShuRequest.ContentBean contentBean = new FeiShuRequest.ContentBean();
        contentBean.setText(content);
        feiShuRequest.setContent(contentBean);
        String resultJson = JsonUtil.toJsonString(feiShuRequest);
        try {
            String callbackResult = HttpConnectionUtils.postRequest(FEISHU_NOTICE_URL, resultJson, 30000);
            System.out.println(callbackResult);
        } catch (Exception e) {

        }
    }
}