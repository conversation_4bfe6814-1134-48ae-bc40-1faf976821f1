package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.libyV2.RecognizeDetailDTO;
import com.lenztech.bi.enterprise.dto.libyV2.RecognizeDetailResp;
import com.lenztech.bi.enterprise.entity.LibaiDetailInfo;
import com.lenztech.bi.enterprise.entity.LibaiImageInfo;
import com.lenztech.bi.enterprise.mapper.LiByV2DetailInfoMapper;
import com.lenztech.bi.enterprise.mapper.LiByV2ImageInfoMapper;
import com.lenztech.bi.enterprise.service.LiByV2ReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * (LibaiDetailInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-27 11:58:05
 */
@Service
@Slf4j
public class LiByDetailInfoServiceImpl implements LiByV2ReportService {

    @Autowired
    private LiByV2ImageInfoMapper liByV2ImageInfoMapper;

    @Autowired
    private LiByV2DetailInfoMapper liByV2DetailInfoMapper;

    @Override
    public RecognizeDetailResp getBiTargetList(String responseId) {
        RecognizeDetailResp recognizeDetailResp = new RecognizeDetailResp();
        List<RecognizeDetailDTO> imageResultList = new ArrayList<>();
        recognizeDetailResp.setResponseId(responseId);

        // 查询所有图片信息
        LambdaQueryWrapper<LibaiImageInfo> imageWrapper = new LambdaQueryWrapper<>();
        imageWrapper.eq(LibaiImageInfo::getResponseId, responseId);
        List<LibaiImageInfo> imageList = liByV2ImageInfoMapper.selectList(imageWrapper);

        if (CollectionUtils.isEmpty(imageList)) {
            log.info("【立白项目查询BI识别结果图片集为空!】, responseId:{}", responseId);
            recognizeDetailResp.setImageNum(0);
            recognizeDetailResp.setDetailDTOList(imageResultList);
            return recognizeDetailResp;
        }

        // 查询识别出所有SKU信息
        LambdaQueryWrapper<LibaiDetailInfo> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(LibaiDetailInfo::getResponseId, responseId);
        productWrapper.orderByDesc(LibaiDetailInfo::getFacingCount);
        List<LibaiDetailInfo> productList = liByV2DetailInfoMapper.selectList(productWrapper);

        if (CollectionUtils.isEmpty(productList)) {
            log.info("【立白项目查询BI识别结果集为空!】, responseId:{}", responseId);
            recognizeDetailResp.setImageNum(imageList.size());
            recognizeDetailResp.setDetailDTOList(imageResultList);
            return recognizeDetailResp;
        }

        // 构建识别明细
        recognizeDetailResp.setImageNum(imageList.size());
        for (LibaiDetailInfo libaiDetailInfo : productList) {
            RecognizeDetailDTO productDTO = new RecognizeDetailDTO();
            productDTO.setProductName(libaiDetailInfo.getProductName());
            productDTO.setDistributeStatus(libaiDetailInfo.getDistributeStatus());
            productDTO.setFacingCount(libaiDetailInfo.getFacingCount());
            imageResultList.add(productDTO);
        }
        recognizeDetailResp.setDetailDTOList(imageResultList);

        return recognizeDetailResp;
    }
}