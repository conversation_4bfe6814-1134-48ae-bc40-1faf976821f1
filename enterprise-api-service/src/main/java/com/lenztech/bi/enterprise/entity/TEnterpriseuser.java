package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * （企业用户）用于存储企业用户的相关信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-17
 */
public class TEnterpriseuser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @TableId("Id")
    private String Id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 公司名称
     */
    private String company;

    private String companyStatus;

    /**
     * 注册时间
     */
    @TableField("registTime")
    private LocalDateTime registTime;

    private String phone;

    private String phonedStatus;

    /**
     * 是否开启推送功能（0 不开起 1开启）
     */
    @TableField("IsPush")
    private String IsPush;

    /**
     * 企业会员信誉度
     */
    private Integer credit;

    /**
     * 是否允许删除 0不允许 1允许
     */
    private String isdel;

    /**
     * 总充值金额
     */
    private Double ppzcz;

    /**
     * 总消费金额
     */
    private Double zxfje;

    /**
     * 预支付金额
     */
    private Double yzfje;

    /**
     * 已消费金额
     */
    private Double yxfje;

    /**
     * 活动金额
     */
    private Double hdje;

    /**
     * 已建任务数
     */
    private Integer yjrws;

    /**
     * 任务竞价金额
     */
    private Double rwjj;

    private String temp1;

    private String temp2;

    /**
     * 是否允许查看会员统计 0不允许 1允许
     */
    private String ishytj;

    /**
     * 权限设置 0不允许 1允许
     */
    private String qxsz;

    /**
     * 是否有链接题 0不允许 1允许
     */
    private String linksz;

    /**
     * 选择题分设置 0不允许 1允许
     */
    private String optionfen;

    /**
     * 允许有运营活动 0:没有 1:有
     */
    private String ifactivity;

    /**
     * 是否允许企业会员使用后台功能 0：不允许 1：允许
     */
    private String ifagree;

    private String ifbi;

    /**
     * 是否为兼职人员 1是 0不是
     */
    private String partTimeWorker;

    /**
     * 兼职人员有没有编辑地址库权限 0没有  1有
     */
    private String operateAddressPool;

    /**
     * 是否有导出地址库的权限 0没有 1有
     */
    private String exportAddressPool;

    /**
     * 公司ID,与id相同，则为公司账号
     */
    @TableField("mainId")
    private String mainId;

    /**
     * 是否可以添加子账号 0不可以1可以
     */
    private Integer manageAccountPermission;

    /**
     * 关联BI系统公司id
     */
    private Integer companyId;

    public String getId() {
        return Id;
    }

    public void setId(String Id) {
        this.Id = Id;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }
    public String getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(String companyStatus) {
        this.companyStatus = companyStatus;
    }
    public LocalDateTime getRegistTime() {
        return registTime;
    }

    public void setRegistTime(LocalDateTime registTime) {
        this.registTime = registTime;
    }
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getPhonedStatus() {
        return phonedStatus;
    }

    public void setPhonedStatus(String phonedStatus) {
        this.phonedStatus = phonedStatus;
    }
    public String getIsPush() {
        return IsPush;
    }

    public void setIsPush(String IsPush) {
        this.IsPush = IsPush;
    }
    public Integer getCredit() {
        return credit;
    }

    public void setCredit(Integer credit) {
        this.credit = credit;
    }
    public String getIsdel() {
        return isdel;
    }

    public void setIsdel(String isdel) {
        this.isdel = isdel;
    }
    public Double getPpzcz() {
        return ppzcz;
    }

    public void setPpzcz(Double ppzcz) {
        this.ppzcz = ppzcz;
    }
    public Double getZxfje() {
        return zxfje;
    }

    public void setZxfje(Double zxfje) {
        this.zxfje = zxfje;
    }
    public Double getYzfje() {
        return yzfje;
    }

    public void setYzfje(Double yzfje) {
        this.yzfje = yzfje;
    }
    public Double getYxfje() {
        return yxfje;
    }

    public void setYxfje(Double yxfje) {
        this.yxfje = yxfje;
    }
    public Double getHdje() {
        return hdje;
    }

    public void setHdje(Double hdje) {
        this.hdje = hdje;
    }
    public Integer getYjrws() {
        return yjrws;
    }

    public void setYjrws(Integer yjrws) {
        this.yjrws = yjrws;
    }
    public Double getRwjj() {
        return rwjj;
    }

    public void setRwjj(Double rwjj) {
        this.rwjj = rwjj;
    }
    public String getTemp1() {
        return temp1;
    }

    public void setTemp1(String temp1) {
        this.temp1 = temp1;
    }
    public String getTemp2() {
        return temp2;
    }

    public void setTemp2(String temp2) {
        this.temp2 = temp2;
    }
    public String getIshytj() {
        return ishytj;
    }

    public void setIshytj(String ishytj) {
        this.ishytj = ishytj;
    }
    public String getQxsz() {
        return qxsz;
    }

    public void setQxsz(String qxsz) {
        this.qxsz = qxsz;
    }
    public String getLinksz() {
        return linksz;
    }

    public void setLinksz(String linksz) {
        this.linksz = linksz;
    }
    public String getOptionfen() {
        return optionfen;
    }

    public void setOptionfen(String optionfen) {
        this.optionfen = optionfen;
    }
    public String getIfactivity() {
        return ifactivity;
    }

    public void setIfactivity(String ifactivity) {
        this.ifactivity = ifactivity;
    }
    public String getIfagree() {
        return ifagree;
    }

    public void setIfagree(String ifagree) {
        this.ifagree = ifagree;
    }
    public String getIfbi() {
        return ifbi;
    }

    public void setIfbi(String ifbi) {
        this.ifbi = ifbi;
    }
    public String getPartTimeWorker() {
        return partTimeWorker;
    }

    public void setPartTimeWorker(String partTimeWorker) {
        this.partTimeWorker = partTimeWorker;
    }
    public String getOperateAddressPool() {
        return operateAddressPool;
    }

    public void setOperateAddressPool(String operateAddressPool) {
        this.operateAddressPool = operateAddressPool;
    }
    public String getExportAddressPool() {
        return exportAddressPool;
    }

    public void setExportAddressPool(String exportAddressPool) {
        this.exportAddressPool = exportAddressPool;
    }
    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }
    public Integer getManageAccountPermission() {
        return manageAccountPermission;
    }

    public void setManageAccountPermission(Integer manageAccountPermission) {
        this.manageAccountPermission = manageAccountPermission;
    }
    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    @Override
    public String toString() {
        return "TEnterpriseuser{" +
        "Id=" + Id +
        ", email=" + email +
        ", password=" + password +
        ", company=" + company +
        ", companyStatus=" + companyStatus +
        ", registTime=" + registTime +
        ", phone=" + phone +
        ", phonedStatus=" + phonedStatus +
        ", IsPush=" + IsPush +
        ", credit=" + credit +
        ", isdel=" + isdel +
        ", ppzcz=" + ppzcz +
        ", zxfje=" + zxfje +
        ", yzfje=" + yzfje +
        ", yxfje=" + yxfje +
        ", hdje=" + hdje +
        ", yjrws=" + yjrws +
        ", rwjj=" + rwjj +
        ", temp1=" + temp1 +
        ", temp2=" + temp2 +
        ", ishytj=" + ishytj +
        ", qxsz=" + qxsz +
        ", linksz=" + linksz +
        ", optionfen=" + optionfen +
        ", ifactivity=" + ifactivity +
        ", ifagree=" + ifagree +
        ", ifbi=" + ifbi +
        ", partTimeWorker=" + partTimeWorker +
        ", operateAddressPool=" + operateAddressPool +
        ", exportAddressPool=" + exportAddressPool +
        ", mainId=" + mainId +
        ", manageAccountPermission=" + manageAccountPermission +
        ", companyId=" + companyId +
        "}";
    }
}
