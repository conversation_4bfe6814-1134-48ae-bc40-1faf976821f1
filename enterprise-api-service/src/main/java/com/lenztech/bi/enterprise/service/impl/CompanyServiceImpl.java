//package com.lenztech.bi.enterprise.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.lenztech.bi.enterprise.dto.CompanyNameInfoResp;
//import com.lenztech.bi.enterprise.entity.Company;
//import com.lenztech.bi.enterprise.mapper.bienterprise.CompanyMapper;
//import com.lenztech.bi.enterprise.service.ICompanyService;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.apache.commons.compress.utils.Lists;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <p>
// *  服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-08-28
// */
//@Service
//public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, Company> implements ICompanyService {
//
//    @Autowired
//    private CompanyMapper companyMapper;
//
//    /**
//     * 获取公司信息
//     * @return
//     */
//    public CompanyNameInfoResp getCompanyNameInfo() {
//        List<CompanyNameInfoResp.CompanyNameInfo> infoList = Lists.newArrayList();
//
//        LambdaQueryWrapper<Company> companyWrapper = new LambdaQueryWrapper<>();
//        List<Company> companyList = companyMapper.selectList(companyWrapper);
//        for (Company company : companyList) {
//            CompanyNameInfoResp.CompanyNameInfo info = new CompanyNameInfoResp.CompanyNameInfo();
//            info.setProjectId(company.getCompanyId().toString());
//            info.setProjectName(company.getCompanyName());
//            infoList.add(info);
//        }
//        CompanyNameInfoResp resp = new CompanyNameInfoResp();
//        resp.setCompanyNameInfoList(infoList);
//        return resp;
//    }
//}
