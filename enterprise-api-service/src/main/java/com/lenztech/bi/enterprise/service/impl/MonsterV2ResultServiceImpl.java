package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.monsterV2.MonsterBiTargetResp;
import com.lenztech.bi.enterprise.dto.monsterV2.ReportFormsReq;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiImageResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiPatchResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;
import com.lenztech.bi.enterprise.dto.unileverapp.CoordinateDTO;
import com.lenztech.bi.enterprise.entity.MonsterAppImageDetail;
import com.lenztech.bi.enterprise.entity.MonsterAppProductDetail;
import com.lenztech.bi.enterprise.entity.MonsterPatchesResult;
import com.lenztech.bi.enterprise.entity.MonsterStoreResult;
import com.lenztech.bi.enterprise.mapper.MonsterAppImageMapper;
import com.lenztech.bi.enterprise.mapper.MonsterAppProductMapper;
import com.lenztech.bi.enterprise.mapper.MonsterV2PatchesMapper;
import com.lenztech.bi.enterprise.mapper.MonsterV2StoreMapper;
import com.lenztech.bi.enterprise.service.MonsterV2ResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 魔爪sku信息表(MonsterPatchesResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-16 11:33:41
 */
@Service
@Slf4j
public class MonsterV2ResultServiceImpl implements MonsterV2ResultService {

    @Autowired
    private MonsterV2PatchesMapper monsterV2PatchesMapper;

    @Autowired
    private MonsterV2StoreMapper monsterV2StoreMapper;

    @Autowired
    private MonsterAppImageMapper monsterAppImageMapper;

    @Autowired
    private MonsterAppProductMapper monsterAppProductMapper;

    @Override
    public MonsterBiTargetResp getTargetList(ReportFormsReq req) {

        LambdaQueryWrapper<MonsterPatchesResult> patchesWrapper = new LambdaQueryWrapper<>();
        patchesWrapper.in(MonsterPatchesResult::getResponseId, req.getResponseIdList());
        List<MonsterPatchesResult> monsterPatchesLists = monsterV2PatchesMapper.selectList(patchesWrapper);

        LambdaQueryWrapper<MonsterStoreResult> storeWrapper = new LambdaQueryWrapper<>();
        storeWrapper.in(MonsterStoreResult::getResponseId, req.getResponseIdList());
        List<MonsterStoreResult> monsterStoreLists = monsterV2StoreMapper.selectList(storeWrapper);

        MonsterBiTargetResp monsterBiTargetResp = new MonsterBiTargetResp();
        monsterBiTargetResp.setMonsterStoreResultList(monsterStoreLists);
        monsterBiTargetResp.setMonsterPatchesResultList(monsterPatchesLists);

        return monsterBiTargetResp;
    }

    @Override
    public ApiResultDTO getBiTargetList(String responseId) {
        ApiResultDTO apiResultDTO = new ApiResultDTO();
        apiResultDTO.setResponseId(responseId);

        // 查询所有图片信息
        LambdaQueryWrapper<MonsterAppImageDetail> imageWrapper = new LambdaQueryWrapper<>();
        imageWrapper.eq(MonsterAppImageDetail::getResponseId, responseId);
        List<MonsterAppImageDetail> imageDetailList = monsterAppImageMapper.selectList(imageWrapper);

        if (CollectionUtils.isEmpty(imageDetailList)) {
            log.info("【查询BI识别结果集为空!】, responseId:{}", responseId);
            return apiResultDTO;
        }

        // 查询识别出所有SKU信息
        LambdaQueryWrapper<MonsterAppProductDetail> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(MonsterAppProductDetail::getResponseId, responseId);
        List<MonsterAppProductDetail> productDetailList = monsterAppProductMapper.selectList(productWrapper);

        if (CollectionUtils.isEmpty(productDetailList)) {
            log.info("【查询BI识别明细表结果集为空!】, responseId:{}", responseId);
            return apiResultDTO;
        }

        // 根据ImageId分组
        Map<String, List<MonsterAppProductDetail>> productMap = productDetailList.stream().collect(Collectors.groupingBy(MonsterAppProductDetail::getImageId));

        // 构建识别明细
        List<ApiImageResultDTO> apiImageResultList = Lists.newArrayList();
        for (MonsterAppImageDetail imageDetail : imageDetailList) {
            ApiImageResultDTO imageResult = new ApiImageResultDTO();
            imageResult.setImageId(imageDetail.getImageId());
            imageResult.setRemake(imageDetail.getRemake());
            imageResult.setRemakeScore(imageDetail.getRemakeScore());

            ArrayList<ApiPatchResultDTO> patchListList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(productMap.get(imageDetail.getImageId()))) {
                for (MonsterAppProductDetail productDetail : productMap.get(imageDetail.getImageId())) {
                    ApiPatchResultDTO sku = new ApiPatchResultDTO();
                    CoordinateDTO coordinateDTO = new CoordinateDTO();
                    sku.setId(productDetail.getProductId());
                    sku.setSkuCode(productDetail.getCustomCode());
                    sku.setColumn(productDetail.getColumn());
                    sku.setLayer(productDetail.getLayer());
                    sku.setStitchDuplicateStatus(productDetail.getInarge());
                    coordinateDTO.setxMin(productDetail.getXmin());
                    coordinateDTO.setyMin(productDetail.getYmin());
                    coordinateDTO.setxMax(productDetail.getXmax());
                    coordinateDTO.setyMax(productDetail.getYmax());
                    sku.setCoordinate(coordinateDTO);
                    patchListList.add(sku);
                }
            }

            imageResult.setPatches(patchListList);
            apiImageResultList.add(imageResult);
        }
        apiResultDTO.setAiResult(apiImageResultList);

        return apiResultDTO;
    }
}