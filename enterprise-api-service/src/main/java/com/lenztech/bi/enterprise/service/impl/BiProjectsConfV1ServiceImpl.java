package com.lenztech.bi.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.BiProjectsConfV1DTO;
import com.lenztech.bi.enterprise.entity.BiProjectsConfV1;
import com.lenztech.bi.enterprise.mapper.lenzbi.BiProjectsConfV1Mapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2023年01月05日 19:48
 */
@Service
public class BiProjectsConfV1ServiceImpl {

    @Autowired
    private BiProjectsConfV1Mapper biProjectsConfV1Mapper;

    public List<BiProjectsConfV1DTO> getListByOthersArgs(String othersArgs) {
        LambdaQueryWrapper<BiProjectsConfV1> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiProjectsConfV1::getOthersArgs,othersArgs);
        List<BiProjectsConfV1> biProjectsConfV1List = biProjectsConfV1Mapper.selectList(queryWrapper);

        List<BiProjectsConfV1DTO> projectsConfV1DTOS = biProjectsConfV1List.stream().map(item -> {
            BiProjectsConfV1DTO biProjectsConfV1DTO = new BiProjectsConfV1DTO();
            BeanUtils.copyProperties(item, biProjectsConfV1DTO);
            return biProjectsConfV1DTO;
        }).collect(Collectors.toList());

        return projectsConfV1DTOS;

    }
}
