package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.SnowBeerBiResultResp;
import com.lenztech.bi.enterprise.dto.SnowBeerCompleteStatusResp;
import com.lenztech.bi.enterprise.entity.BiResponseRecord;
import com.lenztech.bi.enterprise.entity.XuehuaResults;
import com.lenztech.bi.enterprise.mapper.XuehuaResultsMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.BiResponseRecordMapper;
import com.lenztech.bi.enterprise.service.SnowBeerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2021/1/26 17:12
 **/

@Service
public class SnowBeerServiceImpl implements SnowBeerService {

    @Autowired
    private XuehuaResultsMapper xuehuaResultsMapper;

    @Autowired
    private BiResponseRecordMapper biResponseRecordMapper;

    @Override
    public SnowBeerBiResultResp getResult(String responseId) {
        SnowBeerBiResultResp resp = new SnowBeerBiResultResp();
        List<XuehuaResults> resultList = xuehuaResultsMapper.getResultByResponseId(responseId);
        resp.setSnowBeerResult(resultList);
        return resp;
    }

    /**
     * 获取雪花识别状态
     *
     * @param responseId
     * @return
     */
    @Override
    public SnowBeerCompleteStatusResp getCompleteStatus(String responseId) {
        SnowBeerCompleteStatusResp resp = new SnowBeerCompleteStatusResp();
        List<BiResponseRecord> recordList = biResponseRecordMapper.getRecordList(responseId);
        if (CollectionUtils.isEmpty(recordList)) {
            resp.setCompleteStatus(false);
        } else {
            resp.setCompleteStatus(true);
        }
        return resp;
    }
}
