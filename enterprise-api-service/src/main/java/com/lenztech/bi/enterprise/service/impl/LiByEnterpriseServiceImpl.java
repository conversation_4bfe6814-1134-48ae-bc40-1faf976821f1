package com.lenztech.bi.enterprise.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lenztech.bi.enterprise.comon.StoreTypeEnum;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.liby.*;
import com.lenztech.bi.enterprise.entity.LibyProductDispalyListEntity;
import com.lenztech.bi.enterprise.entity.LibyStoreDetailReportPcEntity;
import com.lenztech.bi.enterprise.entity.LibyStoreReportPcEntity;
import com.lenztech.bi.enterprise.mapper.lenzbi.LibyProductDisplayListMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.LibyStoreDetailReportPcMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.LibyStoreReportPcMapper;
import com.lenztech.bi.enterprise.service.LiByEnterpriseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/9 14:51
 * @since JDK 1.8
 */
@Slf4j
@Service
public class LiByEnterpriseServiceImpl implements LiByEnterpriseService {

    private LibyStoreReportPcMapper libyStoreReportPcMapper;
    private LibyProductDisplayListMapper libyProductDisplayListMapper;
    private LibyStoreDetailReportPcMapper libyStoreDetailReportPcMapper;

    @Autowired
    public LiByEnterpriseServiceImpl(LibyStoreReportPcMapper libyStoreReportPcMapper,
                                     LibyProductDisplayListMapper libyProductDisplayListMapper,
                                     LibyStoreDetailReportPcMapper libyStoreDetailReportPcMapper) {
        this.libyStoreReportPcMapper = libyStoreReportPcMapper;
        this.libyProductDisplayListMapper = libyProductDisplayListMapper;
        this.libyStoreDetailReportPcMapper = libyStoreDetailReportPcMapper;
    }

    @Override
    public ResponseData<CoreStoreDetailRespDTO> listCoreStoreDetail(CoreStoreDetailReqDTO coreStoreDetailReqDTO) {
        Map<String, Object> param = convertReqParamToMap(coreStoreDetailReqDTO);
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities = libyStoreReportPcMapper.selectLibyStoreReportPcEntity(param);
        if (CollectionUtils.isEmpty(libyStoreReportPcEntities)) {
            return ResponseData.failure().msg("暂无数据");
        }
        CoreStoreDetailRespDTO coreStoreDetailRespDTO = new CoreStoreDetailRespDTO();
        List<CoreStoreDetailRespScoreDTO> coreStoreDetailRespScoreDTOS = mergeCoreStoreDetailRespScore(libyStoreReportPcEntities);
        coreStoreDetailRespDTO.setCoreStoreDetailRespScoreDTOS(coreStoreDetailRespScoreDTOS);
        List<CoreStoreDetailRespVisualizeStoreDTO> coreStoreDetailRespVisualizeStoreDTOS = mergeCoreStoreDetailRespVisualizeStoreRadarDTO(libyStoreReportPcEntities);
        coreStoreDetailRespDTO.setCoreStoreDetailRespVisualizeStoreDTOS(coreStoreDetailRespVisualizeStoreDTOS);
        return ResponseData.success(coreStoreDetailRespDTO);
    }

    @Override
    public ResponseData<PointsAreaDTO> listPointsArea(CoreStoreDetailReqDTO coreStoreDetailReqDTO) {
        Map<String, Object> param = convertReqParamToMap(coreStoreDetailReqDTO);
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities = libyStoreReportPcMapper.selectLibyStoreReportPcEntity(param);
        if (CollectionUtils.isEmpty(libyStoreReportPcEntities)) {
            return ResponseData.failure().msg("暂无数据");
        }
        //按大区分组
        List<PointsAreaDetailDTO> pointsAreaDetailDTOS = Lists.newArrayList();
        Set<String> areas = libyStoreReportPcEntities.stream().map(LibyStoreReportPcEntity::getPlace).collect(Collectors.toSet());
        for (String area : areas) {
            PointsAreaDetailDTO pointsAreaDetailDTO = new PointsAreaDetailDTO();
            pointsAreaDetailDTO.setArea(area);
            List<LibyStoreReportPcEntity> libyStoreReportPcEntityList = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getPlace().equals(area)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(libyStoreReportPcEntityList)) {
                pointsAreaDetailDTO.setPointsStoreDetailDTOS(Lists.newArrayList());
                pointsAreaDetailDTOS.add(pointsAreaDetailDTO);
                continue;
            }
            //门店去重
            List<PointsStoreDetailDTO> pointsStoreDetailDTOS = Lists.newArrayList();
            Set<String> storeCodes = libyStoreReportPcEntityList.stream().map(LibyStoreReportPcEntity::getStoreCode).collect(Collectors.toSet());
            for (String storeCode : storeCodes) {
                List<LibyStoreReportPcEntity> libyStoreReportPcEntitiesFilter = libyStoreReportPcEntityList.stream().filter(libyStoreReportPcEntity -> storeCode.equals(libyStoreReportPcEntity.getStoreCode())).collect(Collectors.toList());
                PointsStoreDetailDTO pointsStoreDetailDTO = new PointsStoreDetailDTO();
                pointsStoreDetailDTO.setCity(libyStoreReportPcEntitiesFilter.get(0).getCity());
                pointsStoreDetailDTO.setStoreCode(storeCode);
                pointsStoreDetailDTO.setStoreName(libyStoreReportPcEntitiesFilter.get(0).getStoreName());
                pointsStoreDetailDTOS.add(pointsStoreDetailDTO);
            }
            pointsAreaDetailDTO.setPointsStoreDetailDTOS(pointsStoreDetailDTOS);
            pointsAreaDetailDTOS.add(pointsAreaDetailDTO);
        }
        PointsAreaDTO pointsAreaDTO = new PointsAreaDTO();
        pointsAreaDTO.setPointsAreaDetailDTOS(pointsAreaDetailDTOS);
        return ResponseData.success(pointsAreaDTO);
    }

    @Override
    public ResponseData<StoreScoreDetailRespDTO> getStoreScoreDetail(CoreStoreDetailReqDTO coreStoreDetailReqDTO) {
        Map<String, Object> param = convertReqParamToMap(coreStoreDetailReqDTO);
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities = libyStoreReportPcMapper.selectLibyStoreReportPcEntity(param);
        if (CollectionUtils.isEmpty(libyStoreReportPcEntities)) {
            return ResponseData.failure().msg("暂无数据");
        }
        //取该门店最新的答卷
        LibyStoreReportPcEntity libyStoreReportPcEntity = libyStoreReportPcEntities.get(0);
        StoreScoreDetailRespDTO storeScoreDetailRespDTO = mergeLibyStoreReportPc(libyStoreReportPcEntity);
        //查询规则
        List<LibyProductDispalyListEntity> libyProductDispalyListEntities = libyProductDisplayListMapper.listLibyProductDisplayList(Maps.newHashMap());
        if (CollectionUtils.isEmpty(libyProductDispalyListEntities)) {
            storeScoreDetailRespDTO.setStoreScoreDetailRuleDTOS(Lists.newArrayList());
            return ResponseData.success(storeScoreDetailRespDTO);
        }
        Set<String> rules = libyProductDispalyListEntities.stream().map(LibyProductDispalyListEntity::getRule).collect(Collectors.toSet());
        //TODO 移除不需要的
        rules.remove("主货架远景");
        //查询报表明细
        List<LibyStoreDetailReportPcEntity> libyStoreDetailReportPcEntities = libyStoreDetailReportPcMapper.listLibyStoreDetailReportPc(ImmutableMap.<String, Object>builder()
                .put("responseId", libyStoreReportPcEntity.getResponseId())
                .build());
        List<StoreScoreDetailRuleDTO> storeScoreDetailRuleDTOS = Lists.newArrayList();
        for (String rule : rules) {
            StoreScoreDetailRuleDTO storeScoreDetailRuleDTO = new StoreScoreDetailRuleDTO();
            storeScoreDetailRuleDTO.setRule(rule);
            storeScoreDetailRuleDTO.setStoreScoreDetailRuleExamineDTOS(mergeStoreScoreDetailRuleExamineList(rule, libyStoreDetailReportPcEntities, libyProductDispalyListEntities));
            storeScoreDetailRuleDTOS.add(storeScoreDetailRuleDTO);
        }
        storeScoreDetailRespDTO.setStoreScoreDetailRuleDTOS(storeScoreDetailRuleDTOS);
        return ResponseData.success(storeScoreDetailRespDTO);
    }

    @Override
    public ResponseData<StoreDTO> getStoreList() {
        StoreDTO storeDTO = new StoreDTO();
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities = libyStoreReportPcMapper.selectLibyStoreReportPcEntity(Maps.newHashMap());
        if (CollectionUtils.isEmpty(libyStoreReportPcEntities)) {
            return ResponseData.success(storeDTO);
        }
        Set<String> areas = libyStoreReportPcEntities.stream().map(LibyStoreReportPcEntity::getPlace).collect(Collectors.toSet());
        List<StoreAreaDTO> storeAreaDTOS = Lists.newArrayList();
        for (String area : areas) {
            StoreAreaDTO storeAreaDTO = new StoreAreaDTO();
            storeAreaDTO.setArea(area);
            List<StoreAreaCityDTO> storeAreaCityDTOS = Lists.newArrayList();
            Set<String> cityList = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> area.equals(libyStoreReportPcEntity.getPlace())).map(LibyStoreReportPcEntity::getCity).collect(Collectors.toSet());
            for (String city : cityList) {
                StoreAreaCityDTO storeAreaCityDTO = new StoreAreaCityDTO();
                storeAreaCityDTO.setCity(city);
                List<StoreAreaDetailDTO> storeAreaDetailDTOS = Lists.newArrayList();
                List<LibyStoreReportPcEntity> libyStoreReportPcEntityList = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getPlace().equals(area) && libyStoreReportPcEntity.getCity().equals(city)).collect(Collectors.toList());
                Set<String> storeCodes = libyStoreReportPcEntityList.stream().map(LibyStoreReportPcEntity::getStoreCode).collect(Collectors.toSet());
                for (String storeCode : storeCodes) {
                    StoreAreaDetailDTO storeAreaDetailDTO = new StoreAreaDetailDTO();
                    storeAreaDetailDTO.setStoreCode(storeCode);
                    List<LibyStoreReportPcEntity> libyStoreReportPcEntityListStoreFilter = libyStoreReportPcEntityList.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getStoreCode().equals(storeCode)).collect(Collectors.toList());
                    storeAreaDetailDTO.setStoreName(libyStoreReportPcEntityListStoreFilter.get(0).getStoreName());
                    storeAreaDetailDTOS.add(storeAreaDetailDTO);
                }
                storeAreaCityDTO.setStoreAreaDetailDTOS(storeAreaDetailDTOS);
                storeAreaCityDTOS.add(storeAreaCityDTO);
            }
            storeAreaDTO.setStoreAreaCityDTOS(storeAreaCityDTOS);
            storeAreaDTOS.add(storeAreaDTO);
        }
        storeDTO.setStoreAreaDTOS(storeAreaDTOS);
        return ResponseData.success(storeDTO);
    }

    /**
     * 考核的 + 不考核但是识别到的
     * @param rule
     * @param libyStoreDetailReportPcEntities
     * @param libyProductDispalyListEntities
     * @return
     */
    private List<StoreScoreDetailRuleExamineDTO> mergeStoreScoreDetailRuleExamineList(String rule,
                                                                                      List<LibyStoreDetailReportPcEntity> libyStoreDetailReportPcEntities,
                                                                                      List<LibyProductDispalyListEntity> libyProductDispalyListEntities){
        List<StoreScoreDetailRuleExamineDTO> storeScoreDetailRuleExamineDTOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(libyProductDispalyListEntities)) {
            return storeScoreDetailRuleExamineDTOS;
        }
        //考核的
        List<LibyProductDispalyListEntity> libyProductDispalyListEntityListCheck = libyProductDispalyListEntities.stream().filter(libyProductDispalyListEntity -> rule.equals(libyProductDispalyListEntity.getRule()) && libyProductDispalyListEntity.getIsCheck().intValue() == 1).collect(Collectors.toList());
        List<Integer> productIdCheck = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(libyProductDispalyListEntityListCheck)) {
            productIdCheck = libyProductDispalyListEntityListCheck.stream().map(LibyProductDispalyListEntity::getProductId).collect(Collectors.toList());
        }
        //不考核但识别到的
        List<LibyProductDispalyListEntity> libyProductDispalyListEntityListNotCheck = libyProductDispalyListEntities.stream().filter(libyProductDispalyListEntity -> rule.equals(libyProductDispalyListEntity.getRule()) && libyProductDispalyListEntity.getIsCheck().intValue() == 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(libyStoreDetailReportPcEntities)) {
            //rule的识别到的
            List<LibyStoreDetailReportPcEntity> libyStoreDetailReportPcEntityList = libyStoreDetailReportPcEntities.stream().filter(libyStoreDetailReportPcEntity -> rule.equals(libyStoreDetailReportPcEntity.getRule())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(libyProductDispalyListEntityListNotCheck) && CollectionUtils.isNotEmpty(libyStoreDetailReportPcEntityList)) {
                List<Integer> productIdNotCheck = libyProductDispalyListEntityListNotCheck.stream().map(LibyProductDispalyListEntity::getProductId).collect(Collectors.toList());
                List<String> productIds = libyStoreDetailReportPcEntityList.stream().map(LibyStoreDetailReportPcEntity::getProductId).collect(Collectors.toList());
                List<Integer> productIdsInteger = productIds.stream().map(pid -> Integer.parseInt(pid)).collect(Collectors.toList());
                //求交集
                productIdNotCheck.retainAll(productIdsInteger);
                if (CollectionUtils.isNotEmpty(productIdNotCheck)) {
                    productIdCheck.addAll(productIdNotCheck);
                }
            }
        }
        if (CollectionUtils.isEmpty(productIdCheck)) {
            return storeScoreDetailRuleExamineDTOS;
        }
        //组装list
        for (LibyProductDispalyListEntity libyProductDispalyListEntity : libyProductDispalyListEntities) {
            if (!productIdCheck.contains(libyProductDispalyListEntity.getProductId())) {
                continue;
            }
            StoreScoreDetailRuleExamineDTO storeScoreDetailRuleExamineDTO = new StoreScoreDetailRuleExamineDTO();
            storeScoreDetailRuleExamineDTO.setProductId(libyProductDispalyListEntity.getProductId());
            storeScoreDetailRuleExamineDTO.setExamineItem(libyProductDispalyListEntity.getDisplayName());
            //几个
            long value = 0;
            if (CollectionUtils.isNotEmpty(libyStoreDetailReportPcEntities)) {
                value = libyStoreDetailReportPcEntities.stream().filter(libyStoreDetailReportPcEntity -> StringUtils.isNotBlank(libyStoreDetailReportPcEntity.getProductId()) && null != libyProductDispalyListEntity.getProductId() && libyProductDispalyListEntity.getProductId().intValue() == Integer.parseInt(libyStoreDetailReportPcEntity.getProductId())).count();
            }
            storeScoreDetailRuleExamineDTO.setValue(value);
            storeScoreDetailRuleExamineDTOS.add(storeScoreDetailRuleExamineDTO);
        }
        return storeScoreDetailRuleExamineDTOS;
    }

    private StoreScoreDetailRespDTO mergeLibyStoreReportPc(LibyStoreReportPcEntity libyStoreReportPcEntity){
        StoreScoreDetailRespDTO storeScoreDetailRespDTO = new StoreScoreDetailRespDTO();
        storeScoreDetailRespDTO.setResponseId(libyStoreReportPcEntity.getResponseId());
        storeScoreDetailRespDTO.setStoreType(libyStoreReportPcEntity.getStoreType());
        storeScoreDetailRespDTO.setVisitTime(libyStoreReportPcEntity.getStartTime());
        storeScoreDetailRespDTO.setLiByDisplayForm(libyStoreReportPcEntity.getLibyDuixing());
        storeScoreDetailRespDTO.setLiByDisplayNum(libyStoreReportPcEntity.getLibyDuiNum());
        StoreScoreDetailExamineItemDTO storeScoreDetailExamineItemDTO = new StoreScoreDetailExamineItemDTO();
        storeScoreDetailExamineItemDTO.setDiDuiScore(libyStoreReportPcEntity.getDiduiDefen().intValue());
        storeScoreDetailExamineItemDTO.setGuaTiaoScore(libyStoreReportPcEntity.getGuatiaoDefen().intValue());
        storeScoreDetailExamineItemDTO.setLianDaiScore(libyStoreReportPcEntity.getLiandaiDefen().intValue());
        storeScoreDetailExamineItemDTO.setQuGeScore(libyStoreReportPcEntity.getQugeDefen().intValue());
        storeScoreDetailExamineItemDTO.setWuLiaoScore(libyStoreReportPcEntity.getWuliaoDefen().intValue());
        storeScoreDetailRespDTO.setStoreScoreDetailExamineItemDTO(storeScoreDetailExamineItemDTO);
        return storeScoreDetailRespDTO;
    }

    private List<CoreStoreDetailRespScoreDTO> mergeCoreStoreDetailRespScore(List<LibyStoreReportPcEntity> libyStoreReportPcEntities) {
        List<CoreStoreDetailRespScoreDTO> coreStoreDetailRespScoreDTOS = Lists.newArrayList();
        //0-70分
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities1 = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getTotalScore().intValue() >= 0 && libyStoreReportPcEntity.getTotalScore().intValue() <= 70).collect(Collectors.toList());
        //50-70分
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities2 = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getTotalScore().intValue() >= 50 && libyStoreReportPcEntity.getTotalScore().intValue() <= 70).collect(Collectors.toList());
        //0-49分
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities3 = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getTotalScore().intValue() >= 0 && libyStoreReportPcEntity.getTotalScore().intValue() < 50).collect(Collectors.toList());
        // 地堆陈设
        long diduiTotal = libyStoreReportPcEntities1.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getDiduiDefen().intValue() < 20).count();
        long diduiQualified = libyStoreReportPcEntities2.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getDiduiDefen().intValue() < 20).count();
        long diduiNotQualified = libyStoreReportPcEntities3.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getDiduiDefen().intValue() < 20).count();
        coreStoreDetailRespScoreDTOS.add(mergeCoreStoreDetailRespScoreDTO("地堆陈设", diduiTotal, diduiQualified, diduiNotQualified));
        //连带
        long lianDaiTotal = libyStoreReportPcEntities1.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getLiandaiDefen().intValue() < 5).count();
        long lianDaiQualified = libyStoreReportPcEntities2.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getLiandaiDefen().intValue() < 5).count();
        long lianDaiNotQualified = libyStoreReportPcEntities3.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getLiandaiDefen().intValue() < 5).count();
        coreStoreDetailRespScoreDTOS.add(mergeCoreStoreDetailRespScoreDTO("连带销售", lianDaiTotal, lianDaiQualified, lianDaiNotQualified));
        //物料
        long wuLiaoTotal = libyStoreReportPcEntities1.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getWuliaoDefen().intValue() < 25).count();
        long wuLiaoQualified = libyStoreReportPcEntities2.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getWuliaoDefen().intValue() < 25).count();
        long wuLiaoNotQualified = libyStoreReportPcEntities3.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getWuliaoDefen().intValue() < 25).count();
        coreStoreDetailRespScoreDTOS.add(mergeCoreStoreDetailRespScoreDTO("物料有无", wuLiaoTotal, wuLiaoQualified, wuLiaoNotQualified));
        //区隔
        long quGeTotal = libyStoreReportPcEntities1.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getQugeDefen().intValue() < 10).count();
        long quGeQualified = libyStoreReportPcEntities2.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getQugeDefen().intValue() < 10).count();
        long quGeNotQualified = libyStoreReportPcEntities3.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getQugeDefen().intValue() < 10).count();
        coreStoreDetailRespScoreDTOS.add(mergeCoreStoreDetailRespScoreDTO("双品牌区隔", quGeTotal, quGeQualified, quGeNotQualified));
        //挂条
        long guaTiaoTotal = libyStoreReportPcEntities1.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getGuatiaoDefen().intValue() < 10).count();
        long guaTiaoQualified = libyStoreReportPcEntities2.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getGuatiaoDefen().intValue() < 10).count();
        long guaTiaoNotQualified = libyStoreReportPcEntities3.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getGuatiaoDefen().intValue() < 10).count();
        coreStoreDetailRespScoreDTOS.add(mergeCoreStoreDetailRespScoreDTO("挂条挂袋", guaTiaoTotal, guaTiaoQualified, guaTiaoNotQualified));
        return coreStoreDetailRespScoreDTOS;
    }

    private CoreStoreDetailRespScoreDTO mergeCoreStoreDetailRespScoreDTO(String examineItem, long total, long qualified, long notQualified) {
        CoreStoreDetailRespScoreDTO coreStoreDetailRespScoreDTO1 = new CoreStoreDetailRespScoreDTO();
        coreStoreDetailRespScoreDTO1.setDeductScoreTotal(total);
        coreStoreDetailRespScoreDTO1.setDeductScoreQualified(qualified);
        coreStoreDetailRespScoreDTO1.setDeductScoreNotQualified(notQualified);
        coreStoreDetailRespScoreDTO1.setExamineItem(examineItem);
        return coreStoreDetailRespScoreDTO1;
    }

    private List<CoreStoreDetailRespVisualizeStoreDTO> mergeCoreStoreDetailRespVisualizeStoreRadarDTO(List<LibyStoreReportPcEntity> libyStoreReportPcEntities) {
        List<CoreStoreDetailRespVisualizeStoreDTO> coreStoreDetailRespVisualizeStoreDTOS = Lists.newArrayList();
        coreStoreDetailRespVisualizeStoreDTOS.add(mergeCoreStoreDetailRespVisualizeStoreRadarDTOByStoreType(libyStoreReportPcEntities, "0"));
        coreStoreDetailRespVisualizeStoreDTOS.add(mergeCoreStoreDetailRespVisualizeStoreRadarDTOByStoreType(libyStoreReportPcEntities, "1"));
        coreStoreDetailRespVisualizeStoreDTOS.add(mergeCoreStoreDetailRespVisualizeStoreRadarDTOByStoreType(libyStoreReportPcEntities, "2"));
        return coreStoreDetailRespVisualizeStoreDTOS;
    }

    private CoreStoreDetailRespVisualizeStoreDTO mergeCoreStoreDetailRespVisualizeStoreRadarDTOByStoreType(List<LibyStoreReportPcEntity> libyStoreReportPcEntities, String storeType) {
        CoreStoreDetailRespVisualizeStoreDTO coreStoreDetailRespVisualizeStoreDTO = new CoreStoreDetailRespVisualizeStoreDTO();
        coreStoreDetailRespVisualizeStoreDTO.setVisualizeStoreType(StoreTypeEnum.getNameCnByNameEn(storeType));
        //0-70分
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities1 = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getTotalScore().intValue() >= 0 && libyStoreReportPcEntity.getTotalScore().intValue() <= 70 && storeType.equals(libyStoreReportPcEntity.getStoreType())).collect(Collectors.toList());
        //50-70分
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities2 = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getTotalScore().intValue() >= 50 && libyStoreReportPcEntity.getTotalScore().intValue() <= 70 && storeType.equals(libyStoreReportPcEntity.getStoreType())).collect(Collectors.toList());
        //0-49分
        List<LibyStoreReportPcEntity> libyStoreReportPcEntities3 = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getTotalScore().intValue() >= 0 && libyStoreReportPcEntity.getTotalScore().intValue() < 50 && storeType.equals(libyStoreReportPcEntity.getStoreType())).collect(Collectors.toList());
        List<CoreStoreDetailRespVisualizeStoreRadarDTO> coreStoreDetailRespVisualizeStoreRadarDTOS = Lists.newArrayList();
        //total
        coreStoreDetailRespVisualizeStoreRadarDTOS.add(getCoreStoreDetailRespVisualizeStoreRadarDTO("Total", libyStoreReportPcEntities1));
        //合格门店
        coreStoreDetailRespVisualizeStoreRadarDTOS.add(getCoreStoreDetailRespVisualizeStoreRadarDTO("合格门店", libyStoreReportPcEntities2));
        //不合格门店
        coreStoreDetailRespVisualizeStoreRadarDTOS.add(getCoreStoreDetailRespVisualizeStoreRadarDTO("不合格门店", libyStoreReportPcEntities3));
        coreStoreDetailRespVisualizeStoreDTO.setCoreStoreDetailRespVisualizeStoreRadarDTOS(coreStoreDetailRespVisualizeStoreRadarDTOS);
        return coreStoreDetailRespVisualizeStoreDTO;
    }

    private CoreStoreDetailRespVisualizeStoreRadarDTO getCoreStoreDetailRespVisualizeStoreRadarDTO(String name, List<LibyStoreReportPcEntity> libyStoreReportPcEntities) {
        CoreStoreDetailRespVisualizeStoreRadarDTO coreStoreDetailRespVisualizeStoreRadarDTO = new CoreStoreDetailRespVisualizeStoreRadarDTO();
        coreStoreDetailRespVisualizeStoreRadarDTO.setName(name);
        long didui = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getDiduiDefen().intValue() < 20).count();
        long lianDai = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getLiandaiDefen().intValue() < 5).count();
        long wuLiao = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getWuliaoDefen().intValue() < 25).count();
        long quGe = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getQugeDefen().intValue() < 10).count();
        long guaTiao = libyStoreReportPcEntities.stream().filter(libyStoreReportPcEntity -> libyStoreReportPcEntity.getGuatiaoDefen().intValue() < 10).count();
        coreStoreDetailRespVisualizeStoreRadarDTO.setDiDui(didui);
        coreStoreDetailRespVisualizeStoreRadarDTO.setLianDai(lianDai);
        coreStoreDetailRespVisualizeStoreRadarDTO.setWuLiao(wuLiao);
        coreStoreDetailRespVisualizeStoreRadarDTO.setQuGe(quGe);
        coreStoreDetailRespVisualizeStoreRadarDTO.setGuaTiao(guaTiao);
        return coreStoreDetailRespVisualizeStoreRadarDTO;
    }

    private Map<String, Object> convertReqParamToMap(CoreStoreDetailReqDTO coreStoreDetailReqDTO) {
        Map<String, Object> param = Maps.newHashMap();
        if (null != coreStoreDetailReqDTO) {
            if (StringUtils.isNotBlank(coreStoreDetailReqDTO.getCity())) {
                param.put("city", coreStoreDetailReqDTO.getCity());
            }
            if (null != coreStoreDetailReqDTO.getStartTime()) {
                param.put("startTime", coreStoreDetailReqDTO.getStartTime());
            }
            if (null != coreStoreDetailReqDTO.getEndTime()) {
                param.put("endTime", coreStoreDetailReqDTO.getEndTime());
            }
            if (StringUtils.isNotBlank(coreStoreDetailReqDTO.getStoreType())) {
                param.put("storeType", coreStoreDetailReqDTO.getStoreType());
            }
            if (StringUtils.isNotBlank(coreStoreDetailReqDTO.getStoreCode())) {
                param.put("storeCode", coreStoreDetailReqDTO.getStoreCode());
            }
        }
        return param;
    }


}
