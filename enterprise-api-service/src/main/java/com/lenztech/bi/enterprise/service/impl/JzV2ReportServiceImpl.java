package com.lenztech.bi.enterprise.service.impl;

import com.lenztech.bi.enterprise.dto.jzv2.JzV2BiTargetResp;
import com.lenztech.bi.enterprise.dto.jzv2.JzDisplay;
import com.lenztech.bi.enterprise.dto.jzv2.JzDisplaySku;
import com.lenztech.bi.enterprise.mapper.lenzbi.JzV2DisplayMapper;
import com.lenztech.bi.enterprise.mapper.lenzbi.JzV2DisplaySkuMapper;
import com.lenztech.bi.enterprise.service.JzV2ReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 江中二期查询BI识别指标结果
 *
 * <AUTHOR>
 * @date 2021-10-11 14:58
 */
@Service
public class JzV2ReportServiceImpl implements JzV2ReportService {

    public static final Logger logger = LoggerFactory.getLogger(JzV2ReportServiceImpl.class);

    @Autowired
    private JzV2DisplayMapper jzV2DisplayMapper;

    @Autowired
    private JzV2DisplaySkuMapper jzV2DisplaySkuMapper;


    /**
     * 根据答卷id查询bi结果
     * @param responseId
     * @return
     */
    @Override
    public JzV2BiTargetResp getBiTargetList(String responseId) {
        logger.info("【江中二期】查询bi结果集，responseId:" + responseId);
        JzV2BiTargetResp jzV2BiTargetResp = new JzV2BiTargetResp();
        try {
            List<JzDisplay> imageList = jzV2DisplayMapper.getImageList(responseId);
            List<JzDisplaySku> sceneList = jzV2DisplaySkuMapper.getSceneList(responseId);
            jzV2BiTargetResp.setResponseId(responseId);
            jzV2BiTargetResp.setJzV2Display(imageList);
            jzV2BiTargetResp.setJzV2DisplaySku(sceneList);
        }catch (Exception e){
            logger.info("【江中二期】查询bi结果集异常！", e);
        }
        return jzV2BiTargetResp;
    }
}
