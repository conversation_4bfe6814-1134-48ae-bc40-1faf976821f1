package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneInfo;
import com.lenztech.bi.enterprise.entity.BnhdPicLinks;
import com.lenztech.bi.enterprise.entity.BnhdPosmsResult;
import com.lenztech.bi.enterprise.entity.BnhdStoreRecord;
import com.lenztech.bi.enterprise.mapper.BnhdPatchesResultMapper;
import com.lenztech.bi.enterprise.mapper.BnhdPicLinksMapper;
import com.lenztech.bi.enterprise.mapper.BnhdPosmsResultMapper;
import com.lenztech.bi.enterprise.mapper.BnhdStoreRecordMapper;
import com.lenztech.bi.enterprise.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/02/24
 * Time: 15:38
 * 类功能: 百年糊涂poc相关接口逻辑
 */
@Service
public class BnhdReportService {

//    @Autowired
//    private TResponseMapper responseMapper;

//    @Autowired
//    private TTasklaunchMapper tasklaunchMapper;

    @Autowired
    private BnhdPatchesResultMapper bnhdPatchesResultMapper;

    @Autowired
    private BnhdStoreRecordMapper bnhdStoreRecordMapper;

    @Autowired
    private BnhdPicLinksMapper bnhdPicLinksMapper;

    @Autowired
    private BnhdPosmsResultMapper bnhdPosmsResultMapper;

    /**
     * 产品 银世纪(铁罐)
     */
    private static final String PRODUCT_SILVER_CENTURY = "银世纪(铁罐)";

    /**
     * 产品 超值装(纸袋)
     */
    private static final String PRODUCT_VALUE_PACK = "超值装(纸袋)";

    /**
     * POSM 广告牌
     */
    private static final String POSM_BILLBOARD= "广告牌";

    /**
     * POSM 价格牌
     */
    private static final String POSM_PRICE_TAG = "价格牌";

    /**
     * 场景 货架
     */
    private static final String SCENE_SHELF = "货架";

    /**
     * 场景 端架
     */
    private static final String SCENE_END_FRAME = "端架";

    /**
     * 场景 酒柜
     */
    private static final String SCENE_WINE_CABINET = "酒柜";

    /**
     * 场景 割箱
     */
    private static final String SCENE_CUT_BOX = "割箱";

    /**
     * 场景 堆头
     */
    private static final String SCENE_DUITOU = "堆头";

    /**
     * 分销 否
     */
    private static final String DISTRIBUTION_YES = "Y";

    /**
     * 分销 否
     */
    private static final String DISTRIBUTION_NO = "N";

    /**
     * 不完整标记
     */
    private static final String INCOMPLETE_MARK = "(检测到不完整货架)";

    /**
     * 获取百年糊涂门店信息
     * @param responseId
     * @return
     */
    public BnhdStoreRecord getStoreinfo(String responseId){

        LambdaQueryWrapper<BnhdStoreRecord> bnhdStoreRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bnhdStoreRecordLambdaQueryWrapper.eq(BnhdStoreRecord::getResponseId, responseId);
        BnhdStoreRecord bnhdStoreRecord = bnhdStoreRecordMapper.selectOne(bnhdStoreRecordLambdaQueryWrapper);

        return bnhdStoreRecord;
    }

    /**
     * 获取posm结果
     * @param responseId
     * @return
     */
    public List<BnhdPosmsResult> getPosmResult(String responseId){

        LambdaQueryWrapper<BnhdPosmsResult> bnhdPosmsResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bnhdPosmsResultLambdaQueryWrapper.eq(BnhdPosmsResult::getResponseId, responseId);
        List<BnhdPosmsResult> bnhdPosmsResult = bnhdPosmsResultMapper.selectList(bnhdPosmsResultLambdaQueryWrapper);

        return bnhdPosmsResult;
    }

    /**
     * 获取百年糊涂识别场景及其产品信息
     * @param responseId
     * @return
     */
    public List<RecognizeSceneInfo> getRecognizeSceneProducts(String responseId){

        //是否不完整货架
        boolean isIncomplete = isIncomplete(responseId);
        List<RecognizeSceneInfo> recognizeSceneInfoList = new ArrayList<>();
        LambdaQueryWrapper<BnhdPicLinks> bnhdPicLinksLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bnhdPicLinksLambdaQueryWrapper.eq(BnhdPicLinks::getResponseId, responseId);
        List<BnhdPicLinks> bnhdPicLinksList = bnhdPicLinksMapper.selectList(bnhdPicLinksLambdaQueryWrapper);
        Map<String, String> stringBnhdPicLinksMap = bnhdPicLinksList.stream().collect(Collectors.toMap(BnhdPicLinks::getScene, BnhdPicLinks::getPicUrl));
        if (stringBnhdPicLinksMap != null && stringBnhdPicLinksMap.size() > 0){
            List<String> sceneList = getSceneList();
            for (String scene: sceneList){
                RecognizeSceneInfo recognizeSceneInfo = getRecognizeSceneInfo(responseId, scene, stringBnhdPicLinksMap);
                if (SCENE_SHELF.equals(recognizeSceneInfo.getScene()) && isIncomplete){
                    recognizeSceneInfo.setScene(recognizeSceneInfo.getScene() + INCOMPLETE_MARK);
                }
                if (recognizeSceneInfo.getProductPriceInfos() != null && recognizeSceneInfo.getProductPriceInfos().size() > 0){
                    recognizeSceneInfoList.add(recognizeSceneInfo);
                }

            }
        }

        return recognizeSceneInfoList;
    }

    /**
     * 获取百年糊涂识别产品信息
     * @param responseId
     * @return
     */
    public List<ProductPriceInfo> getRecognizeProductsInfo(String responseId){

        Map<String, ProductPriceInfo> productPriceInfoMap = initProductPriceInfo();
        List<ProductPriceInfo> productPriceInfoList = bnhdPatchesResultMapper.getProductPriceInfo(responseId);
        for (ProductPriceInfo productPriceInfo : productPriceInfoList){
            ProductPriceInfo productPriceInfo1 = productPriceInfoMap.get(productPriceInfo.getProductName());
            if (productPriceInfo1 != null){
                productPriceInfo1.setProductPrice(removeRepeat(productPriceInfo.getProductPrice()));
                productPriceInfo1.setCurrentUnit(productPriceInfo.getCurrentUnit());
                productPriceInfo1.setTotalAmount(productPriceInfo.getTotalAmount());
                productPriceInfo1.setIfDist((Integer.valueOf(productPriceInfo.getTotalAmount()) > 0) ? 1 : 0);
            }

        }
        return productPriceInfoMap.values().stream().collect(Collectors.toList());
    }

    /**
     * 获取百年糊涂识别场景信息
     * @return
     */
    public RecognizeSceneInfo getRecognizeSceneInfo(String responseId, String scene, Map<String, String> stringBnhdPicLinksMap){

        RecognizeSceneInfo recognizeSceneInfo = new RecognizeSceneInfo();
        recognizeSceneInfo.setScene(scene);
        recognizeSceneInfo.setPicUrl(stringBnhdPicLinksMap.get(scene));
        List<ProductPriceInfo> productPriceInfoList = bnhdPatchesResultMapper.getRecognizeSceneProducts(responseId, scene);
        for (ProductPriceInfo productPriceInfo: productPriceInfoList){
            if (StringUtil.isNotBlank(productPriceInfo.getCurrentUnit())){
                productPriceInfo.setCurrentUnit(removeRepeat(productPriceInfo.getCurrentUnit()));
            }
        }
        if (productPriceInfoList != null && productPriceInfoList.size() > 0){
            recognizeSceneInfo.setProductPriceInfos(productPriceInfoList);
        }

        return recognizeSceneInfo;
    }

    /**
     * 封装场景集合, 要求按 货架、端架、酒柜、割箱、堆头 次序
     * @return
     */
    public List<String> getSceneList(){

        List<String> sceneList = new ArrayList<>();
        sceneList.add(SCENE_SHELF);
        sceneList.add(SCENE_END_FRAME);
        sceneList.add(SCENE_WINE_CABINET);
        sceneList.add(SCENE_CUT_BOX);
        sceneList.add(SCENE_DUITOU);
        return sceneList;
    }

    /**
     * 获取是否完整货架 true完整
     * @return
     */
    public boolean isIncomplete(String responseId){

        //默认非不完整
        boolean isIncomplete = false;
        BnhdStoreRecord bnhdStoreRecord = getStoreinfo(responseId);
        if (bnhdStoreRecord != null && (1 == bnhdStoreRecord.getIfShelfFull())){
            isIncomplete = true;
        }
        return isIncomplete;
    }

    /**
     * 去除重复价签 层数
     * @return
     */
    public String removeRepeat(String sourceData){

        sourceData = sourceData.replaceAll("￥", "");
        String[] sourceDatas = sourceData.split(",");
        Set sourceDataSet = new HashSet();
        for (String data: sourceDatas){
            if (StringUtils.isNotBlank(data) && (sourceDataSet.size() < 7)){
                sourceDataSet.add(data);
            }
        }
        String resultData = String.join(",", sourceDataSet);
        return resultData;

    }

    /**
     * 初始化产品信息
     * @return
     */
    public Map<String, ProductPriceInfo> initProductPriceInfo(){

        Map<String, ProductPriceInfo> productPriceInfoMap = new HashMap<>();
        ProductPriceInfo productPriceInfo = new ProductPriceInfo();
        productPriceInfo.setIfDist(0);
        productPriceInfo.setProductName(PRODUCT_SILVER_CENTURY);
        productPriceInfoMap.put(PRODUCT_SILVER_CENTURY, productPriceInfo);

        ProductPriceInfo productPriceInfo1 = new ProductPriceInfo();
        productPriceInfo1.setIfDist(0);
        productPriceInfo1.setProductName(PRODUCT_VALUE_PACK);
        productPriceInfoMap.put(PRODUCT_VALUE_PACK, productPriceInfo1);

        return productPriceInfoMap;
    }

}
