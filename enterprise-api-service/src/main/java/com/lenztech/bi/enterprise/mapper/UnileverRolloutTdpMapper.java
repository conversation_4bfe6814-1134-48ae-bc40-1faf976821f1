package com.lenztech.bi.enterprise.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.UnileverRolloutTdp;
import feign.Param;

import java.util.List;

@DS("lenzbi")
public interface UnileverRolloutTdpMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(UnileverRolloutTdp record);

    int insertSelective(UnileverRolloutTdp record);

    UnileverRolloutTdp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(UnileverRolloutTdp record);

    List<UnileverRolloutTdp> getByResponseId(@Param("responseId") String responseId);
}