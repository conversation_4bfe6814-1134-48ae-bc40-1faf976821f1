package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("chinashopdemo_kpi")
public class ChinashopdemoKpi implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问卷ID
     */
    private String responseId;

    /**
     * PSKU上架率(小数)
     */
    @TableField("PSKUdist")
    private Float PSKUdist;

    /**
     * 货架缺货率(小数)
     */
    @TableField("shortageRate")
    private Float shortageRate;

    /**
     * 陈列是否合格(1=是, 0=否)
     */
    @TableField("ifDisplayValid")
    private Integer ifDisplayValid;

    /**
     * 价格错误数量
     */
    @TableField("wrongPricingCount")
    private Integer wrongPricingCount;

    @TableField("stitchURL")
    private String stitchURL;


}
