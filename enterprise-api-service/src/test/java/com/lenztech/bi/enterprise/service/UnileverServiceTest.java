package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.service.enterprise.UnileverServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;

/**
 * UnileverService测试类
 */
@SpringBootTest
public class UnileverServiceTest {

    @Autowired
    private UnileverServiceImpl unileverService;

    /**
     * 测试处理昨天的数据
     */
    @Test
    public void testProcessYesterdayData() {
        try {
            unileverService.processUnileverData(null);
            System.out.println("处理昨天的数据测试完成");
        } catch (Exception e) {
            System.err.println("处理昨天的数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试处理指定执行ID的数据
     */
    @Test
    public void testProcessSpecificData() {
        try {
            // 这里需要替换为实际存在的taskAddressId
            unileverService.processUnileverData(Collections.singletonList(3810700150230943844L));
            System.out.println("处理指定执行ID的数据测试完成");
        } catch (Exception e) {
            System.err.println("处理指定执行ID的数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试空列表处理
     */
    @Test
    public void testProcessEmptyList() {
        try {
            unileverService.processUnileverData(Collections.emptyList());
            System.out.println("处理空列表测试完成");
        } catch (Exception e) {
            System.err.println("处理空列表测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 