package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.service.enterprise.OptimusServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;

@SpringBootTest
public class OptimusServiceTest {

    @Autowired
    private OptimusServiceImpl optimusService;

    /**
     * 只处理指定执行id的记录，验证主流程
     */
    @Test
    public void testProcessAndSaveByTaskAddressId() {
//        optimusService.calc(Collections.singletonList(3783245413604851712L));
        optimusService.calc(Arrays.asList(3783245413604851712L, 3783245516687409152L));
//        optimusService.calcAndSaveBrandLevelDisplayCount(3783245413604851712L);
//        optimusService.calcAndSaveCategoryLevelDisplayCount(3783245413604851712L);
    }
} 